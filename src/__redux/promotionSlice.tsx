import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PromotionObject, PromotionConfig, PromotionFilterParam } from "../app/Types/promotion-types";


interface PromotionState {
  promotions: PromotionObject[];
  loading: boolean;
  page: number;
  totalPages: number;
  limit: number;
  open: boolean;
  formData: PromotionObject | null;
  isAddMode: boolean;
  refresh: boolean;
  searchText: string;
  config: PromotionConfig;
  filters: PromotionFilterParam;
  showFilters: boolean;
  confirmDialogOpen: boolean;
  confirmDialogLoading: boolean;
  confirmDialogError: string | null;
  confirmDialogSuccess: boolean;
  sortBy: string | null;
  sortOrder: "asc" | "desc" | null;
  tabData: Record<string, any>;
}

const initialState: PromotionState = {
  promotions: [],
  loading: false,
  page: 1,
  totalPages: 1,
  limit: 100,
  open: false,
  formData: null,
  isAddMode: false,
  refresh: false,
  searchText: "",
  config: {
    fiscalPeriodNumber: [],
    fiscalWeekNumber: [],
    fiscalQuarterNumber: [],
    fiscalYear: [],
  },
  filters: {
    fiscalPeriodNumber: null,
    fiscalWeekNumber: null,
    fiscalQuarterNumber: null,
    fiscalYear: null,
  },
  showFilters: false,
  confirmDialogOpen: false,
  confirmDialogLoading: false,
  confirmDialogError: null,
  confirmDialogSuccess: false,
  sortBy: null,
  sortOrder: null,
  tabData: {},
};

const promotionSlice = createSlice({
  name: "promotion",
  initialState,
  reducers: {
    setPromotions: (state, action: PayloadAction<PromotionObject[]>) => {
      state.promotions = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
    },
    setOpen: (state, action: PayloadAction<boolean>) => {
      state.open = action.payload;
    },
    setFormData: (state, action: PayloadAction<PromotionObject | null>) => {
      state.formData = action.payload;
    },
    setIsAddMode: (state, action: PayloadAction<boolean>) => {
      state.isAddMode = action.payload;
    },
    setRefresh: (state, action: PayloadAction<boolean>) => {
      state.refresh = action.payload;
    },
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
    },
    setConfig: (state, action: PayloadAction<PromotionConfig>) => {
      state.config = action.payload;
    },
    setFilters: (state, action: PayloadAction<PromotionFilterParam>) => {
      state.filters = action.payload;
    },
    setShowFilters: (state, action: PayloadAction<boolean>) => {
      state.showFilters = action.payload;
    },
    setConfirmDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogOpen = action.payload;
    },
    setConfirmDialogLoading: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogLoading = action.payload;
    },
    setConfirmDialogError: (state, action: PayloadAction<string | null>) => {
      state.confirmDialogError = action.payload;
    },
    setConfirmDialogSuccess: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogSuccess = action.payload;
    },
    setSortBy: (state, action: PayloadAction<string | null>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<"asc" | "desc" | null>) => {
      state.sortOrder = action.payload;
    },
    setTabData: (state, action: PayloadAction<Record<string, any>>) => {
      state.tabData = action.payload;
    },
  },
});

export const {
  setPromotions,
  setLoading,
  setPage,
  setTotalPages,
  setLimit,
  setOpen,
  setFormData,
  setIsAddMode,
  setRefresh,
  setSearchText,
  setConfig,
  setFilters,
  setShowFilters,
  setConfirmDialogOpen,
  setConfirmDialogLoading,
  setConfirmDialogError,
  setConfirmDialogSuccess,
  setSortBy,
  setSortOrder,
  setTabData,
} = promotionSlice.actions;

export default promotionSlice.reducer;
