import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface AuditSqlQueryItem {
  query: string;
  params: Record<string, unknown>;
}

export interface SqlQueryObject {
  query: string;
  params: Record<string, unknown>;
  action?: "UPDATE" | "INSERT" | "DELETE" | string;
  tableName?: string;
  primaryKey?: {
    column: string;
    value: string | number;
  };
}

export interface AuditLogObject {
  Id?: string;
  id?: string;
  userId?: string | number;
  name?: string;
  roles?: string[] | string;
  email?: string;
  ipAddress?: string;
  tenantId?: string;
  apiEndpoint?: string;
  requestBody?: Record<string, unknown>;
  result?: boolean | null;
  timestamp?: Date | string;
  sqlQuery?: AuditSqlQueryItem[] | SqlQueryObject | string;
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
}

export interface LogsFilterParam {
  searchTerm: string | null;
}

export interface AuditLogState {
  auditLogs: AuditLogObject[];
  loading: boolean;
  pageSize: number;
  totalPages: number;
  currentPage: number;
  filters: LogsFilterParam;
  totalCount: number;
}

const initialState: AuditLogState = {
  auditLogs: [],
  loading: false,
  pageSize: 15,
  totalPages: 1,
  currentPage: 1,
  totalCount: 0,
  filters: {
    searchTerm: null,
  },
};

const auditLogSlice = createSlice({
  name: "auditLog",
  initialState,
  reducers: {
    setAuditLogs: (state, action: PayloadAction<AuditLogObject[]>) => {
      state.auditLogs = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setCurrentPages: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<LogsFilterParam>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setTotalCount: (state, action: PayloadAction<number>) => {
      state.totalCount = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
  },
});

export const { setAuditLogs, setLoading, setPage, setTotalPages, setCurrentPages, setFilters, setTotalCount, setCurrentPage } = auditLogSlice.actions;

export default auditLogSlice.reducer;
