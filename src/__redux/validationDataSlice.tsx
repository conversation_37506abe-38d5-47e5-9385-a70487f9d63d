import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VDConfig } from "../app/api/validationDataAPi";


export interface ValidRecordItem {
	site_number: number;
	store: string;
	business_date: string | null;
	store_name: string | null;
	gp_net_sales: number | null;
	gp_gross_sales: number | null;
	gp_trans: number | null;
	fact_net_sales: number | null;
	fact_gross_sales: number | null;
	fact_trans: number | null;
	ot_hrs_net_sales: number | null;
	ot_gross_sales: number | null;
	PB_Total_hrs: number | null;
	Portal_Total_Hrs: number | null;
	PB_Total_Labour_cost: number | null;
	Portal_Total_Labour_Cost: number | null;
	ukg_raw_total_hours: number | null;
	ukg_raw_exception_pay: number | null;
	ukg_raw_Sales: number | null;
	hs_raw_total_hrs: number | null;
	hs_raw_exception_pay: number | null;
	hs_raw_sales: number | null;
}

export interface ValidationDataFilterParam {
	storeName: string | null;
	businessDate: Date | null;
}


interface ValidationDataState {
  page: number;
  totalPages: number;
  limit: number;
	filters: ValidationDataFilterParam;
	showFilters: boolean;
	config: VDConfig;
}

const initialState: ValidationDataState = {
	page: 1,
	totalPages: 1,
	limit: 15,
	filters: {
		storeName: null,
		businessDate: null
	},
	showFilters: false,
	config: {
		stores: []
	}
}

const validationDataSlice = createSlice({
	name: "validationData",
	initialState,
	reducers: {
		setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
    },
		setFilters: (state, action: PayloadAction<ValidationDataFilterParam>) => {
      state.filters = action.payload;
    },
    setShowFilters: (state, action: PayloadAction<boolean>) => {
      state.showFilters = action.payload;
    },
		setConfig: (state, action: PayloadAction<VDConfig>) => {
      state.config = action.payload;
    },
	}
});

export const {
	setPage,
	setTotalPages,
	setLimit,
	setFilters,
	setShowFilters,
	setConfig,
} = validationDataSlice.actions;

export default validationDataSlice.reducer;