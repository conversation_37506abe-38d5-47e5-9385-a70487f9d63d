import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface PromotionData {
  fiscal_year?: number | null;
  fiscal_period?: string | null;
  fiscal_week?: string | null;
  holidayAndEvents?: string | null;
  marketingPromotion?: string | null;
  loyaltyCampaigns?: string | null;
  [key: string]: string | number | null | undefined;
}

export const initialAddPromotionData: PromotionData = {
  fiscal_year: null,
  fiscal_period: null,
  fiscal_week: null,
  holidayAndEvents: null,
  marketingPromotion: null,
  loyaltyCampaigns: null,
};

interface AddPromotionState {
  newPromotion: PromotionData;
}

const initialState: AddPromotionState = {
  newPromotion: initialAddPromotionData,
};

const addPromotionSlice = createSlice({
  name: "addPromotion",
  initialState,
  reducers: {
    setNewPromotion: (state, action: PayloadAction<PromotionData>) => {
      state.newPromotion = action.payload;
    },
  },
});

export const { setNewPromotion } = addPromotionSlice.actions;

export default addPromotionSlice.reducer;
