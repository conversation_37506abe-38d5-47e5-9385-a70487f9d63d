import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Config, filterList, Store, TabData } from "../app/Types/store-types";

interface StoreManagerState {
  stores: Store[];
  loading: boolean;
  page: number;
  totalPages: number;
  limit: number;
  open: boolean;
  formData: Store;
  isAddMode: boolean;
  refresh: boolean;
  searchText: string;
  config: Config;
  selectedGeographies: string[];
  selectedRegions: string[];
  selectedDistricts: string[];
  selectedMarkets: string[];
  selectedStoreConcepts: string[];
  selectedStoreNames: string[];
  selectedIsClosed: string;
  filters: filterList | null;
  showFilters: boolean;
  confirmDialogOpen: boolean;
  confirmDialogLoading: boolean;
  confirmDialogError: string | null;
  confirmDialogSuccess: boolean;
  tabData: {
    [key: string]: TabData;
  };
  currentTab: number;
  sortBy: string | null;
  sortOrder: "asc" | "desc" | null;
}

const initialState: StoreManagerState = {
  stores: [],
  loading: false,
  page: 1,
  totalPages: 1,
  limit: 15,
  open: false,
  formData: {
    "SQL ID": 0,
    storeName: "",
    "Store Number": "",
    State: "",
    "Store Code": "",
    "Legacy Store ID": 0,
    Store: "",
    Phone: "",
    siteAddress1: "",
    siteAddress2: "",
    city: "",
    stateAbbreviation: "",
    zip: "",
    storeConceptName: "",
    "Open Year": 0,
    openDate: "",
    marketName: "",
    geographyName: "",
    Region: "",
    District: "",
  },
  isAddMode: false,
  refresh: false,
  searchText: "",
  config: {
    geographyName: [],
    Region: [],
    District: [],
    marketName: [],
    storeConceptName: [],
    storeName: [],
  },
  selectedGeographies: [],
  selectedRegions: [],
  selectedDistricts: [],
  selectedMarkets: [],
  selectedStoreConcepts: [],
  selectedStoreNames: [],
  selectedIsClosed: "",
  filters: null,
  showFilters: false,
  confirmDialogOpen: false,
  confirmDialogLoading: false,
  confirmDialogError: null,
  confirmDialogSuccess: false,
  tabData: {},
  currentTab: 0,
  sortBy: "SQL ID",
  sortOrder: "asc",
};

const storeManagerSlice = createSlice({
  name: "storeManager",
  initialState,
  reducers: {
    setStores: (state, action: PayloadAction<Store[]>) => {
      state.stores = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
    },
    setOpen: (state, action: PayloadAction<boolean>) => {
      state.open = action.payload;
    },
    setFormData: (state, action: PayloadAction<Store>) => {
      state.formData = action.payload;
    },
    setIsAddMode: (state, action: PayloadAction<boolean>) => {
      state.isAddMode = action.payload;
    },
    setRefresh: state => {
      state.refresh = !state.refresh;
    },
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
    },
    setConfig: (state, action: PayloadAction<Config>) => {
      state.config = action.payload;
    },
    setSelectedGeographies: (state, action: PayloadAction<string[]>) => {
      state.selectedGeographies = action.payload;
    },
    setSelectedRegions: (state, action: PayloadAction<string[]>) => {
      state.selectedRegions = action.payload;
    },
    setSelectedDistricts: (state, action: PayloadAction<string[]>) => {
      state.selectedDistricts = action.payload;
    },
    setSelectedMarkets: (state, action: PayloadAction<string[]>) => {
      state.selectedMarkets = action.payload;
    },
    setSelectedStoreConcepts: (state, action: PayloadAction<string[]>) => {
      state.selectedStoreConcepts = action.payload;
    },
    setSelectedStoreNames: (state, action: PayloadAction<string[]>) => {
      state.selectedStoreNames = action.payload;
    },
    setSelectedIsClosed: (state, action: PayloadAction<string>) => {
      state.selectedIsClosed = action.payload;
    },
    setFilters: (state, action: PayloadAction<filterList | null>) => {
      state.filters = action.payload;
    },
    setShowFilters: (state, action: PayloadAction<boolean>) => {
      state.showFilters = action.payload;
    },
    setConfirmDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogOpen = action.payload;
    },
    setConfirmDialogLoading: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogLoading = action.payload;
    },
    setConfirmDialogError: (state, action: PayloadAction<string | null>) => {
      state.confirmDialogError = action.payload;
    },
    setConfirmDialogSuccess: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogSuccess = action.payload;
    },
    setTabData: (state, action: PayloadAction<{ tableName: string; data: TabData }>) => {
      state.tabData[action.payload.tableName] = action.payload.data;
    },
    setCurrentTab: (state, action: PayloadAction<number>) => {
      state.currentTab = action.payload;
    },
    resetTabData: state => {
      state.tabData = {};
      state.currentTab = 0;
    },
    removeTabData: (state, action: PayloadAction<string>) => {
      delete state.tabData[action.payload];
    },
    setSortBy: (state, action: PayloadAction<string | null>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<"asc" | "desc" | null>) => {
      state.sortOrder = action.payload;
    },
  },
});

export const {
  setStores,
  setLoading,
  setPage,
  setTotalPages,
  setLimit,
  setOpen,
  setFormData,
  setIsAddMode,
  setRefresh,
  setSearchText,
  setConfig,
  setSelectedGeographies,
  setSelectedRegions,
  setSelectedDistricts,
  setSelectedMarkets,
  setSelectedStoreConcepts,
  setSelectedStoreNames,
  setSelectedIsClosed,
  setFilters,
  setShowFilters,
  setConfirmDialogOpen,
  setConfirmDialogLoading,
  setConfirmDialogError,
  setConfirmDialogSuccess,
  setTabData,
  setCurrentTab,
  resetTabData,
  removeTabData,
  setSortBy,
  setSortOrder,
} = storeManagerSlice.actions;

export default storeManagerSlice.reducer;
