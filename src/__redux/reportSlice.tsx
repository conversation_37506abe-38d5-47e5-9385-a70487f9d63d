

export interface ReportState<T> {
    data: T[];
    loading: boolean;
    error: string | null;
}
  
export interface ReportItem {
    segment: string;
    geographyName: string;
    Region: string;
    District: string;
    Cohort: string;
    storeConceptName: string;
    dayname: string;
    storeCnt: number;
    thisYearNetSales: number;
    thisYearTrans: number;
    previousYear_NetSales: number;
    previousYear_Transactions: number;
}

export interface TyPtdYtdReport {
    cohortSales: TyPtdYtdItem[];
    regionSales: TyPtdYtdItem[];
    geographySales: TyPtdYtdItem[];
    regionDistrictSales: TyPtdYtdItem[];
    segmentSales: TyPtdYtdItem[];
}

export interface TyPtdYtdItem {
    storeConceptName: string;
    dayname: string;
    Region: string;
    District: string;
    Segment: string;
    geographyName: string;

    thisYearNetSales: number | null;
    thisYearTrans: number | null;
    previousYear_NetSales: number | null;
    previousYear_Transactions: number | null;
}

export interface LyWtdReport {
    cohortSales: LyWtdItem[];
    regionSales: LyWtdItem[];
    geographySales: LyWtdItem[];
    regionDistrictSales: LyWtdItem[];
    segmentSales: LyWtdItem[];
}

export interface LyWtdItem {
    Comp: string;
    storeConceptName: string;
    dayname: string;
    storeCnt: number
    Region: string;
    District: string;
    Segment: string;
    geographyName: string;

    previousYearNetSales: number | null;
    previousYearTrans: number | null;
    previousToPreviousYear_NetSales: number | null;
    previousToPreviousYear_Transactions: number | null;
}

export interface LyYtdPtdReport {
    cohortSales: LyYtdPtdItem[];
    regionSales: LyYtdPtdItem[];
    geographySales: LyYtdPtdItem[];
    regionDistrictSales: LyYtdPtdItem[];
    segmentSales: LyYtdPtdItem[];
}

export interface LyYtdPtdItem {
    Comp: string;
    storeConceptName: string;
    dayname: string;
    storeCnt: number
    Region: string;
    District: string;
    Segment: string;
    geographyName: string;

    lastYearNetSales: number | null;
    lastYearTrans: number | null;
    twoYearsAgoNetSales: number | null;
    twoYearsAgoTransactions: number | null;
    previousYear_NetSales: number | null;
    previousYear_Transactions: number | null;
    previousToPreviousYear_NetSales: number | null;
    previousToPreviousYear_Transactions: number | null;
}
