import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PipelineListResponse, PipelineRunApi, PipelineStatusError } from "../app/api/pipelinDashApi";

// Define interfaces for pipeline dashboard state
export interface PipelineOverviewStats {
  totalRuns: number;
  successRate: string;
  totalDuration: string;
  failedRuns: number;
  startTime: string;
  endTime: string;
}

export interface PipelineRun {
  name: string;
  status: "Succeeded" | "Failed" | "InProgress" | "Cancelled" | "N/A";
  lastRun?: string;
  started?: string;
  duration: string;
  durationSoFar?: string;
  successRate: string;
  errorMessage?: string;
  runId: string;
  pipelineName?: string;
  lastUpdated?: string;
  runStart?: string;
  activityStats?: {
    totalActivities: number;
    statusCounts: {
      Succeeded: number;
    };
    completedActivities: number;
  };
  progressPercentage?: number;
  folder: string;
  errors?: PipelineStatusError[] | null;
}

export interface PipelineSchedule {
  name: string;
  type: string;
  status: string;
  description: string;
  properties: {
    type: string;
    runtimeState: string;
    annotations: string[];
    pipelines: {
      pipelineReference: {
        type: string;
        referenceName: string;
      };
      parameters: Record<string, string>;
    }[];
    recurrence: {
      frequency: string;
      interval: number;
      startTime: string;
      timeZone: string;
      schedule: {
        minutes: number[];
        hours: number[];
        weekDays: string[];
        monthDays?: number[];
        months?: string[];
      };
    };
  };
}

export interface PipelineFilters {
  searchTerm: string;
  category: string;
  status: string;
}

interface pipelineSpecificStats {
  pipelineName: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  totalDuration: string;
  successRate: string;
}

export interface PipelineDashState {
  overview: PipelineOverviewStats;
  pipelineSpecificStats: pipelineSpecificStats[];
  pipelines: PipelineRun[];
  pipelineListData: PipelineListResponse;
  pipelineRunsData: PipelineRunApi[];
  pipelineSchedules: PipelineSchedule[];
  loading: boolean;
  schedulesLoading: boolean;
  totalPipelines?: number;
  cardStatsLoader: boolean;
  error: string | null;
  schedulesError: string | null;
  filters: PipelineFilters;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

const initialState: PipelineDashState = {
  overview: {
    totalRuns: 0,
    successRate: "0%",
    totalDuration: "0 min",
    failedRuns: 0,
    startTime: "",
    endTime: "",
  },
  pipelineSpecificStats: [],
  pipelineRunsData: [],
  pipelineListData: {
    count: 0,
    totalPipelines: 0,
    page: 1,
    totalPages: 1,
    pipelines: [],
    hasNextPage: false,
    hasPreviousPage: false,
    search: "",
    endpoint: "",
  },
  pipelineSchedules: [],
  totalPipelines: 0,
  pipelines: [],
  loading: false,
  schedulesLoading: false,
  cardStatsLoader: false,
  error: null,
  schedulesError: null,
  filters: {
    searchTerm: "",
    category: "All Categories",
    status: "All Status",
  },
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 9,
  },
};

const pipelineDashSlice = createSlice({
  name: "pipelineDash",
  initialState,
  reducers: {
    setOverviewStats: (state, action: PayloadAction<PipelineOverviewStats>) => {
      state.overview = action.payload;
    },
    setPipelineSpecificStats: (state, action: PayloadAction<pipelineSpecificStats[]>) => {
      state.pipelineSpecificStats = action.payload;
    },
    setPipelineRunsData: (state, action: PayloadAction<PipelineRunApi[]>) => {
      state.pipelineRunsData = action.payload;
    },
    setTotalPipelines: (state, action: PayloadAction<number>) => {
      state.totalPipelines = action.payload;
    },
    setPipelines: (state, action: PayloadAction<PipelineRun[]>) => {
      state.pipelines = action.payload;
    },
    setPipelineSchedules: (state, action: PayloadAction<PipelineSchedule[]>) => {
      state.pipelineSchedules = action.payload;
    },
    setCardStatsLoader: (state, action: PayloadAction<boolean>) => {
      state.cardStatsLoader = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSchedulesLoading: (state, action: PayloadAction<boolean>) => {
      state.schedulesLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSchedulesError: (state, action: PayloadAction<string | null>) => {
      state.schedulesError = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<PipelineFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.pagination.currentPage = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<PipelineDashState["pagination"]>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setPipelineListData: (state, action: PayloadAction<PipelineListResponse>) => {
      state.pipelineListData = action.payload;
    },
  },
});

export const {
  setOverviewStats,
  setPipelineSpecificStats,
  setPipelineRunsData,
  setPipelines,
  setLoading,
  setCardStatsLoader,
  setError,
  setFilters,
  setCurrentPage,
  setPagination,
  setTotalPipelines,
  setPipelineListData,
  setPipelineSchedules,
  setSchedulesLoading,
  setSchedulesError,
} = pipelineDashSlice.actions;

export default pipelineDashSlice.reducer;
