import { createSlice, PayloadAction } from "@reduxjs/toolkit";


export const initialAddCalendarData = {
    "fiscalCalendarId": null,
    "calendarDate": null,
    "previousYearDate": null,
    "fiscalPeriodNumber": null,
    "fiscalWeekNumber": null,
    "fiscalQuarterNumber": null,
    "fiscalYear": null,
    "isPayPeriodStart": null,
    "isPayPeriodEnd": null,
    "Holiday": null,
}

interface AddCalendarState {
    newCalendar: Record<string, any>;
}

const initialState: AddCalendarState = {
    newCalendar: initialAddCalendarData
}

const addCalendarSlice = createSlice({
  name: "addCalendar",
  initialState,
  reducers: {
    setNewCalendar: (state, action: PayloadAction<Record<string, any>>) => {
      state.newCalendar = action.payload;
    }
  }
})

export const {setNewCalendar} = addCalendarSlice.actions;

export default addCalendarSlice.reducer;