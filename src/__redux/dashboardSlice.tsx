import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Pipeline {
  name: string;
  id: string;
  type: string;
  folder: {
    name: string;
  };
}

interface PipelineResponse {
  count: number;
  totalPipelines: number;
  page: number;
  totalPages: number;
  pipelines: Pipeline[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface PipelineRun {
  runId: string;
  runGroupId: string;
  isLatest: boolean;
  pipelineName: string;
  parameters: Record<string, unknown>;
  invokedBy: {
    name: string;
    id: string;
    invokedByType: string;
  };
  lastUpdated: string;
  runStart: string;
  runEnd: string;
  durationInMs: number;
  status: string;
  message: string;
  id: string;
  debugRunId: string | null;
  pipelineReturnValue: Record<string, unknown>;
  annotations: unknown[];
  runDimension: Record<string, unknown>;
}

interface DashboardState {
  pipelineData: PipelineResponse | null;
  loading: boolean;
  error: string | null;
  page: number;
  limit: number;
  selectedPipeline: string | null;
  pipelineRuns: PipelineRun[];
  startDate: Date;
  endDate: Date;
  searchQuery: string;
}

const initialState: DashboardState = {
  pipelineData: null,
  loading: false,
  error: null,
  page: 1,
  limit: 9,
  selectedPipeline: null,
  pipelineRuns: [],
  startDate: new Date(new Date().setHours(0, 0, 0, 0)),
  endDate: new Date(),
  searchQuery: "",
};

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setPipelineData: (state, action: PayloadAction<PipelineResponse>) => {
      state.pipelineData = action.payload;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },
    setSelectedPipeline: (state, action: PayloadAction<string | null>) => {
      state.selectedPipeline = action.payload;
    },
    setPipelineRuns: (state, action: PayloadAction<PipelineRun[]>) => {
      state.pipelineRuns = action.payload;
    },
    setStartDate: (state, action: PayloadAction<Date>) => {
      state.startDate = action.payload;
    },
    setEndDate: (state, action: PayloadAction<Date>) => {
      state.endDate = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      // Add this reducer
      state.searchQuery = action.payload;
    },
  },
});

export const {
  setPage,
  setPipelineData,
  setLoading,
  setError,
  setSelectedPipeline,
  setPipelineRuns,
  setStartDate,
  setEndDate,
  setSearchQuery, // Add this to the exported actions
} = dashboardSlice.actions;

export default dashboardSlice.reducer;
