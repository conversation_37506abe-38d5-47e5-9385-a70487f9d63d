import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type StoreEntry = Record<string, Record<string, any>>

export const initialAddStoreData = {
  "str_store": {
    "storeId": null,
    "companyId": null,
    "siteId": null,
    "posId": null,
    "loyaltyId": null,
    "storeConceptId": null,
    "dataProviderId": null,
    "storeNumber": null,
    "storeName": null,
    "openDate": null,
    "closedDate": null,
    "showReporting": null,
    "compareDate": null,
    "isClosed": null,
    "validFrom": null,
    "validTo": null,
    "marketId": null,
    "legacyStoreCode": null,
    "legacyStoreId": null,
    "storePhone": null,
    "geographyId": null,
    "isComp": null,
    "upcomingCompDate": null,
    "districtId": null,
    "cohortId": null
  },
  "str_site": {
    "siteId": null,
    "siteAddress1": null,
    "siteAddress2": null,
    "city": null,
    "state": null,
    "stateAbbreviation": null,
    "zip": null,
    "squareFootage": null,
    "latitude": null,
    "longitude": null,
    // "geographyPoint": "POINT(-99.999999 39.999999)",
    "validFrom": null,
    "validTo": null,
    "siteName": null,
    "projectedOpenDate": null
  },
  "str_storeConcept": {
    "storeConceptId": null,
    "storeConceptName": null,
    "validFrom": null,
    "validTo": null
  },
  "str_market": {
    "marketId": null,
    "marketName": null,
    "validFrom": null,
    "validTo": null
  },
  "str_district": {
    "districtid": null,
    "districtName": null,
    "employeeId": null,
    "validFrom": null,
    "validTo": null
  },
  "str_regionDistrict": {
    "regionDistrictId": null,
    "regionId": null,
    "districtId": null,
    "validFrom": null,
    "validTo": null
  },
  "str_region": {
    "regionid": null,
    "regionName": null,
    "employeeId": null,
    "validFrom": null,
    "validTo": null
  },
  "str_geography": {
    "geographyId": null,
    "geographyName": null,
    "validFrom": null,
    "validTo": null
  },
  "emp_employee": {
    "employeeId": null,
    "companyId": null,
    "employeeNumber": null,
    "firstName": null,
    "lastName": null,
    "email": null,
    "departmentId": null,
    "hireDate": null,
    "jobId": null,
    "termDate": null,
    "isActive": null,
    "managerEmployeeNumber": null,
    "validFrom": null,
    "validTo": null,
    "phone": null
  }
}

interface AddStoreState {
  newStore: StoreEntry;
}

const initialState: AddStoreState = {
  newStore: initialAddStoreData
}

const addStoreSlice = createSlice({
  name: "addStore",
  initialState,
  reducers: {
    setNewStore: (state, action: PayloadAction<StoreEntry>) => {
      state.newStore = action.payload;
    }
  }
})

export const {setNewStore} = addStoreSlice.actions;

export default addStoreSlice.reducer;