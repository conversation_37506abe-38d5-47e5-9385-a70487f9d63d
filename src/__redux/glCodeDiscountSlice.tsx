import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import moment from "moment";

export const initialAddGlDiscountData = {
  discount_name: null,
  gl_account_no: null,
  is_active: true,
  source: "Revel",
  update_date: moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss"),
  id: null,
};

export interface AddGLDiscountRecord {
  discount_name: string | null;
  gl_account_no: string | null;
  is_active: boolean | null;
  source: string | null;
  id: number | null;
  update_date: string | null;
  [key: string]: string | number | boolean | Date | null | undefined; // Index signature
}

interface AddGLDiscountState {
  newGLDiscount: AddGLDiscountRecord;
}

const initialState: AddGLDiscountState = {
  newGLDiscount: initialAddGlDiscountData,
};

const addGLDiscountSlice = createSlice({
  name: "addGLDiscount",
  initialState,
  reducers: {
    setNewGlDiscount: (state, action: PayloadAction<AddGLDiscountRecord>) => {
      state.newGLDiscount = action.payload;
    },
  },
});

export const { setNewGlDiscount } = addGLDiscountSlice.actions;

export default addGLDiscountSlice.reducer;
