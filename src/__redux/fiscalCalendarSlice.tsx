import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FiscalCalendar, FiscalCalendarConfig, FiscalCalendarFilterParam } from "../app/Types/fiscal-calendar-types";


interface FiscalCalendarState {
  fiscalCalendars: FiscalCalendar[];
  loading: boolean;
  page: number;
  totalPages: number;
  limit: number;
  open: boolean;
  formData: FiscalCalendar | null;
  isAddMode: boolean;
  refresh: boolean;
  searchText: string;
  config: FiscalCalendarConfig;
  filters: FiscalCalendarFilterParam;
  showFilters: boolean;
  confirmDialogOpen: boolean;
  confirmDialogLoading: boolean;
  confirmDialogError: string | null;
  confirmDialogSuccess: boolean;
  sortBy: string | null;
  sortOrder: "asc" | "desc" | null;
  tabData: Record<string, any>;
}

const initialState: FiscalCalendarState = {
  fiscalCalendars: [],
  loading: false,
  page: 1,
  totalPages: 1,
  limit: 15,
  open: false,
  formData: null,
  isAddMode: false,
  refresh: false,
  searchText: "",
  config: {
    fiscalPeriodNumber: [],
    fiscalWeekNumber: [],
    fiscalQuarterNumber: [],
    fiscalYear: [],
  },
  filters: {
    fiscalPeriodNumber: null,
    fiscalWeekNumber: null,
    fiscalQuarterNumber: null,
    fiscalYear: null,
  },
  showFilters: false,
  confirmDialogOpen: false,
  confirmDialogLoading: false,
  confirmDialogError: null,
  confirmDialogSuccess: false,
  sortBy: null,
  sortOrder: null,
  tabData: {},
};

const fiscalCalendarSlice = createSlice({
  name: "fiscalCalendar",
  initialState,
  reducers: {
    setFiscalCalendars: (state, action: PayloadAction<FiscalCalendar[]>) => {
      state.fiscalCalendars = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setTotalPages: (state, action: PayloadAction<number>) => {
      state.totalPages = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
    },
    setOpen: (state, action: PayloadAction<boolean>) => {
      state.open = action.payload;
    },
    setFormData: (state, action: PayloadAction<FiscalCalendar | null>) => {
      state.formData = action.payload;
    },
    setIsAddMode: (state, action: PayloadAction<boolean>) => {
      state.isAddMode = action.payload;
    },
    setRefresh: (state, action: PayloadAction<boolean>) => {
      state.refresh = action.payload;
    },
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
    },
    setConfig: (state, action: PayloadAction<FiscalCalendarConfig>) => {
      state.config = action.payload;
    },
    setFilters: (state, action: PayloadAction<FiscalCalendarFilterParam>) => {
      state.filters = action.payload;
    },
    setShowFilters: (state, action: PayloadAction<boolean>) => {
      state.showFilters = action.payload;
    },
    setConfirmDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogOpen = action.payload;
    },
    setConfirmDialogLoading: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogLoading = action.payload;
    },
    setConfirmDialogError: (state, action: PayloadAction<string | null>) => {
      state.confirmDialogError = action.payload;
    },
    setConfirmDialogSuccess: (state, action: PayloadAction<boolean>) => {
      state.confirmDialogSuccess = action.payload;
    },
    setSortBy: (state, action: PayloadAction<string | null>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<"asc" | "desc" | null>) => {
      state.sortOrder = action.payload;
    },
    setTabData: (state, action: PayloadAction<Record<string, any>>) => {
      state.tabData = action.payload;
    },
  },
});

export const {
  setFiscalCalendars,
  setLoading,
  setPage,
  setTotalPages,
  setLimit,
  setOpen,
  setFormData,
  setIsAddMode,
  setRefresh,
  setSearchText,
  setConfig,
  setFilters,
  setShowFilters,
  setConfirmDialogOpen,
  setConfirmDialogLoading,
  setConfirmDialogError,
  setConfirmDialogSuccess,
  setSortBy,
  setSortOrder,
  setTabData,
} = fiscalCalendarSlice.actions;

export default fiscalCalendarSlice.reducer;
