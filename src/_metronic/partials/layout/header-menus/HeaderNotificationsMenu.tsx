
import React from "react";
import { FC } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "../../../../app/store";
import { deleteAllAlerts } from "../../../../__redux/generalSlice";
import { Box, Button, Typography } from "@mui/material";
import {
  defaultAlerts,
  defaultLogs,
  KTIcon,
  toAbsoluteUrl,
  useIllustrationsPath,
} from '../../../helpers'

const HeaderNotificationsMenu: FC = () => {
  const allAlerts = useSelector((state: RootState) => state.general.allAlerts);
  const dispatch = useDispatch<AppDispatch>();

  const handleAlertDismiss = () => {
    try {
      dispatch(deleteAllAlerts());
    } catch (error) {
      console.error("Error dismissing alert:", error);
    }
  };

  return (
    <div className="menu menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px" data-kt-menu="true">
      <div className="d-flex flex-column justify-content-center align-items-start rounded-top bg-primary p-6" style={{ minHeight: "100px" }}>
        <h3 className="text-white fw-bold mb-2">Alerts</h3>
        <span className="text-white-50">
          {allAlerts.length} {allAlerts.length === 1 ? "alert" : "alerts"}
        </span>
      </div>

      <div className="tab-content">
        <div className="tab-pane fade show active" id="kt_topbar_notifications_1" role="tabpanel">
          <div className="scroll-y mh-325px my-5 px-8">
            {allAlerts.length === 0 && <Typography>No alerts</Typography>}
            {allAlerts.length > 0 && <Button onClick={handleAlertDismiss}>Dismiss All</Button>}
            {allAlerts.map((alert) => (
              <Box
                key={alert.id}
                sx={{
                  backgroundColor: alert.heading === "Success" ? "green" : "red",
                  color: "white",
                  border: "1px solid #e0e0e0",
                  borderRadius: "8px",
                  padding: "16px",
                  marginBottom: "16px",
                  position: "relative",
                }}
              >
                <Typography variant="h6" color="white" fontWeight="bold">
                  {alert.heading}
                </Typography>
                <Typography>{alert.message}</Typography>
              </Box>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export {HeaderNotificationsMenu}
