import React, { FC } from "react";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "../../../../__redux/authSlice";
import { AppDispatch, RootState } from "../../../../app/store";
import { Avatar, Box, Button, Divider, Menu, MenuItem, Typography } from "@mui/material";
import { AccountCircle, ExitToApp } from "@mui/icons-material";
import PersonIcon from "@mui/icons-material/Person";
import { logoutChannel } from "../../../../app/modules/auth";

const HeaderUserMenu: FC = () => {
  const auth = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    dispatch(logout());
    logoutChannel.postMessage("Logout");
    handleClose();
  };

  const currentUserName = auth?.userName?.split(" [")[0] || "";
  const [firstName, lastName] = currentUserName.split(" ");
  const initials = `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase();

  return (
    <Box>
      <Button onClick={handleMenu} color="inherit" className="d-flex align-items-center">
        <Typography variant="h6" className="d-none d-md-block">
          {firstName} {lastName}
        </Typography>
        <Avatar sx={{ marginLeft: 2, width: 50, height: 50 }} className="me-2 bg-primary">
          {initials || <AccountCircle />}
        </Avatar>
      </Button>
      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        keepMounted
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <Box className="p-3" style={{ width: "250px" }}>
          <Typography variant="h6" className="mb-2">
            <PersonIcon sx={{ fontSize: 35, marginRight: 2 }} />
            {firstName} {lastName}
          </Typography>
          <Typography variant="body1" color="textSecondary" className="mb-1">
            {auth?.email}
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Role:{" "}
            {auth?.userRole?.map((role) => (
              <div key={role}>- {role}</div>
            ))}
          </Typography>
        </Box>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ExitToApp className="me-2" />
          Sign Out
        </MenuItem>
      </Menu>
    </Box>
  );
};

export { HeaderUserMenu };
