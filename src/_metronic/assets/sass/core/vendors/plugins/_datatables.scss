//
// DataTables
//

$toolbar-spacing: 1rem 0;

// Base
table.dataTable {
    width: 100% !important;
    margin: 0 !important;

    th {
        border-bottom-color: var(--#{$prefix}table-border-color);
    }
}

table.dataTable th.dt-type-numeric, 
table.dataTable th.dt-type-date, 
table.dataTable td.dt-type-numeric, 
table.dataTable td.dt-type-date {
    text-align: left;
}

// Head
table.dataTable>thead>tr> {
    td, th {
        &:not(.sorting_disabled) {
            padding-right: 0;
        }
    }
}

// Sorting
table.dataTable > thead {
    .dt-column-order {
        display: none;    
    }

    .dt-orderable-asc,
    .dt-orderable-desc, {
        outline: 0 !important;

        &:after,
        &:before {
            display: none !important;
        }
    }

    .dt-ordering-asc,
    .dt-ordering-desc {
        vertical-align: middle;
        
        &:before,
        &:after {
            position: relative !important;
            opacity: 1 !important;
            display: inline-block !important;
            width: 0.65rem;
            height: 0.65rem;
            content: " " !important;
            //top: 50%;
            bottom: auto;
            right: auto !important;
            left: auto;
            margin-left: 0.5rem;
        }

        &:before {
            display: none !important;
        }
    }
}

table.dataTable > thead .dt-ordering-asc:after {
    opacity: 1;
    @include svg-bg-icon(arrow-top, var(--#{$prefix}text-muted));
}

table.dataTable > thead .dt-ordering-desc:after {
    opacity: 1;
    @include svg-bg-icon(arrow-bottom, var(--#{$prefix}text-muted));
}

// Processing
.dt-container {
    .table-responsive {
        position: relative;
    }

    .dt-processing {
        @include border-radius($border-radius);
        box-shadow: var(--#{$prefix}dropdown-box-shadow);
        background-color: $tooltip-bg;
        color: var(--#{$prefix}gray-700);
        font-weight: $font-weight-semibold;
        margin: 0 !important;
        width: auto;
        padding: 1rem 2rem !important;
        transform: translateX(-50%) translateY(-50%);

        > div {
            display: none;    
        }
    }
}

// Responsive
.dtr-control {
	$icon-size:  1.35rem;
    
    &:before {
        border: 0 !important;
        height: $icon-size;
        width: $icon-size;
        line-height: 1.5;
        text-indent: -999px !important;
        margin-bottom: -(divide($icon-size, 4));
        margin-right: divide($icon-size, 2);
        display: inline-block;
        position: relative;
        font-size: 1.05rem;
        border: 0;
        box-shadow: none;
        mask-size: 85%;
        -webkit-mask-size: 85%;
        content: " ";

        :root &,
        [data-bs-theme="light"] & {
            @include svg-bg-icon(expand, $gray-600);  
        }

        [data-bs-theme="dark"] & {
            @include svg-bg-icon(expand, $gray-600-dark);  
        }
    }
}

.dt-hasChild.dtr-expanded {
	.dtr-control {
		&:before {
			@include svg-bg-icon(collapse, $primary);
		}
	}
}

thead .dtr-control {
	&:before {
		display: none;
	}	
}

table.dataTable > tbody > tr.child span.dtr-title {
    display: inline-block;
    min-width: 100px;
    font-weight: $font-weight-semibold;
}

table.dataTable > tbody > tr.child span.dtr-data {
    font-weight: $font-weight-normal;
}

// Striped
table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
    box-shadow: inset 0 0 0 9999px var(--#{$prefix}table-striped-bg)
}

table.table.dataTable > tbody > tr.selected > * {
    box-shadow: inset 0 0 0 9999px var(--#{$prefix}primary);
    color: var(--#{$prefix}primary-inverse);

    a:not([class="btn"]) {
        color: var(--#{$prefix}primary-light);
        font-weight: $font-weight-semibold;
        text-decoration: underline;
        text-decoration-style: dotted;

        &:hover {
            color: var(--#{$prefix}primary-inverse);
        }
    }
}

// Toolbar
.dt-search > div,
.dt-toolbar > div {
	display: flex;
	align-items: center;	
}

.dt-toolbar .dt-info {
	margin-left: 0.75rem;
	padding-top: 0 !important;
	font-size: 1rem;
	font-weight: 500;
    color: var(--#{$prefix}text-gray-700);
}

// Pagination
.dt-paging  {
    padding: $toolbar-spacing;
    margin-left: 0.5rem;

    .pagination {
        margin: 0;
    }
}

// Scroll
.dt-scroll-body {
    border-left: 0 !important;
		border-bottom: 0 !important;

    .dt-orderable-none.dt-ordering-asc,
    .dt-orderable-none.dt-ordering-desc {
        &:after {
            display: none !important;
        }
    }
}

.dt-scroll-foot {
	border-top: 1px solid var(--#{$prefix}border-color);
}

.dt-scroll > .dt-scroll-body > .table > thead {
    line-height: 0;
    
    .dt-orderable-asc,
		.dt-orderable-desc {
        &:after,
        &:before {
            display: none !important;
        }
    }
}

div.dtfc-right-top-blocker,
div.dtfc-left-top-blocker {
    background-color: var(--#{$prefix}body-bg);
}

// Fixed column
table.dataTable thead tr > .dtfc-fixed-left, 
table.dataTable thead tr > .dtfc-fixed-right {
    background-color: var(--#{$prefix}body-bg);

		&:after {
			box-shadow: none !important;	
		}
}

table.dataTable tbody tr > .dtfc-fixed-left, 
table.dataTable tbody tr > .dtfc-fixed-right {
    background-color: var(--#{$prefix}body-bg);

		&:after {
			box-shadow: none !important;	
		}
}

// Fixed header
.dtfh-floatingparent {
    box-shadow: var(--#{$prefix}box-shadow-sm);

    .table {
        background-color: var(--#{$prefix}body-bg) !important;
    }
    
    &,
    .table,
    .table th {
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }
}

// Child details
.dtr-details {
    display: table !important;  

    li {
        display: table-row !important;  

        .dtr-title {
            padding-right: 0.75rem;    
            color: var(--#{$prefix}gray-900);
        }

        .dtr-data {
            color: var(--#{$prefix}gray-700);
        }

        .dtr-title,
        .dtr-data {
            font-size: 1rem;
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
            display: table-cell !important;    
            border-bottom: 1px solid var(--#{$prefix}border-color);
        }    
    }    
}