//
// Buttons Base
//

// Base
.btn {
}

// With icon
.btn {
	// Font icon
	> i {
		display: inline-flex;
		font-size: $font-size-base;
		padding-right: 0.35rem;
		vertical-align: middle;
	}

	// Icon only button
	&.btn-icon {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		padding: 0;
		height: $input-height;
		width: $input-height;
		line-height: 1;

		i {
			padding-right: 0;	
		}

		// Sizes
		&.btn-sm {
			height: $input-height-sm;
			width: $input-height-sm;			 
		}

		&.btn-lg {
			height: $input-height-lg;
			width: $input-height-lg;
		}

		&.btn-circle {
			border-radius: 50%;
		}
	}
}

// Utilities
.btn {
	.btn-reset {
		background-color: transparent;
		border: 0;
		box-shadow: none;
		user-select: none;
		outline: none;
	}

	&.btn-flex {		
		display: inline-flex;
		align-items: center;
	}

	&.btn-trim-start {
		justify-content: flex-start !important;
		padding-left: 0 !important;		
	}

	&.btn-trim-end {
		justify-content: flex-end !important;
		padding-right: 0 !important;	
	} 		
}