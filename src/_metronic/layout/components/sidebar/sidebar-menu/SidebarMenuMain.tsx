import { useSelector } from "react-redux";
import { ROLES } from "../../../../../app/constants/constants";
import { RootState } from "../../../../../app/store";
import { SidebarMenuItem } from "./SidebarMenuItem";

const SidebarMenuMain = () => {
  // const intl = useIntl();
  const { userRole } = useSelector((state: RootState) => state.auth);

  const isAllowedToView = (allowedRoles: string[]): boolean => {
    if (!userRole) return false;
    return allowedRoles.some(role => userRole.includes(role));
  };

  return (
    // Use boot strap icon only
    <>
      {/* <SidebarMenuItem to="/dashboard" icon="columns-gap" title={intl.formatMessage({ id: "MENU.DASHBOARD" })} fontIcon="bi-app-indicator" /> */}
      {isAllowedToView([ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]) && (
        <>
          <SidebarMenuItem to="/pipeline-dash" icon="columns-gap" title={"Pipeline Dashboard"} />
        </>
      )}

      {/* Store Logistic User Routes */}
      {isAllowedToView([ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]) && (
        <>
          <SidebarMenuItem to="/store-logistic-manager" icon="window-sidebar" title={"Store Logistic Manager"} />
        </>
      )}

      {isAllowedToView([ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]) && (
        <>
          <SidebarMenuItem to="/fiscal-calendar" icon="calendar-month" title={"Fiscal Calendar"} />
          <SidebarMenuItem to="/report" icon="file-earmark-text" title={"Report"} />
        </>
      )}

      {/* Discount & GL Code User Routes */}
      {isAllowedToView([ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.DISCOUNT_PAGE_ADMIN, ROLES.DISCOUNT_PAGE_READER]) && (
        <SidebarMenuItem to="/revel-discount" icon="shop-window" title={"Discount & GL Code"} />
      )}

      {isAllowedToView([ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]) && (
        <>
          <SidebarMenuItem to="/data-validation" icon="card-checklist" title={"Data Validation"} />
          <SidebarMenuItem to="/promotion" icon="cash-coin" title={"Promotions"} />
          <SidebarMenuItem to="/audit-logs" icon="card-list" title={"Audit Logs"} />
          <SidebarMenuItem to="/user-manual" icon="book-half" title={"User Manual"} />
        </>
      )}

      {/* admin routes */}

      <>
        {/* <SidebarMenuItem to="/overview" title="Overview" icon="window-sidebar" fontIcon="bi-layers" /> */}
        {/* <SidebarMenuItemWithSub to="/assets" icon="box-seam" title="Assets" fontIcon="bi-archive">
            <SidebarMenuItem to="/assets/meta-data" title="Asset Info" hasBullet={true} />
            <SidebarMenuItem to="/assets/equipment" title="Equipment Info" hasBullet={true} />
          </SidebarMenuItemWithSub> */}
        {/* <SidebarMenuItem to="/maintenance" title="Maintenance" icon="wrench-adjustable-circle" fontIcon="bi-layers" /> */}
      </>

      {/* <SidebarMenuItem to="/rent" title="Rent" icon="abstract-12" fontIcon="bi-layers" /> */}

      {/* <SidebarMenuItem to="/builder" icon="switch" title="Layout Builder" fontIcon="bi-layers" />
      <div className="menu-item">
        <div className="menu-content pt-8 pb-2">
          <span className="menu-section text-muted text-uppercase fs-8 ls-1">Crafted</span>
        </div>
      </div>
      <SidebarMenuItemWithSub to="/crafted/pages" title="Pages" fontIcon="bi-archive" icon="element-plus">
        <SidebarMenuItemWithSub to="/crafted/pages/profile" title="Profile" hasBullet={true}>
          <SidebarMenuItem to="/crafted/pages/profile/overview" title="Overview" hasBullet={true} />
          <SidebarMenuItem to="/crafted/pages/profile/projects" title="Projects" hasBullet={true} />
          <SidebarMenuItem to="/crafted/pages/profile/campaigns" title="Campaigns" hasBullet={true} />
          <SidebarMenuItem to="/crafted/pages/profile/documents" title="Documents" hasBullet={true} />
          <SidebarMenuItem to="/crafted/pages/profile/connections" title="Connections" hasBullet={true} />
        </SidebarMenuItemWithSub>

        <SidebarMenuItemWithSub to="/crafted/pages/wizards" title="Wizards" hasBullet={true}>
          <SidebarMenuItem to="/crafted/pages/wizards/horizontal" title="Horizontal" hasBullet={true} />
          <SidebarMenuItem to="/crafted/pages/wizards/vertical" title="Vertical" hasBullet={true} />
        </SidebarMenuItemWithSub>
      </SidebarMenuItemWithSub>
      <SidebarMenuItemWithSub to="/crafted/accounts" title="Accounts" icon="profile-circle" fontIcon="bi-person">
        <SidebarMenuItem to="/crafted/account/overview" title="Overview" hasBullet={true} />
        <SidebarMenuItem to="/crafted/account/settings" title="Settings" hasBullet={true} />
      </SidebarMenuItemWithSub>
      <SidebarMenuItemWithSub to="/error" title="Errors" fontIcon="bi-sticky" icon="cross-circle">
        <SidebarMenuItem to="/error/404" title="Error 404" hasBullet={true} />
        <SidebarMenuItem to="/error/500" title="Error 500" hasBullet={true} />
      </SidebarMenuItemWithSub>
      <SidebarMenuItemWithSub to="/crafted/widgets" title="Widgets" icon="element-7" fontIcon="bi-layers">
        <SidebarMenuItem to="/crafted/widgets/lists" title="Lists" hasBullet={true} />
        <SidebarMenuItem to="/crafted/widgets/statistics" title="Statistics" hasBullet={true} />
        <SidebarMenuItem to="/crafted/widgets/charts" title="Charts" hasBullet={true} />
        <SidebarMenuItem to="/crafted/widgets/mixed" title="Mixed" hasBullet={true} />
        <SidebarMenuItem to="/crafted/widgets/tables" title="Tables" hasBullet={true} />
        <SidebarMenuItem to="/crafted/widgets/feeds" title="Feeds" hasBullet={true} />
      </SidebarMenuItemWithSub>
      <div className="menu-item">
        <div className="menu-content pt-8 pb-2">
          <span className="menu-section text-muted text-uppercase fs-8 ls-1">Apps</span>
        </div>
      </div>
      <SidebarMenuItemWithSub to="/apps/chat" title="Chat" fontIcon="bi-chat-left" icon="message-text-2">
        <SidebarMenuItem to="/apps/chat/private-chat" title="Private Chat" hasBullet={true} />
        <SidebarMenuItem to="/apps/chat/group-chat" title="Group Chart" hasBullet={true} />
        <SidebarMenuItem to="/apps/chat/drawer-chat" title="Drawer Chart" hasBullet={true} />
      </SidebarMenuItemWithSub>
      <SidebarMenuItem to="/apps/user-management/users" icon="abstract-28" title="User management" fontIcon="bi-layers" />
      <div className="menu-item">
        <a target="_blank" className="menu-link" href={import.meta.env.VITE_APP_PREVIEW_DOCS_URL + "/changelog"}>
          <span className="menu-icon">
            <KTIcon iconName="code" className="fs-2" />
          </span>
          <span className="menu-title">Changelog {import.meta.env.VITE_APP_VERSION}</span>
        </a>
      </div> */}
    </>
  );
};

export { SidebarMenuMain };
