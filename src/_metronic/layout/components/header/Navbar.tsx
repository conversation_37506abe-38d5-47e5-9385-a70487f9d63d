import PersonIcon from "@mui/icons-material/Person";
import clsx from "clsx";
import { HeaderNotificationsMenu, HeaderUserMenu, ThemeModeSwitcher } from "../../../partials";
import { KTIcon } from "../../../helpers";
import { useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../app/store";
import { useDispatch } from "react-redux";
import { Typography } from "@mui/material";
// import { useLayout } from "../../core";

const itemClass = "ms-1 ms-md-4";
const btnClass = "btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px";
const userAvatarClass = "symbol-35px";
const btnIconClass = "fs-2";

const Navbar = () => {
  const auth = useSelector((state: RootState) => state.auth);
  // const dispatch = useDispatch<AppDispatch>();

  let currentUserName = auth?.userName;
  if (currentUserName) {
    currentUserName = currentUserName.split(" [")[0];
  }
  const firstName = currentUserName?.split(" ")[0];
  const lastName = currentUserName?.split(" ")[1];
  // const { config } = useLayout();
  return (
    <div
      className="app-navbar flex-shrink-0"
      // style={{ display: "flex", justifyContent: "space-between", width: "100%" }}
    >
      <div style={{ display: "flex", flexDirection: "row" }}>
        {/* <Typography sx={{ alignSelf: "center", fontSize: "1.5rem" }}>
          Hi, {firstName} {lastName}
        </Typography> */}
        {/* <div className={clsx("app-navbar-item align-items-stretch", itemClass)}>
          <Search />
        </div> */}

        {/* <div className={clsx("app-navbar-item", itemClass)}>
        <div id="kt_activities_toggle" className={btnClass}>
          <KTIcon iconName="chart-simple" className={btnIconClass} />
        </div>
      </div> */}

      <div className={clsx("app-navbar-item", itemClass)}>
        {/* <div data-kt-menu-trigger="{default: 'click'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end" className={btnClass}>
          <KTIcon iconName="element-plus" className={btnIconClass} />
        </div> */}
        <HeaderNotificationsMenu />
      </div>

        {/* <div className={clsx("app-navbar-item", itemClass)}>
          <div className={clsx("position-relative", btnClass)} id="kt_drawer_chat_toggle">
            <KTIcon iconName="message-text-2" className={btnIconClass} />
            <span className="bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink" />
          </div>
        </div> */}

        {/* <div className={clsx("app-navbar-item", itemClass)}>
          <ThemeModeSwitcher toggleBtnClass={clsx("btn-active-light-primary btn-custom")} />
        </div> */}

        <div className={clsx("app-navbar-item", itemClass)}>
          {/* <div
            style={{ backgroundColor: "#222222", borderRadius: "50%", padding: "5px" }}
            className={clsx("cursor-pointer symbol", userAvatarClass)}
            data-kt-menu-trigger="{default: 'click'}"
            data-kt-menu-attach="parent"
            data-kt-menu-placement="bottom-end"
          > */}
          {/* <img src={toAbsoluteUrl("media/avatars/300-3.jpg")} alt="" /> */}
          {/* <PersonIcon sx={{ fontSize: 35, color: "#ffff" }} /> */}
          {/* </div> */}
          <HeaderUserMenu />
        </div>

        {/* {config.app?.header?.default?.menu?.display && (
        <div className="app-navbar-item d-lg-none ms-2 me-n3" title="Show header menu">
          <div className="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_header_menu_toggle">
            <KTIcon iconName="text-align-left" className={btnIconClass} />
          </div>
        </div>
      )} */}
      </div>
    </div>
  );
};

export { Navbar };
