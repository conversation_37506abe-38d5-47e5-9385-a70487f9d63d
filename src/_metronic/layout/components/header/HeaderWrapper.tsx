import CodeIcon from "@mui/icons-material/Code";
import { Box, Typography } from "@mui/material";
import clsx from "clsx";
import { Link } from "react-router-dom";
import { KTIcon } from "../../../helpers";
import { LayoutSetup, useLayout } from "../../core";
import { Header } from "./Header";
import { Navbar } from "./Navbar";

export function HeaderWrapper() {
  const { config, classes } = useLayout();
  if (config.app?.header?.default?.container === "fluid") {
    LayoutSetup.classes.headerContainer.push("container-fluid");
  } else {
    LayoutSetup.classes.headerContainer.push("container-xxl");
  }
  if (!config.app?.header?.display) {
    return null;
  }

  return (
    <div
      id="kt_app_header"
      className="app-header"
      data-kt-sticky="true"
      data-kt-sticky-activate="{default: true, lg: true}"
      data-kt-sticky-name="app-header-minimize"
      data-kt-sticky-offset='{default: "200px", lg: "0"}'
      data-kt-sticky-animation="false"
    >
      <div
        id="kt_app_header_container"
        className={clsx("app-container", classes.headerContainer.join(" "), config.app?.header?.default?.containerClass)}
      >
        {config.app.sidebar?.display && (
          <>
            {config.layoutType !== "dark-header" && config.layoutType !== "light-header" ? (
              <div className="d-flex align-items-center d-lg-none ms-n2 me-2" title="Show sidebar menu">
                <div className="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
                  <KTIcon iconName="abstract-14" className=" fs-1" />
                </div>
                <div className="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                  <Link to="/dashboard" className="d-lg-none">
                    {/* <PublicIcon /> */}
                    <div className="d-flex justify-content-center align-items-center w-100">
                      <img src="media/lou/lou-malnatis-logo-clear.png" alt="Logo" className="img-fluid" style={{ maxWidth: "11rem" }} />
                    </div>
                  </Link>
                </div>
              </div>
            ) : null}
          </>
        )}
        {/* {!(config.layoutType === "dark-sidebar" || config.layoutType === "light-sidebar") && (
          <div className="d-flex align-items-center flex-grow-1 flex-lg-grow-0 me-lg-15">
            <Link to="/dashboard">
              {config.layoutType === "dark-header" ? (
                <img alt="Logo" src={toAbsoluteUrl("media/logos/default-dark.svg")} className="h-20px h-lg-30px app-sidebar-logo-default" />
              ) : (
                <>
                  <img
                    alt="Logo"
                    src={toAbsoluteUrl("media/logos/default.svg")}
                    className="h-20px h-lg-30px app-sidebar-logo-default theme-light-show"
                  />
                  <img
                    alt="Logo"
                    src={toAbsoluteUrl("media/logos/default-dark.svg")}
                    className="h-20px h-lg-30px app-sidebar-logo-default theme-dark-show"
                  />
                </>
              )}
            </Link>
          </div>
        )} */}
        <div id="kt_app_header_wrapper" className="d-flex align-items-stretch justify-content-between flex-lg-grow-1">
          {config.app.header.default?.content === "menu" && config.app.header.default.menu?.display && (
            <div
              className="app-header-menu app-header-mobile-drawer align-items-stretch"
              data-kt-drawer="true"
              data-kt-drawer-name="app-header-menu"
              data-kt-drawer-activate="{default: true, lg: false}"
              data-kt-drawer-overlay="true"
              data-kt-drawer-width="225px"
              data-kt-drawer-direction="end"
              data-kt-drawer-toggle="#kt_app_header_menu_toggle"
              data-kt-swapper="true"
              data-kt-swapper-mode="{default: 'append', lg: 'prepend'}"
              data-kt-swapper-parent="{default: '#kt_app_body', lg: '#kt_app_header_wrapper'}"
            >
              <div className="d-flex justify-content-center align-items-center w-100">
                <img src="media/lou/lou-malnatis-logo-clear.png" alt="Logo" className="img-fluid" style={{ maxWidth: "11rem" }} />
              </div>
              <Header />
            </div>
          )}
          {import.meta.env.VITE_APP_ENV === "DEV" && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                padding: "15px",
                backgroundColor: "red",
                color: (theme) => theme.palette.primary.contrastText,
                // position: "relative",
                // overflow: "hidden",
                // "&::before": {
                //   content: '""',
                //   position: "absolute",
                //   top: 0,
                //   left: "-100%",
                //   width: "200%",
                //   height: "100%",
                //   background: "linear-gradient(to right, transparent, rgba(255,255,255,0.2), transparent)",
                //   animation: "shine 2s infinite",
                // },
                // "@keyframes shine": {
                //   "0%": { left: "-100%" },
                //   "100%": { left: "100%" },
                // },
              }}
            >
              <Typography
                variant="subtitle1"
                component="div"
                sx={{
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <CodeIcon sx={{ mr: 1 }} />
                {import.meta.env.VITE_APP_ENV === "DEV" ? "Development Environment" : "Production Environment"}
              </Typography>
            </Box>
          )}
          <Navbar />
        </div>
      </div>
    </div>
  );
}
