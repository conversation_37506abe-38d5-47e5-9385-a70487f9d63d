
export interface FiscalCalendar {
	fiscalCalendarId: number;
	calendarDate: string;
	previousYearDate: string;
	dayName: string;
	dayOfWeek: number;
	FutureFiscalYear: string | number;
	PreviousFiscalYear: number;
	fiscalPeriodNumber: number;
	fiscalWeekNumber: number;
	fiscalQuarterNumber: number;
	fiscalYear: number;
	createDate: string;
	isPayPeriodEnd: boolean;
	isPayPeriodStart: boolean;
	Holiday: boolean;
}

export interface FiscalCalendarConfig {
	fiscalPeriodNumber: number[];
	fiscalWeekNumber: number[];
	fiscalQuarterNumber: number[];
	fiscalYear: number[];
}

export interface FiscalCalendarFilterParam {
	fiscalPeriodNumber: string | null;
	fiscalWeekNumber: string | null;
	fiscalQuarterNumber: string | null;
	fiscalYear: string | null;
}