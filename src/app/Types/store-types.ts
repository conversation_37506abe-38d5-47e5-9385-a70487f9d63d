export interface Store {
  "SQL ID": number;
  storeName: string;
  "Store Number": string;
  State: string;
  "Store Code": string;
  "Legacy Store ID": number;
  Store: string;
  Phone: string;
  siteAddress1: string;
  siteAddress2: string;
  city: string;
  stateAbbreviation: string;
  zip: string;
  storeConceptName: string;
  "Open Year": number;
  openDate: string;
  marketName: string;
  geographyName: string;
  Region: string;
  District: string;
  RM?: string;
  DM?: string;
  IsClosed?: string;
}

export interface filterList {
  geographyName: string[];
  Region: string[];
  District: string[];
  marketName: string[];
  storeConceptName: string[];
  storeName: string[];
  IsClosed?: string;
}

export interface Config {
  geographyName: string[];
  Region: string[];
  District: string[];
  marketName: string[];
  storeConceptName: string[];
  storeName: string[];
}

export interface TabData {
  [key: string]: string | number | boolean | Date | { latitude: number; longitude: number } | null;
}
