import { Box, CircularProgress } from "@mui/material";
import React from "react";
import "./FullScreenLoader.css";

interface FullScreenLoaderProps {
  isLoading: boolean;
}

const FullScreenLoader: React.FC<FullScreenLoaderProps> = ({ isLoading }) => {
  if (!isLoading) {
    return null;
  }

  return (
    <Box className="fullscreen-loader">
      <CircularProgress />
    </Box>
  );
};

export default FullScreenLoader;
