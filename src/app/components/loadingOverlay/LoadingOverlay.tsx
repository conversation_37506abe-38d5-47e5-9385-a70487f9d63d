import React from "react";
import { Box, CircularProgress } from "@mui/material";

interface LoadingOverlayProps {
  loading: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ loading }) => {
  if (!loading) return null;

  return (
    <Box
      position="absolute"
      top={0}
      left={0}
      right={0}
      bottom={0}
      display="flex"
      alignItems="center"
      justifyContent="center"
      bgcolor="rgba(255, 255, 255, 0.7)"
      zIndex={1}
    >
      <CircularProgress />
    </Box>
  );
};

export default LoadingOverlay;
