import React from "react";
import { Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button, Box, Typography, CircularProgress } from "@mui/material";
import { styled } from "@mui/material/styles";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";

interface ConfirmationDialogProps {
  open: boolean;
  title: string;
  content: string;
  onConfirm: () => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: theme.shape.borderRadius * 2,
    boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
    backdropFilter: "blur(4px)",
    border: "1px solid rgba(255, 255, 255, 0.18)",
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  textTransform: "none",
  padding: theme.spacing(1, 3),
  transition: "all 0.3s ease",
  "&:hover": {
    transform: "translateY(-2px)",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
  },
}));

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({ open, title, content, onConfirm, onCancel, isLoading, error, success }) => {
  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      console.error("Error in confirmation:", error);
    }
  };

  return (
    <StyledDialog open={open} onClose={onCancel} aria-labelledby="confirmation-dialog-title" aria-describedby="confirmation-dialog-description">
      <DialogTitle id="confirmation-dialog-title">
        <Typography variant="h6" component="span">
          {title}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="confirmation-dialog-description">{content}</DialogContentText>
        {isLoading && (
          <Box display="flex" justifyContent="center" my={2}>
            <CircularProgress />
          </Box>
        )}
        {error && (
          <Box display="flex" alignItems="center" my={2} color="error.main">
            <ErrorOutlineIcon sx={{ mr: 1 }} />
            <Typography variant="body2">{error}</Typography>
          </Box>
        )}
        {success && (
          <Box display="flex" alignItems="center" my={2} color="success.main">
            <CheckCircleOutlineIcon sx={{ mr: 1 }} />
            <Typography variant="body2">Operation completed successfully!</Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <StyledButton onClick={onCancel} color="primary" variant="outlined" disabled={isLoading}>
          Cancel
        </StyledButton>
        <StyledButton onClick={handleConfirm} color="primary" variant="contained" autoFocus disabled={isLoading}>
          Confirm
        </StyledButton>
      </DialogActions>
    </StyledDialog>
  );
};

export default ConfirmationDialog;
