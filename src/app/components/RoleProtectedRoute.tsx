import { FC } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "../store";

interface RoleProtectedRouteProps {
  allowedRoles: string[];
}

const RoleProtectedRoute: FC<RoleProtectedRouteProps> = ({ allowedRoles }) => {
  const { userRole } = useSelector((state: RootState) => state.auth);

  if (!userRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return allowedRoles.some((role) => userRole.includes(role)) ? <Outlet /> : <Navigate to="/unauthorized" replace />;
};

export default RoleProtectedRoute;
