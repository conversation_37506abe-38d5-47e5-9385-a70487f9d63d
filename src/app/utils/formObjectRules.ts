import { AddStoreFormFieldType } from "./formFieldTypes";

interface FormObjectValidation {
  helperText: string;
  maxInputLimit: number;
  fieldType: AddStoreFormFieldType;
}

export const AddStoreFormObjectRule: Record<string, FormObjectValidation> = {
  // TABLE:: str_store
  storeId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  companyId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  siteId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  posId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  loyaltyId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  storeConceptId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  dataProviderId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  storeNumber: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  storeName: {
    helperText: "",
    maxInputLimit: 100,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  openDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  closedDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  showReporting: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.YES_NO,
  },
  compareDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  isClosed: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.YES_NO,
  },
  validFrom: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  validTo: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  marketId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  legacyStoreCode: {
    helperText: "",
    maxInputLimit: 2,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  legacyStoreId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  storePhone: {
    helperText: "",
    maxInputLimit: 10,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  geographyId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  isComp: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.YES_NO,
  },
  upcomingCompDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  districtId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  cohortId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },

  // TABLE:: str_site
  // "siteId" already exist under str_store section
  siteName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  siteAddress1: {
    helperText: "",
    maxInputLimit: 100,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  siteAddress2: {
    helperText: "",
    maxInputLimit: 100,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  city: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  state: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  stateAbbreviation: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  zip: {
    helperText: "",
    maxInputLimit: 6,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  squareFootage: {
    helperText: "",
    maxInputLimit: 5,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  latitude: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  longitude: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  projectedOpenDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  // "geographyPoint" filed is hidden on the UI
  // "validFrom" & "validTo" already exist under str_store section

  //  TABLE:: str_storeConcept
  // "storeConceptId" "validFrom" & "validTo" already exist under str_store section
  storeConceptName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },

  // TABLE:: str_market
  // "marketId" "validFrom" & "validTo" already exist under str_store section
  marketName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },

  // TABLE:: str_district
  districtid: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  districtName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  // "validFrom" & "validTo" already exist under str_store section

  // TABLE:: str_regionDistrict
  regionDistrictId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  regionId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  // "validFrom" & "validTo" already exist under str_store section

  // TABLE:: str_region
  // "validFrom" & "validTo" already exist under str_store section
  regionid: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  regionName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },

  // TABLE:: str_geography
  // "geographyId" "validFrom" & "validTo" already exist under str_store section
  geographyName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },

  // TABLE:: emp_employee
  employeeId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  employeeNumber: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  firstName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  lastName: {
    helperText: "",
    maxInputLimit: 20,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  email: {
    helperText: "",
    maxInputLimit: 100,
    fieldType: AddStoreFormFieldType.EMAIL,
  },
  departmentId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  jobId: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  hireDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  termDate: {
    helperText: "",
    maxInputLimit: 8,
    fieldType: AddStoreFormFieldType.DATE,
  },
  isActive: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.YES_NO,
  },
  managerEmployeeNumber: {
    helperText: "",
    maxInputLimit: 4,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  phone: {
    helperText: "",
    maxInputLimit: 10,
    fieldType: AddStoreFormFieldType.NUMERIC,
  },
  // TABLE:: emp_job
  // jobId is already exist under emp_employee section
  jobDescription: {
    helperText: "",
    maxInputLimit: 250,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  job_code: {
    helperText: "",
    maxInputLimit: 100, // Using a reasonable limit for VARCHAR(MAX)
    fieldType: AddStoreFormFieldType.TEXT,
  },
  external_job_id: {
    helperText: "",
    maxInputLimit: 100, // Using a reasonable limit for VARCHAR(MAX)
    fieldType: AddStoreFormFieldType.TEXT,
  },
  // "validFrom" & "validTo" already exist elsewhere

  // TABLE:: emp_department
  // "departmentId" already exists under emp_employee section
  departmentName: {
    helperText: "",
    maxInputLimit: 50,
    fieldType: AddStoreFormFieldType.TEXT,
  },
  // "validFrom" & "validTo" already exist elsewhere
};
