import { ROLES } from "../constants/constants";

export const isAdmin = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const adminRoles = [ROLES.ADMIN];

  return userRoles.some(role => adminRoles.includes(role as ROLES));
};

export const isDeveloper = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const devRoles = [ROLES.DEVELOPER];

  return userRoles.some(role => devRoles.includes(role as ROLES));
};

export const isStorePageAdmin = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const adminRoles = [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.STORE_LOGISTIC_ADMIN];

  return userRoles.some(role => adminRoles.includes(role as ROLES));
};

export const isStorePageReader = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const readerRoles = [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_READER];

  return userRoles.some(role => readerRoles.includes(role as ROLES));
};

export const isStorePageReadOnly = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const readerRoles = [ROLES.READER, ROLES.STORE_LOGISTIC_READER];

  return userRoles.some(role => readerRoles.includes(role as ROLES));
};

export const isDiscountPageAdmin = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const adminRoles = [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.DISCOUNT_PAGE_ADMIN];

  return userRoles.some(role => adminRoles.includes(role as ROLES));
};

export const isDiscountPageReader = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const readerRoles = [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.DISCOUNT_PAGE_READER];

  return userRoles.some(role => readerRoles.includes(role as ROLES));
};

export const isDiscountPageReadOnly = (userRoles: string[] | null): boolean => {
  if (!userRoles) return false;

  const readerRoles = [ROLES.READER, ROLES.DISCOUNT_PAGE_READER];

  return userRoles.some(role => readerRoles.includes(role as ROLES));
};
