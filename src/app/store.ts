// src/app/store.ts
import { configureStore } from "@reduxjs/toolkit";
import { combineReducers } from "redux";
import { persistReducer, persistStore } from "redux-persist";
import storage from "redux-persist/lib/storage"; // defaults to localStorage for web
import authSlice from "../__redux/authSlice";
import generalSlice from "../__redux/generalSlice";
import storeManagerSlice from "../__redux/storeManagerSlice";
import fiscalCalendarSlice from "../__redux/fiscalCalendarSlice";
import promotionSlice from "../__redux/promotionSlice";
import auditLogSlice from "../__redux/auditLogSlice";
import addStoreSlice from "../__redux/addStoreSlice";
import dashboardSlice from "../__redux/dashboardSlice";
import addCalendarSlice from "../__redux/addCalendarSlice";
import addPromotionSlice from "../__redux/addPromotionSlice";
import glCodeDiscountSlice from "../__redux/glCodeDiscountSlice";
import validationDataSlice from "../__redux/validationDataSlice";
import pipelineDashSlice from "../__redux/pipelineDashSlice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["auth"],
};

const rootReducer = combineReducers({
  // counter: counterSlice,
  auth: authSlice,
  general: generalSlice,
  storeManager: storeManagerSlice,
  addStore: addStoreSlice,
  addCalendar: addCalendarSlice,
  fiscalCalendar: fiscalCalendarSlice,
  promotion: promotionSlice,
  auditLog: auditLogSlice,
  addPromotion: addPromotionSlice,
  dashboard: dashboardSlice,
  glDiscount: glCodeDiscountSlice,
  validationData: validationDataSlice,
  pipelineDash: pipelineDashSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
