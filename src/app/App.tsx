import {Suspense, useEffect} from 'react'
import {Outlet} from 'react-router-dom'
import {I18nProvider} from '../_metronic/i18n/i18nProvider'
import {LayoutProvider, LayoutSplashScreen} from '../_metronic/layout/core'
import {MasterInit} from '../_metronic/layout/MasterInit'
import {AuthInit, logoutAllTabs } from './modules/auth'
import {ThemeModeProvider} from '../_metronic/partials'
import { checkTokenExpiration } from "./modules/auth/core/AuthHelpers"
import { useDispatch, useSelector } from "react-redux"
import { RootState } from "./store"

const App = () => {
  const dispatch = useDispatch();
  const auth = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    logoutAllTabs()
   }, [])

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (auth.tokenExpirationTime !== null) {
      const checkExpiration = () => {
        console.log("checkExpiration", new Date(auth.tokenExpirationTime as number).toLocaleTimeString());
        checkTokenExpiration(dispatch, auth.tokenExpirationTime);
      };

      intervalId = setInterval(checkExpiration, 1000); // Check every second
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [dispatch, auth.tokenExpirationTime]);
  
  return (
    <Suspense fallback={<LayoutSplashScreen />}>
      <I18nProvider>
        <LayoutProvider>
          <ThemeModeProvider>
            <AuthInit>
              <Outlet />
              <MasterInit />
            </AuthInit>
          </ThemeModeProvider>
        </LayoutProvider>
      </I18nProvider>
    </Suspense>
  );
};

export {App};
