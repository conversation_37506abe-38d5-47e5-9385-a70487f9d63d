import axios from "axios";

const URL = import.meta.env.VITE_API_URL;

interface PipelineStatusesResponse {
  overallStats: {
    totalRuns: number;
    successfulRuns: number;
    failedRuns: number;
    successRate: string;
    totalDuration: string;
    startTime: string;
    endTime: string;
  };
  pipelineSpecificStats: [
    {
      pipelineName: string;
      totalRuns: number;
      successfulRuns: number;
      failedRuns: number;
      totalDuration: string;
      successRate: string;
    }
  ];
}

export const getAllPipelineStatuses = async (startTime?: string, endTime?: string) => {
  try {
    const body = {
      ...(startTime && { startTime }),
      ...(endTime && { endTime }),
    };

    const response = await axios.post<PipelineStatusesResponse>(`${URL}/all-pipeline-statuses`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching all pipeline statuses:", error);
    throw error;
  }
};

interface OverallMetrics {
  startDate: string;
  endDate: string;
  totalDays: number;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
}

interface PipelineMetric {
  pipelineName: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
}

interface DailyMetric {
  date: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
  pipelineMetrics: PipelineMetric[];
}

interface PipelineMetricsResponse {
  overallMetrics: OverallMetrics;
  dailyMetrics: DailyMetric[];
}

export const getDailyPipelineMetrics = async (startTime?: string, endTime?: string, pipelineName?: string) => {
  try {
    const body = {
      ...(startTime && { startTime }),
      ...(endTime && { endTime }),
      ...(pipelineName && { pipelineName }),
    };

    const response = await axios.post<PipelineMetricsResponse>(`${URL}/daily-pipeline-metrics`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching daily pipeline metrics:", error);
    throw error;
  }
};

export interface PipelineListResponse {
  count: number;
  totalPipelines: number;
  page: number;
  totalPages: number;
  pipelines: {
    name: string;
    id: string;
    type: string;
    folder: {
      name: string;
    };
  }[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  search: string;
  endpoint: string;
}

export const getPipelineList = async (page: number = 1, limit: number = 9, search: string = "") => {
  try {
    const body = {
      page,
      limit,
      search,
    };

    const response = await axios.post<PipelineListResponse>(`${URL}/get-pipeline-list`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline list:", error);
    throw error;
  }
};

interface TriggerPipelineRequestBody {
  pipelineName: string;
  parameters: {
    param1: string;
    param2: string;
  };
}
export const triggerPipeline = async (pipelineName: string, parameters: { param1: string; param2: string }) => {
  try {
    const body: TriggerPipelineRequestBody = {
      pipelineName,
      parameters,
    };

    const response = await axios.post(`${URL}/trigger-pipeline`, body);
    return response.data;
  } catch (error) {
    console.error("Error triggering pipeline:", error);
    throw error;
  }
};

export const cancelPipelineRun = async (runId: string) => {
  try {
    const body = {
      runId,
    };
    const response = await axios.post(`${URL}/cancel-pipeline-run`, body);
    return response.data;
  } catch (error) {
    console.error("Error canceling pipeline run:", error);
    throw error;
  }
};

interface PipelineActivityRunsRequestBody {
  pipelineName: string;
  runId: string;
  filterOptions: {
    lastUpdatedAfter?: string;
    lastUpdatedBefore?: string;
    filters?: {
      key: string;
      value: string;
    }[];
  };
}

interface PipelineActivityRunsResponse {
  activityName: string;
  activityType: string;
  status: string;
  durationInMs: number;
  error: {
    errorCode: string;
    message: string;
    failureType: string;
    target: string;
    details: string;
  };
  input: {
    source: {
      type: string;
      sqlReaderQuery: string;
      queryTimeout: string;
      partitionOption: string;
    };
    dataset: {
      referenceName: string;
      type: string;
      parameters: {
        Schema: string;
        Table: string;
      };
    };
    firstRowOnly: boolean;
  };
  output: {
    count: number;
    value: {
      ID: number;
      SourceDB: string;
      SourceSchemaName: string;
      SourceTableName: string;
      ColumnList: string;
      TargetContainer: string;
      TargetPath: string;
      TargetFile: string;
      FileFormat: string;
      TargetStageTableSchema: string;
      TargetStageTableName: string;
      DataLoadType: string;
      DateField: string | null;
      LastDate: string;
      EnableAction: number;
      SubjectArea: string;
      ProcessName: string;
      SourcePath: string;
      SourceFileName: string;
      ProcessSequence: number;
      refresh_date: string;
      comments: string;
      LastDateParquet: string;
      Inc_query: string;
    }[];
    effectiveIntegrationRuntime: string;
    billingReference: {
      activityType: string;
      billableDuration: {
        meterType: string;
        duration: number;
        unit: string;
      }[];
      totalBillableDuration: {
        meterType: string;
        duration: number;
        unit: string;
      }[];
    };
    durationInQueue: {
      integrationRuntimeQueue: number;
    };
  };
  retryAttempt: null;
}

export const getPipelineActivityRuns = async (pipelineName: string, runId: string) => {
  try {
    const body: PipelineActivityRunsRequestBody = {
      pipelineName,
      runId,
      filterOptions: {
        filters: [],
      },
    };
    const response = await axios.post<PipelineActivityRunsResponse>(`${URL}/pipeline-activity-runs`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline activity runs:", error);
    throw error;
  }
};

export interface PipelineRunApi {
  runId: string;
  runGroupId: string;
  isLatest: boolean;
  pipelineName: string;

  invokedBy: {
    name: string;
    id: string;
    invokedByType: string;
  };
  lastUpdated: string;
  runStart: string;
  runEnd: string;
  durationInMs: number;
  status: string;
  message: string;
  id: string;
  debugRunId: string | null;
}

export interface PipelineRunsResponse {
  value: PipelineRunApi[];
}

export const getPipelineRuns = async (pipelineName: string, startTime?: string, endTime?: string) => {
  try {
    const body = {
      pipelineName,
      ...(startTime && { startTime }),
      ...(endTime && { endTime }),
    };
    const response = await axios.post<PipelineRunsResponse>(`${URL}/pipeline-runs`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline runs:", error);
    throw error;
  }
};

export interface PipelineStatusResponse {
  runId: string;
  status: string;
  pipelineName: string;
  runStart: string;
  runEnd: string;
  durationInMs: number;
  progressPercentage: number;
  estimatedTimeRemaining: null;
  lastUpdated: string;
  isLatest: boolean;
  activityStats: {
    totalActivities: number;
    statusCounts: {
      Succeeded: number;
    };
    completedActivities: number;
    errors: PipelineStatusError[];
  };
}

export interface PipelineStatusError {
  activityName: string;
  errorMessage: string;
  errorCode: string;
  details: string[];
}

export const getPipelineStatus = async (runId: string) => {
  try {
    const body = {
      runId,
    };
    const response = await axios.post<PipelineStatusResponse>(`${URL}/pipeline-status`, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline status:", error);
    throw error;
  }
};

interface PipelineSchedulesResponse {
  schedules: [
    {
      name: string;
      type: string;
      status: string;
      description: string;
      properties: {
        type: string;
        runtimeState: string;
        annotations: string[];
        pipelines: [
          {
            pipelineReference: {
              type: string;
              referenceName: string;
            };
            parameters: Record<string, string>;
          }
        ];
        recurrence: {
          frequency: string;
          interval: number;
          startTime: string;
          timeZone: string;
          schedule: {
            minutes: number[];
            hours: number[];
            weekDays: string[];
          };
        };
      };
    }
  ];
}

// {
//   "name": "TR_TOC_Writeback_15MINS",
//   "type": "ScheduleTrigger",
//   "status": "Started",
//   "description": "StartTime : Mon-Fri 12:00CT\n\nEndTime : 23:45CT",
//   "properties": {
//       "type": "ScheduleTrigger",
//       "description": "StartTime : Mon-Fri 12:00CT\n\nEndTime : 23:45CT",
//       "runtimeState": "Started",
//       "annotations": [],
//       "pipelines": [
//           {
//               "pipelineReference": {
//                   "type": "PipelineReference",
//                   "referenceName": "PL_Writeback"
//               },
//               "parameters": {}
//           }
//       ],
//       "recurrence": {
//           "frequency": "Week",
//           "interval": 1,
//           "startTime": "2024-10-21T01:10:00.000Z",
//           "timeZone": "Central Standard Time",
//           "schedule": {
//               "minutes": [
//                   45
//               ],
//               "hours": [
//                   12,
//                   13,
//                   14,
//                   15,
//                   16,
//                   17,
//                   18,
//                   19,
//                   20,
//                   21,
//                   22,
//                   23
//               ],
//               "weekDays": [
//                   "Monday",
//                   "Tuesday",
//                   "Wednesday",
//                   "Thursday",
//                   "Friday"
//               ]
//           }
//       }
//   }
// },

export const getPipelineSchedules = async () => {
  try {
    const response = await axios.post<PipelineSchedulesResponse>(`${URL}/pipeline-schedules`, {});
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline schedules:", error);
    throw error;
  }
};

// sample body
// {
//   "triggerName": "BusinessHoursTrigger",
//   "scheduleConfig": {
//     "recurrence": {
//       "frequency": "Week",
//       "interval": 1,
//       "startTime": "2023-05-01T12:45:00Z",
//       "timeZone": "America/Chicago",
//       "schedule": {
//         "weekDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
//         "hours": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//         "minutes": [45]
//       }
//     }
//   }
// }

export interface UpdateTriggerScheduleRequestBody {
  triggerName: string;
  scheduleConfig: {
    recurrence: {
      frequency: string;
      interval: number;
      startTime: string;
      timeZone: string;
      schedule: {
        weekDays?: string[];
        hours?: number[];
        minutes?: number[];
        monthDays?: number[];
        months?: string[];
      };
    };
  };
}

export interface UpdateTriggerScheduleResponse {
  triggerName: string;
  message: string;
  updatedTrigger: {
    id: string;
    name: string;
    type: string;
    etag: string;
    properties: {
      type: string;
      runtimeState: string;
      annotations: unknown[];
      pipelines: {
        pipelineReference: {
          type: string;
          referenceName: string;
        };
        parameters: Record<string, unknown>;
      }[];
      recurrence: {
        frequency: string;
        interval: number;
        startTime: string;
        timeZone: string;
        schedule: {
          minutes: number[];
          hours: number[];
          weekDays?: string[];
          monthDays?: number[];
          months?: string[];
        };
      };
    };
  };
}

export const updateTriggerSchedule = async (objectToUpdate: UpdateTriggerScheduleRequestBody): Promise<UpdateTriggerScheduleResponse> => {
  try {
    const body = {
      triggerName: objectToUpdate.triggerName,
      scheduleConfig: objectToUpdate.scheduleConfig,
    };
    const response = await axios.post<UpdateTriggerScheduleResponse>(`${URL}/update-trigger-schedule`, body);
    return response.data;
  } catch (error) {
    console.error("Error updating trigger schedule:", error);
    throw error;
  }
};

interface TriggerActionResponse {
  triggerName: string;
  message: string;
}

export const startTrigger = async (triggerName: string): Promise<TriggerActionResponse> => {
  try {
    const body = {
      triggerName,
    };
    const response = await axios.post<TriggerActionResponse>(`${URL}/start-trigger`, body);
    return response.data;
  } catch (error) {
    console.error("Error starting trigger:", error);
    throw error;
  }
};

export const stopTrigger = async (triggerName: string): Promise<TriggerActionResponse> => {
  try {
    const body = {
      triggerName,
    };
    const response = await axios.post<TriggerActionResponse>(`${URL}/stop-trigger`, body);
    return response.data;
  } catch (error) {
    console.error("Error stopping trigger:", error);
    throw error;
  }
};
