import axios from "axios";
import { Config, filterList, Store } from "../Types/store-types";
import { da } from "date-fns/locale";
// import { Store, Config, filterList } from "../types";

const URL = import.meta.env.VITE_API_URL;
const API_URL = `${URL}/get-stores`;
const PUT_URL = `${URL}/stores`;
const POST_URL = `${URL}/stores`;
const SEARCH_URL = `${URL}/stores/search`;
const CONFIG_URL = `${URL}/config`;
const TABLE_DATA_URL = `${URL}/get-table-data`;
const STORES_NEW_URL = `${URL}/stores`;
const CHECK_VALUE_URL = `${URL}/check-value-exists`

interface TableData {
	result: any[];
}

export const fetchStores = async (
	page: number,
	limit: number,
	filters?: filterList,
	sortBy?: string | null,
	sortOrder?: 'asc' | 'desc' | null
) => {
	try {
		const response = await axios.post(API_URL, {
			page,
			limit,
			filters: filters ?? {},
			sortBy,
			sortOrder
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching stores:", error);
		throw error;
	}
};

export const fetchConfig = async (): Promise<Config> => {
	try {
		const response = await axios.get(CONFIG_URL);
		return response.data.config;
	} catch (error) {
		console.error("Error fetching config:", error);
		throw error;
	}
};

export const searchStores = async (searchText: string): Promise<Store[]> => {
	try {
		const response = await axios.post(SEARCH_URL, { searchText });
		return response.data.results;
	} catch (error) {
		console.error("Error searching stores:", error);
		throw error;
	}
};

export const addStore = async (storeData: Partial<Store>): Promise<void> => {
	try {
		await axios.post(POST_URL, storeData);
	} catch (error) {
		console.error("Error adding store:", error);
		throw error;
	}
};

export const updateStore = async (storeData: Partial<Store>): Promise<void> => {
	try {
		await axios.put(PUT_URL, storeData);
	} catch (error) {
		console.error("Error updating store:", error);
		throw error;
	}
};

export const updateStoreNew = async (data: { data: Record<string, Record<string, any>>; storeId: number }): Promise<void> => {
	try {
		await axios.put(STORES_NEW_URL, data);
	} catch (error) {
		console.error("Error updating store with new data:", error);
		throw error;
	}
};



export const fetchTableData = async (tableName: string, storeId: number): Promise<TableData> => {
	try {
		const response = await axios.post(TABLE_DATA_URL, { tableName, id: storeId });
		return response.data.result;
	} catch (error) {
		console.error("Error fetching table data:", error);
		throw error;
	}
};

// Save New Store API
type StoreEntry = Record<string, Record<string, any>>
export const addNewStoreData = async (data: StoreEntry) => {
	try {
		const response = await axios.post(STORES_NEW_URL, { data });
		return response.data
	} catch (error) {
		console.error("Error saving new store data", error);
		throw error;
	}
}

export interface StoreExistingValueResponse {
	exists: boolean,
	tableName: string,
	columnName: string,
	value: string,
}

export const checkExistingValueInStore = async (tableName: string, columnName: string, value: string, schemaName: string, isEDW = false): Promise<StoreExistingValueResponse> => {
	try {
		const response = await axios.post(CHECK_VALUE_URL, {
			schemaName,
			tableName,
			columnName,
			value,
			isEDW
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching existing value status:", error);
		throw error;
	}
}