import axios from "axios";

const baseURL = import.meta.env.VITE_API_URL;
const GET_GL_Discount = `${baseURL}/get-revel-dim-discount-category`;
const UPDATE_GL_Discount = `${baseURL}/update-revel-discount-gl-code`;
const ADD_GL_Discount = `${baseURL}/add-revel-discount-gl-code`;
const GET_ALL_GL_DISCOUNTS = `${baseURL}/get-revel-gl-discounts`;

export interface UpdateGlRecordRequest {
  glAccountNo?: string;
  newDiscountName?: string;
  newGlAccountNo?: string;
  source?: string;
  id?: number;
  is_active?: boolean;
}

export const getGLDiscountData = async () => {
  try {
    const response = await axios.get(GET_GL_Discount);
    return response.data.result;
  } catch (error) {
    console.error("Error fetching revel discount data: ", error);
    throw error;
  }
};

export const updateGLDiscountData = async (param: UpdateGlRecordRequest) => {
  try {
    const response = await axios.put(UPDATE_GL_Discount, param);
    return response.data.result;
  } catch (error) {
    console.error("Error fetching revel discount data: ", error);
    throw error;
  }
};

interface GLDiscountRecord {
  discount_name: string | null;
  gl_account_no: string | null;
  is_active: boolean | null;
  source: string | null;
  id: number | null;
  update_date: string | null;
}

export const addGLDiscountData = async (params: GLDiscountRecord[]) => {
  try {
    // Input validation
    if (!Array.isArray(params)) {
      throw new Error("Input must be an array of GL discount records");
    }

    const cleanedParams = params.map(({ id, update_date, ...rest }) => rest);

    // Validate each record in the array
    cleanedParams.forEach((record, index) => {
      if (!record.discount_name || !record.gl_account_no || record.source === undefined) {
        throw new Error(`Invalid record at index ${index}: Missing required fields`);
      }
    });

    const response = await axios.post(ADD_GL_Discount, cleanedParams);

    return response.data.result;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error("Error adding GL discount records:", {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
      });
      throw new Error(`Failed to add GL discount records: ${error.message}`);
    }

    console.error("Error adding GL discount records:", error);
    throw error;
  }
};

export interface GetAllDiscountsParams {
  page: number;
  limit: number;
  searchColumns?: string[];
  searchText?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export const getAllGLDiscountData = async (params: GetAllDiscountsParams) => {
  try {
    const response = await axios.post(GET_ALL_GL_DISCOUNTS, params);
    return response.data;
  } catch (error) {
    console.error("Error fetching all revel discount data: ", error);
    throw error;
  }
};
