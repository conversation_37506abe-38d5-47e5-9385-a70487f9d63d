import axios from "axios";

const URL = import.meta.env.VITE_API_URL;
const tableViewURL = `${URL}/get-table-view`;
const updateTableURL = `${URL}/update-table-view`;
const insertTableURL = `${URL}/insert-table-view`;

// Constants for table constraints
export const IDENTITY_KEYS: Record<string, string[]> = {
  str_geography: ["geographyId", "validFrom", "validTo"],
  str_store: ["storeId", "validFrom", "validTo"],
  emp_employee: ["employeeId", "validFrom", "validTo"],
  str_district: ["districtid", "validFrom", "validTo"],
  str_market: ["marketId", "validFrom", "validTo"],
  str_region: ["regionid", "validFrom", "validTo"],
  str_regionDistrict: ["regionDistrictId", "validFrom", "validTo"],
  str_storeConcept: ["storeConceptId", "validFrom", "validTo"],
  str_site: ["siteId", "validFrom", "validTo"],
  emp_job: ["jobId", "validFrom", "validTo"],
  emp_department: ["departmentId", "validFrom", "validTo"],
};

export const PRIMARY_KEYS: Record<string, string[]> = {
  str_geography: ["geographyId", "validFrom", "validTo"],
  str_store: [
    "storeId",
    "siteId",
    "storeNumber",
    "storeName",
    // "districtId",
    "storeConceptId",
    "marketId",
    "geographyId",
    "legacyStoreCode",
    "legacyStoreId",
    "validFrom",
    "validTo",
  ],
  emp_employee: ["employeeId", "employeeNumber", "validFrom", "validTo"],
  str_district: ["districtid", "validFrom", "validTo"],
  str_market: ["marketId", "validFrom", "validTo"],
  str_region: ["regionid", "validFrom", "validTo"],
  str_regionDistrict: ["regionDistrictId", "validFrom", "validTo"],
  str_storeConcept: ["storeConceptId", "validFrom", "validTo"],
  str_site: ["siteId", "siteName", "validFrom", "validTo"],
  emp_job: ["jobId", "validFrom", "validTo"],
  emp_department: ["departmentId", "validFrom", "validTo"],
};

export const NOT_NULL: Record<string, string[]> = {
  str_geography: ["geographyName"],
  str_store: ["companyId", "siteId", "posId", "loyaltyId", "storeConceptId", "storeNumber", "storeName", "showReporting", "compareDate", "isClosed"],
  emp_employee: ["companyId", "employeeNumber", "firstName", "lastName", "jobId", "isActive", "managerEmployeeNumber"],
  str_district: ["districtName", "employeeId"],
  str_market: ["marketName"],
  str_region: ["regionName", "employeeId"],
  str_regionDistrict: ["regionId", "districtId"],
  str_storeConcept: ["storeConceptName"],
  str_site: ["siteAddress1", "city", "state", "stateAbbreviation", "zip", "siteName"],
  emp_job: ["jobDescription"],
  emp_department: ["departmentName"],
};

export const fetchStoreMemberTableData = async (
  page: number,
  limit: number,
  tableName: string,
  searchText?: string | null,
  searchColumns?: string[] | null
) => {
  try {
    const response = await axios.post(tableViewURL, {
      page,
      limit,
      tableName,
      searchText,
      searchColumns,
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching stores:", error);
    throw error;
  }
};

export const updateTableData = async (tableName: string, data: Record<string, unknown>, primaryKeyColumn: string, primaryKeyValue: number | string) => {
  try {
    const response = await axios.put(updateTableURL, {
      tableName,
      data,
      primaryKeyColumn,
      primaryKeyValue,
    });
    return response.data;
  } catch (error) {
    console.error("Error updating table:", error);
    throw error;
  }
};

export const insertTableData = async (tableName: string, data: Record<string, unknown>) => {
  try {
    const response = await axios.post(insertTableURL, {
      tableName,
      data,
    });
    return response.data;
  } catch (error) {
    console.error("Error inserting data:", error);
    throw error;
  }
};
