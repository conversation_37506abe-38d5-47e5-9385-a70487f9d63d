import axios from "axios";
import { ValidationDataFilterParam, ValidRecordItem } from "../../__redux/validationDataSlice";

const URL = import.meta.env.VITE_API_URL;
const VALID_DATA_URL = `${URL}/get-data-validation`;
const GET_REVEL_DISCOUNT_URL = `${URL}/get-revel-dim-discount-category`;
const CONFIG_URL = `${URL}/data-validation/config`;

export interface ValidationDataListResponse {
    limit: number;
    currentPage: number;
    totalPages: number;
    totalCount: number;
    result: ValidRecordItem[];
}

export interface VDFilterParamRequest {
    storeName: string | null;
	businessDate: string | null;
}

export interface VDConfig {
    stores: string[];
}

export const fetchValidationData = async (page: number, limit: number, filterParams: VDFilterParamRequest): Promise<ValidationDataListResponse> => {
    const filters: Record<string, string> = {}
    if (filterParams.storeName) filters.store = filterParams.storeName;
	if (filterParams.businessDate) filters.businessDate = filterParams.businessDate;
    let body = {};
	body = {...body, page, limit};
    if (Object.keys(filters).length !== 0) body = {...body, filters};
    try {
        const response = await axios.post(VALID_DATA_URL, body);
        return response.data;
    } catch (error) {
        console.error("Error fetching validation data: ", error);
        throw error;
    }
}

export const fetchVDConfig = async (): Promise<VDConfig> => {
	try {
		const response = await axios.get(CONFIG_URL);
		return response.data.config;
	} catch (error) {
		console.error("Error fetching config:", error);
		throw error;
	}
};