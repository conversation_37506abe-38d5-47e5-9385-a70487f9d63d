import axios from "axios";

const URL = import.meta.env.VITE_API_URL;
const GET_COHORT_REPORT_URL = `${URL}/get-cohort-report`
const GET_GEOGRAPHY_REPORT_URL = `${URL}/get-geography-report`
const GET_REGION_REPORT_URL = `${URL}/get-region-report`
const GET_DISTRICT_REPORT_URL = `${URL}/get-district-report`
const GET_SEGMENT_REPORT_URL = `${URL}/get-segment-report`

export enum TyReportEndPoint {
	cohort =  "get-cohort-ty-report",
	region =  "get-region-ty-report",
	segment =  "get-segment-ty-report",
	geography =  "get-geography-ty-report",
	regionDistrict =  "get-region-district-ty-report",
}

export enum LyReportEndPoint {
	cohort =  "get-cohort-ly-report",
	region =  "get-region-ly-report",
	segment =  "get-segment-ly-report",
	geography =  "get-geography-ly-report",
	regionDistrict =  "get-region-district-ly-report",
}

export enum LyYtdReportEndPoint {
	cohort =  "get-cohort-ly-ytd-report",
	region =  "get-region-ly-ytd-report",
	segment =  "get-segment-ly-ytd-report",
	geography =  "get-geography-ly-ytd-report",
	regionDistrict =  "get-region-district-ly-ytd-report",
}

export enum LyPtdReportEndPoint {
	cohort =  "get-cohort-ly-ptd-report",
	region =  "get-region-ly-ptd-report",
	segment =  "get-segment-ly-ptd-report",
	geography =  "get-geography-ly-ptd-report",
	regionDistrict =  "get-region-district-ly-ptd-report",
}


export const getCohortReport = async () => {
	try {
		const response = await axios.post(GET_COHORT_REPORT_URL);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getGeographyReport = async () => {
	try {
		const response = await axios.post(GET_GEOGRAPHY_REPORT_URL);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}


export const getRegionReport = async () => {
	try {
		const response = await axios.post(GET_REGION_REPORT_URL);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getDistrictReport = async () => {
	try {
		const response = await axios.post(GET_DISTRICT_REPORT_URL);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getSegmentReport = async () => {
	try {
		const response = await axios.post(GET_SEGMENT_REPORT_URL);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getTYPtdYtdReport = async (type: string, endPoint: TyReportEndPoint) => {
	try {
		let body = {}
		body = {...body, type}
		const response = await axios.post(`${URL}/${endPoint}`, body);
		return response.data;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getLYWtdReport = async (type: string, endPoint: LyReportEndPoint) => {
	try {
		let body = {}
		body = {...body, type}
		const response = await axios.post(`${URL}/${endPoint}`, body);
		return response.data.result;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getLYPtdReport = async (type: string, endPoint: LyPtdReportEndPoint) => {
	try {
		let body = {}
		body = {...body, type}
		const response = await axios.post(`${URL}/${endPoint}`, body);
		return response.data.result;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}

export const getLYYtdReport = async (type: string, endPoint: LyYtdReportEndPoint) => {
	try {
		let body = {}
		body = {...body, type}
		const response = await axios.post(`${URL}/${endPoint}`, body);
		return response.data.result;
	} catch (error) {
		console.error("Error fetching pipeline list:", error);
		throw error;
	}
}