import axios from "axios";
import { LogsFilterParam, AuditLogObject } from "../../__redux/auditLogSlice";

const URL = import.meta.env.VITE_API_URL;
const GET_AUDIT_LOG_URL = `${URL}/get-audit-logs`;

export interface AuditLogListResponse {
	totalCount: number;
	currentPage: number;
	totalPages: number;
	hasMoreResults: boolean;
	pageSize: number;
	result: AuditLogObject[];
	filters: LogsFilterParam;
}

export const fetchAuditLogs = async (
	pageSize: number,
	pageNumber: number,
	filters: LogsFilterParam
): Promise<AuditLogListResponse> => {
	try {
		const body = {
			pageSize,
			pageNumber,
			searchTerm: filters.searchTerm
		};
		const response = await axios.post(GET_AUDIT_LOG_URL, body);
		return response.data;
	} catch (error) {
		console.error(error);
		return {
			totalCount: 0,
			currentPage: 1,
			totalPages: 0,
			hasMoreResults: false,
			pageSize: 0,
			result: [],
			filters: {
				searchTerm: null
			}
		};
	}
};