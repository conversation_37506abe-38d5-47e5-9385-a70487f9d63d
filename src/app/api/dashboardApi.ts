import axios from "axios";

const URL = import.meta.env.VITE_API_URL;
const PIPELINE_LIST_URL = `${URL}/get-pipeline-list`;
const PIPELINE_RUNS_URL = `${URL}/pipeline-runs`;
const PIPELINE_STATUS_URL = `${URL}/pipeline-status`;
const TRIGGER_PIPELINE_URL = `${URL}/trigger-pipeline`;
const ALL_PIPELINE_STATUSES_URL = `${URL}/all-pipeline-statuses`;

export const getAllPipelineStatuses = async () => {
  try {
    const response = await axios.post(ALL_PIPELINE_STATUSES_URL);
    return response.data;
  } catch (error) {
    console.error("Error fetching all pipeline statuses:", error);
    throw error;
  }
};

export const getPipelineList = async (page = 1, limit = 9, search = "") => {
  try {
    const body = {
      page,
      limit,
      search,
    };
    const response = await axios.post(PIPELINE_LIST_URL, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline list:", error);
    throw error;
  }
};

export const getPipelineRuns = async (
  pipelineName: string,
  updatedAfter = new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
  updatedBefore = new Date()
) => {
  try {
    const body = {
      pipelineName,
      updatedAfter,
      updatedBefore,
    };
    const response = await axios.post(PIPELINE_RUNS_URL, body);
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline runs:", error);
    throw error;
  }
};

export const getPipelineStatus = async (runId: string) => {
  try {
    const response = await axios.post(PIPELINE_STATUS_URL, { runId });
    return response.data;
  } catch (error) {
    console.error("Error fetching pipeline status:", error);
    throw error;
  }
};

export const triggerPipeline = async (pipelineName: string) => {
  try {
    const response = await axios.post(TRIGGER_PIPELINE_URL, { pipelineName });
    return response.data;
  } catch (error) {
    console.error("Error triggering pipeline:", error);
    throw error;
  }
};
