import axios from "axios";
import { FiscalCalendarConfig, FiscalCalendarFilterParam } from "../Types/fiscal-calendar-types";

const URL = import.meta.env.VITE_API_URL;
const GET_API_URL = `${URL}/get-fiscal-calendar`
const SEARCH_API_URL = `${URL}/fiscal-calendar/search`
const CONFIG_URL = `${URL}/fiscal-calendar/config`
const UPDATE_URL = `${URL}/update-fiscal-calendar`
const ADD_MANUAL_URL = `${URL}/add-fiscal-calendar`
const BULK_UPLOAD_URL = `${URL}/bulk-upload-fiscal-calendar`
const MISSING_DATE_URL = `${URL}/fiscal-calendar/check-missing-dates`




export const fetchFiscalCalendar = async (
	page: number,
	limit: number,
	filterParams: FiscalCalendarFilterParam,
	sortBy?: string | null,
	sortOrder?: 'asc' | 'desc' | null
) => {
	try {
		const filters: Record<string, string> = {}
		if (filterParams.fiscalPeriodNumber) filters.fiscalPeriodNumber = filterParams.fiscalPeriodNumber;
		if (filterParams.fiscalWeekNumber) filters.fiscalWeekNumber = filterParams.fiscalWeekNumber;
		if (filterParams.fiscalQuarterNumber) filters.fiscalQuarterNumber = filterParams.fiscalQuarterNumber;
		if (filterParams.fiscalYear) filters.fiscalYear = filterParams.fiscalYear;
		
		let body = {};
		body = {...body, page, limit, sortBy, sortOrder}
		if (Object.keys(filters).length !== 0) {
			body = { ...body, filters };
		}

		const response = await axios.post(GET_API_URL, body);
		return response.data;
	} catch (error) {
		console.error(error);
		return {
			result: [],
			totalCount: 0,
			totalPages: 0,
			currentPage: 0,
			count: 0,
		}
	}
};

export const searchFiscalCalendar = async (searchText: string) => {
	try {
		const response = await axios.post(SEARCH_API_URL, { searchText });
		return response.data;
	} catch (error) {
		console.error(error);
		return [];
	}
};

export const fetchFiscalCalendarConfigData = async (): Promise<FiscalCalendarConfig> =>  {
	try {
		const response = await axios.get(CONFIG_URL);
		return response.data.config
	} catch (error) {
		console.error("Error fetching config:", error);
		throw error;
	}
}

export interface UpdateCalendarRecordParams {
	fiscalCalendarId: number,
	filters: CalendarUpdatedField[]
}
interface CalendarUpdatedField {
	field: string,
	value: any
}

export const updateCalendarRecord = async (params: UpdateCalendarRecordParams) => {
	try {
		const response = await axios.put(UPDATE_URL, params );
		return response.data;
	} catch (error) {
		console.error(error);
		throw error;
	}
};

export const addCalendarRecord = async (params: Record<string, any>) => {
	try {
		const response = await axios.post(ADD_MANUAL_URL, params );
		return response.data;
	} catch (error) {
		console.error(error);
		throw error;
	}
};

export const bulkUploadCalendarRecord = async (file: File) => {
	const formData = new FormData();
    formData.append('file', file);
	try {
		const response = await axios.post(BULK_UPLOAD_URL, formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		} );
		return response.data;
	} catch (error) {
		console.error(error);
		throw error;
	}
};

export const fetchMissingDates = async (startDate: string, endDate: string) => {
	const body = { 
		startDate: startDate,
		endDate: endDate
	}
	try {
		const response = await axios.post(MISSING_DATE_URL, body );
		return response.data.result;
	} catch (error) {
		console.error(error);
		throw error;
	}
};