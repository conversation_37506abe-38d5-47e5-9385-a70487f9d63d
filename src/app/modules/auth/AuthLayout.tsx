import { useEffect } from "react";
import { Outlet } from "react-router-dom";
import { toAbsoluteUrl } from "../../../_metronic/helpers";
const AuthLayout = () => {
  useEffect(() => {
    const root = document.getElementById("root");
    if (root) {
      root.style.height = "100%";
    }
    return () => {
      if (root) {
        root.style.height = "auto";
      }
    };
  }, []);

  return (
    <div className="d-flex flex-column flex-lg-row flex-column-fluid h-100">
      <div className="d-flex flex-column flex-lg-row-fluid flex-shrink-1 p-10 order-2 order-lg-1">
        <div className="d-flex flex-center flex-column flex-lg-row-fluid">
          <div className="text-center mb-11 d-flex align-items-center gap-5">
            <span style={{ fontSize: 65 }}>🍕</span>
            <div style={{ position: "relative" }}>
              <img
                src={toAbsoluteUrl("media/lou/lou-malnatis-logo-clear.png")}
                alt="<PERSON>'s Logo"
                style={{ width: "100%", height: "8rem", objectFit: "cover" }}
              />
            </div>
          </div>
          <div className="w-lg-500px p-10">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export { AuthLayout };

{
  /* <div
className="d-flex flex-lg-row-fluid w-75 bgi-size-cover order-1 order-lg-2 bgi-position-center m-10 rounded"
style={{
  backgroundSize: "cover",
  backgroundImage: `url(${toAbsoluteUrl("media/lou/lou_malnati_background.jpg")})`,
}}
></div> */
}
