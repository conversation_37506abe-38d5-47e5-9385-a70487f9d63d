import { useMsal } from "@azure/msal-react";
import { Alert } from "@mui/material";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { saveAuth } from "../../../../__redux/authSlice";
import { toAbsoluteUrl } from "../../../../_metronic/helpers";
import { AppDispatch, RootState } from "../../../store";
import axios from "axios";
import { Role } from "../../../constants/constants";
import { checkTokenExpiration } from "../core/AuthHelpers";

export function Login() {
  const { instance } = useMsal();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && auth.apiToken) {
        checkTokenExpiration(dispatch, auth.tokenExpirationTime);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [dispatch, auth.apiToken, auth.tokenExpirationTime]);

  const handleLogin = () => {
    setLoading(true);
    const loginRequest = {
      scopes: [import.meta.env.VITE_APP_FUNC_EXPOSED_API],
    };

    instance
      .loginPopup(loginRequest)
      .then(response => {
        if (response.accessToken) {
          const userRole = (response?.idTokenClaims as { roles: string[] })?.roles ?? Role.Default;
          const userAccount = response?.account;
          axios.defaults.headers.common["Authorization"] = `Bearer ${response.accessToken}`;
          const expiresIn = response.expiresOn ? (response.expiresOn.getTime() - Date.now()) / 1000 : 10; // Use 10 seconds if expiresOn is not available
          // const expiresIn = 10; // Use 10 seconds if expiresOn is not available
          console.log("User Role: ", userRole);
          dispatch(
            saveAuth({
              apiToken: response.accessToken,
              userName: userAccount?.name as string,
              email: userAccount?.username as string,
              userRole: userRole,
              expiresIn: expiresIn,
            })
          );
          navigate("/dashboard");
        } else {
          setError("No access token received");
        }
      })
      .catch(error => {
        console.error("Login failed", error);
        setError("Login failed: " + error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="d-flex flex-column align-items-center justify-content-center min-vh-100 p-4">
      <p className="text-gray-500">Please sign in to continue</p>

      {/* Login Card */}
      <div className="card w-100 max-w-450px shadow-sm">
        <div className="card-body p-8">
          {error && (
            <Alert severity="error" sx={{ width: "100%", mb: 4 }}>
              {error}
            </Alert>
          )}

          <button
            onClick={handleLogin}
            className="btn btn-lg w-100 d-flex align-items-center justify-content-center gap-3"
            style={{
              backgroundColor: "#0078D4",
              color: "white",
              border: "none",
              padding: "1rem",
              borderRadius: "8px",
              transition: "all 0.2s ease",
              cursor: loading ? "not-allowed" : "pointer",
              opacity: loading ? 0.7 : 1,
            }}
            disabled={loading}
          >
            {!loading ? (
              <>
                <img alt="Azure Logo" src={toAbsoluteUrl("media/misc/azure.png")} className="h-40px me-2" />
                <span className="fs-5 fw-bold">Sign in with Microsoft Azure</span>
              </>
            ) : (
              <>
                <span className="spinner-border spinner-border-sm" />
                <span className="fs-6 ms-2">Authenticating...</span>
              </>
            )}
          </button>

          <div className="text-center mt-6">
            <p className="text-gray-600 fs-7">
              Having trouble signing in? Contact{" "}
              <a href="mailto:<EMAIL>?subject=Issue with LOU Web App - UI" className="text-primary">
                Support
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-8">
        <p className="text-gray-500 fs-8">© {new Date().getFullYear()} Lou Malnati's. All rights reserved.</p>
      </div>
    </div>
  );
}
