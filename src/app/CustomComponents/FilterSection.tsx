import React from "react";
import { Autocomplete, Box, Button, Chip, Collapse, Grid, TextField, Typography, MenuItem, Select, FormControl, InputLabel } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Config } from "../Types/store-types";

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: theme.shape.borderRadius * 2,
    transition: theme.transitions.create(["box-shadow"]),
    "&:hover": {
      boxShadow: "0 0 0 2px rgba(0, 122, 255, 0.2)",
    },
    "&.Mui-focused": {
      boxShadow: "0 0 0 3px rgba(0, 122, 255, 0.3)",
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  textTransform: "none",
  fontWeight: 600,
  boxShadow: "none",
  "&:hover": {
    boxShadow: "0 4px 12px rgba(0, 122, 255, 0.3)",
  },
}));

interface FilterSectionProps {
  showFilters: boolean;
  config: Config;
  selectedGeographies: string[];
  selectedRegions: string[];
  selectedDistricts: string[];
  selectedMarkets: string[];
  selectedStoreConcepts: string[];
  selectedStoreNames: string[];
  selectedIsClosed: string;
  setSelectedGeographies: (value: string[]) => void;
  setSelectedRegions: (value: string[]) => void;
  setSelectedDistricts: (value: string[]) => void;
  setSelectedMarkets: (value: string[]) => void;
  setSelectedStoreConcepts: (value: string[]) => void;
  setSelectedStoreNames: (value: string[]) => void;
  setSelectedIsClosed: (value: string) => void;
  handleResetFilter: () => void;
  handleApplyFilter: () => void;
}
const FilterSection: React.FC<FilterSectionProps> = ({
  showFilters,
  config,
  selectedGeographies,
  selectedRegions,
  selectedDistricts,
  selectedMarkets,
  selectedStoreConcepts,
  selectedStoreNames,
  selectedIsClosed,
  setSelectedGeographies,
  setSelectedRegions,
  setSelectedDistricts,
  setSelectedMarkets,
  setSelectedStoreConcepts,
  setSelectedStoreNames,
  setSelectedIsClosed,
  handleResetFilter,
  handleApplyFilter,
}) => {
  const renderFilterChips = () => {
    const activeFilters = [
      ...selectedGeographies,
      ...selectedRegions,
      ...selectedDistricts,
      ...selectedMarkets,
      ...selectedStoreConcepts,
      ...selectedStoreNames,
      selectedIsClosed === "true" ? "Is Closed" : "Is Open",
    ];

    return activeFilters.map(filter => <Chip key={filter} label={filter} color="primary" variant="outlined" size="medium" sx={{ m: 0.5, fontSize: "1rem" }} />);
  };

  return (
    <>
      <Collapse in={showFilters}>
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.geographyName || []}
                value={selectedGeographies}
                onChange={(_, newValue) => setSelectedGeographies(newValue)}
                renderInput={params => <StyledTextField {...params} label="Geography" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.Region || []}
                value={selectedRegions}
                onChange={(_, newValue) => setSelectedRegions(newValue)}
                renderInput={params => <StyledTextField {...params} label="Region" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.District || []}
                value={selectedDistricts}
                onChange={(_, newValue) => setSelectedDistricts(newValue)}
                renderInput={params => <StyledTextField {...params} label="District" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.marketName || []}
                value={selectedMarkets}
                onChange={(_, newValue) => setSelectedMarkets(newValue)}
                renderInput={params => <StyledTextField {...params} label="Market" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.storeConceptName || []}
                value={selectedStoreConcepts}
                onChange={(_, newValue) => setSelectedStoreConcepts(newValue)}
                renderInput={params => <StyledTextField {...params} label="Store Concept" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                multiple
                options={config.storeName || []}
                value={selectedStoreNames}
                onChange={(_, newValue) => setSelectedStoreNames(newValue)}
                renderInput={params => <StyledTextField {...params} label="Store Name" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Is Closed</InputLabel>
                <Select
                  value={selectedIsClosed}
                  onChange={e => {
                    const value = e.target.value;
                    console.log(value);
                    if (value === "true") {
                      setSelectedIsClosed("true");
                    } else if (value === "false") {
                      setSelectedIsClosed("false");
                    } else {
                      setSelectedIsClosed("");
                    }
                  }}
                  label="Is Closed"
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  <MenuItem value="true">Yes</MenuItem>
                  <MenuItem value="false">No</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <StyledButton variant="outlined" color="secondary" onClick={handleResetFilter}>
              Reset Filters
            </StyledButton>
            <StyledButton variant="contained" color="primary" onClick={handleApplyFilter}>
              Apply Filters
            </StyledButton>
          </Box>
        </Box>
      </Collapse>

      {(selectedGeographies.length > 0 ||
        selectedRegions.length > 0 ||
        selectedDistricts.length > 0 ||
        selectedMarkets.length > 0 ||
        selectedStoreConcepts.length > 0 ||
        selectedStoreNames.length > 0 ||
        selectedIsClosed.length > 0) && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Active Filters:
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap" }}>{renderFilterChips()}</Box>
        </Box>
      )}
    </>
  );
};

export default FilterSection;
