import { Button, Paper, styled, TextField } from "@mui/material";

export const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  background: "rgba(255, 255, 255, 0.8)",
  backdropFilter: "blur(10px)",
  borderRadius: theme.shape.borderRadius * 2,
}));

export const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: theme.shape.borderRadius * 2,
    transition: theme.transitions.create(["box-shadow"]),
    "&:hover": {
      boxShadow: "0 0 0 2px rgba(0, 122, 255, 0.2)",
    },
    "&.Mui-focused": {
      boxShadow: "0 0 0 3px rgba(0, 122, 255, 0.3)",
    },
  },
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  textTransform: "none",
  fontWeight: 600,
  boxShadow: "none",
  "&:hover": {
    boxShadow: "0 4px 12px rgba(0, 122, 255, 0.3)",
  },
}));
