import EditIcon from "@mui/icons-material/Edit";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import KeyIcon from "@mui/icons-material/Key";
import LockIcon from "@mui/icons-material/Lock";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Skeleton,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  // TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { alpha, styled } from "@mui/material/styles";
import moment, { MomentInput } from "moment-timezone";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { IDENTITY_KEYS, NOT_NULL, PRIMARY_KEYS, updateTableData } from "../api/storeMemberTableAPI";
import { RootState } from "../store";
import { AddStoreFormFieldType } from "../utils/formFieldTypes";
import { AddStoreFormObjectRule } from "../utils/formObjectRules";
import { isStorePageAdmin } from "../utils/roleUtils";

// const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
//   maxHeight: "calc(100vh - 350px)",
//   "&::-webkit-scrollbar": {
//     width: "8px",
//     height: "8px",
//   },
//   "&::-webkit-scrollbar-track": {
//     boxShadow: "inset 0 0 6px rgba(0,0,0,0.1)",
//     borderRadius: "10px",
//   },
//   "&::-webkit-scrollbar-thumb": {
//     backgroundColor: alpha(theme.palette.primary.main, 0.3),
//     borderRadius: "10px",
//     "&:hover": {
//       backgroundColor: alpha(theme.palette.primary.main, 0.6),
//     },
//   },
// }));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.mode === "dark" ? alpha(theme.palette.background.paper, 0.9) : theme.palette.primary.light,
  color: theme.palette.mode === "dark" ? theme.palette.primary.contrastText : theme.palette.primary.contrastText,
  whiteSpace: "nowrap",
  padding: theme.spacing(2),
  fontSize: "1.1rem",
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  position: "sticky",
  top: 0,
  zIndex: 10,
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
  },
  "&:hover": {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    transition: theme.transitions.create("background-color", {
      duration: theme.transitions.duration.shortest,
    }),
  },
}));

interface StoreTableProps {
  tableData: Record<string, string>[];
  loading: boolean;
  limit: number;
  Header: string[];
  HeaderKeys: string[];
  fieldTypes?: Record<string, string>;
  tableName?: string;
  primaryKeyColumn?: string;
  onDataUpdate?: () => void;
  readOnly?: boolean;
}

const StoreMemberTable: React.FC<StoreTableProps> = ({
  tableData,
  loading,
  limit,
  Header = [],
  HeaderKeys = [],
  fieldTypes = {},
  tableName = "",
  primaryKeyColumn = "id",
  onDataUpdate,
  readOnly = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [open, setOpen] = useState(false);
  const { userRole } = useSelector((state: RootState) => state.auth);
  const [selectedItem, setSelectedItem] = useState<Record<string, string> | null>(null);
  const [editedItem, setEditedItem] = useState<Record<string, string> | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error" | "info" | "warning",
  });

  const isAdmin = isStorePageAdmin(userRole);

  console.log("!readOnly", readOnly);

  useEffect(() => {
    if (selectedItem) {
      setEditedItem({ ...selectedItem });
    }
  }, [selectedItem]);

  const handleClose = () => {
    setOpen(false);
    setIsEditing(false);
    setEditedItem(null);
    setSelectedItem(null);
    setValidationErrors({});
  };

  const handleOpen = (item: Record<string, string>) => {
    setSelectedItem(item);
    setOpen(true);
  };

  const handleEdit = () => {
    if (readOnly) return;
    setIsEditing(true);
    setValidationErrors({});
  };

  const handleInputChange = (key: string, value: string) => {
    if (!editedItem) return;

    // Clear validation error when user changes the value
    if (validationErrors[key]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }

    setEditedItem({
      ...editedItem,
      [key]: value,
    });
  };

  const validateFields = (): boolean => {
    if (!editedItem || !tableName) return false;

    const errors: Record<string, string> = {};

    // Check non-nullable fields
    const requiredFields = NOT_NULL[tableName] || [];
    requiredFields.forEach(field => {
      const value = editedItem[field];
      // Check for null, undefined, or empty string
      if (value === null || value === undefined || value === "") {
        errors[field] = "This field is required";
      }
    });

    // Validate primary keys haven't changed
    const primaryKeys = PRIMARY_KEYS[tableName] || [];
    if (selectedItem) {
      primaryKeys.forEach(key => {
        if (selectedItem[key] && editedItem[key] !== selectedItem[key]) {
          errors[key] = "Primary key cannot be changed";
        }
      });
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!editedItem || !selectedItem || !tableName || !primaryKeyColumn) return;

    // Validate before saving
    if (!validateFields()) {
      setNotification({
        open: true,
        message: "Please fix validation errors before saving",
        severity: "error",
      });
      return;
    }

    try {
      setIsSaving(true);
      const primaryKeyValue = selectedItem[primaryKeyColumn];

      // Convert editedItem to the format expected by the API
      await updateTableData(tableName, editedItem, primaryKeyColumn, primaryKeyValue);

      setNotification({
        open: true,
        message: "Changes saved successfully",
        severity: "success",
      });

      setIsEditing(false);

      // Refresh data if callback provided
      if (onDataUpdate) {
        onDataUpdate();
      }
    } catch (error) {
      console.error("Error saving changes:", error);
      setNotification({
        open: true,
        message: "Failed to save changes",
        severity: "error",
      });
    } finally {
      setIsSaving(false);
      handleClose();
    }
  };

  type Value = string | number | null | boolean | Date;
  const formatValue = (value: Value, field: string): string => {
    if (value === null) return "-";

    if (["upcomingCompDate", "validTo", "validFrom", "createDate", "compareDate", "openDate", "closedDate", "hireDate"].includes(field)) {
      const date = moment(value as MomentInput);
      if (date.isValid()) {
        return date.tz("America/Chicago").format("MM/DD/YYYY");
      }
    }

    return String(value);
  };

  // Get field type from rules or props
  const getFieldType = (key: string): string => {
    // First check if it exists in AddStoreFormObjectRule
    if (AddStoreFormObjectRule[key]) {
      return AddStoreFormObjectRule[key].fieldType;
    }
    // Fall back to fieldTypes prop
    return fieldTypes[key] || "";
  };

  // Check if a field is an identity key
  const isIdentityField = (key: string): boolean => {
    if (!tableName) return false;
    return IDENTITY_KEYS[tableName]?.includes(key) || false;
  };

  // Check if a field is a primary key
  const isPrimaryKey = (key: string): boolean => {
    if (!tableName) return false;
    return PRIMARY_KEYS[tableName]?.includes(key) || false;
  };

  // Check if a field is required (non-nullable)
  const isRequiredField = (key: string): boolean => {
    if (!tableName) return false;
    return NOT_NULL[tableName]?.includes(key) || false;
  };

  const renderEditField = (key: string, value: string) => {
    const fieldType = getFieldType(key);
    const isIdentity = isIdentityField(key);
    const isPrimary = isPrimaryKey(key);
    const isRequired = isRequiredField(key);
    const hasError = !!validationErrors[key];

    // Identity fields and primary keys should be read-only
    const isReadOnly = isIdentity || isPrimary;

    let fieldIcon = null;
    if (isIdentity) {
      fieldIcon = (
        <Tooltip title="Auto-generated ID (Identity)">
          <LockIcon color="secondary" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    } else if (isPrimary) {
      fieldIcon = (
        <Tooltip title="Primary Key">
          <KeyIcon color="primary" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    }

    const labelText = (
      <Box sx={{ display: "flex", alignItems: "center" }}>
        {formatFieldKey(key)}
        {fieldIcon}
        {isRequired && !isReadOnly && (
          <Typography component="span" color="error" sx={{ ml: 0.5 }}>
            *
          </Typography>
        )}
      </Box>
    );

    const commonProps = {
      error: hasError,
      helperText: hasError ? validationErrors[key] : "",
      disabled: isReadOnly || isSaving,
      required: isRequired && !isReadOnly,
      InputProps: isReadOnly ? { readOnly: true } : undefined,
      variant: isReadOnly ? ("filled" as const) : ("outlined" as const),
      sx: {
        backgroundColor: isReadOnly ? alpha(theme.palette.action.disabledBackground, 0.1) : "transparent",
        opacity: isSaving ? 0.7 : 1,
      },
    };

    switch (fieldType) {
      case AddStoreFormFieldType.DATE:
        return (
          <TextField
            fullWidth
            label={labelText}
            type="date"
            value={value ? moment(value).format("YYYY-MM-DD") : ""}
            onChange={e => handleInputChange(key, e.target.value)}
            InputLabelProps={{ shrink: true }}
            {...commonProps}
          />
        );
      case AddStoreFormFieldType.NUMERIC:
        return (
          <TextField fullWidth label={labelText} type="number" value={value || ""} onChange={e => handleInputChange(key, e.target.value)} {...commonProps} />
        );
      case AddStoreFormFieldType.YES_NO:
        return (
          <FormControl
            fullWidth
            error={hasError}
            disabled={isReadOnly || isSaving}
            required={isRequired && !isReadOnly}
            variant={isReadOnly ? "filled" : "outlined"}
            sx={{ opacity: isSaving ? 0.7 : 1 }}
          >
            <InputLabel>{formatFieldKey(key)}</InputLabel>
            <Select value={value} onChange={e => handleInputChange(key, e.target.value)} label={formatFieldKey(key)} readOnly={isReadOnly}>
              <MenuItem value="true">Yes</MenuItem>
              <MenuItem value="false">No</MenuItem>
              <MenuItem value="">-</MenuItem>
            </Select>
            {hasError && (
              <Typography color="error" variant="caption">
                {validationErrors[key]}
              </Typography>
            )}
          </FormControl>
        );
      case AddStoreFormFieldType.EMAIL:
        return (
          <TextField fullWidth label={labelText} type="email" value={value || ""} onChange={e => handleInputChange(key, e.target.value)} {...commonProps} />
        );
      default:
        return <TextField fullWidth label={labelText} value={value || ""} onChange={e => handleInputChange(key, e.target.value)} {...commonProps} />;
    }
  };

  const renderTableContent = () => {
    if (loading) {
      return (
        <TableBody>
          {[...Array(limit)].map((_, index) => (
            <StyledTableRow key={index}>
              <TableCell>
                <Skeleton animation="wave" variant="circular" width={40} height={40} />
              </TableCell>
              {HeaderKeys.map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton animation="wave" />
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }

    if (tableData.length === 0) {
      return (
        <TableBody>
          <StyledTableRow>
            <TableCell colSpan={HeaderKeys.length + 1} align="center">
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <ErrorOutlineIcon color="action" style={{ fontSize: 60, marginBottom: 16 }} />
                <Typography variant="h6" color="textSecondary">
                  No data found
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                  Try adjusting your search criteria or filters
                </Typography>
              </Box>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {tableData.map((item, index) => (
          <StyledTableRow key={index}>
            <TableCell align="center">
              <Tooltip title="View Details">
                <IconButton
                  onClick={() => handleOpen(item)}
                  sx={{
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    "&:hover": {
                      backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    },
                  }}
                >
                  <VisibilityIcon color="primary" />
                </IconButton>
              </Tooltip>
            </TableCell>
            {HeaderKeys.map(header => {
              const value = formatValue(item[header], header) || "-";
              return (
                <TableCell sx={{ fontSize: "1rem" }} key={header}>
                  {value}
                </TableCell>
              );
            })}
          </StyledTableRow>
        ))}
      </TableBody>
    );
  };

  // Helper function to format field keys for display
  const formatFieldKey = (key: string): string => {
    return key
      .replace(/([A-Z])/g, " $1") // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
  };

  const closeNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Render appropriate read-only field based on field type
  const renderReadOnlyField = (key: string, value: string) => {
    const fieldType = getFieldType(key);
    const isIdentity = isIdentityField(key);
    const isPrimary = isPrimaryKey(key);
    const isRequired = isRequiredField(key);

    let fieldIcon = null;
    if (isIdentity) {
      fieldIcon = (
        <Tooltip title="Auto-generated ID (Identity)">
          <LockIcon color="secondary" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    } else if (isPrimary) {
      fieldIcon = (
        <Tooltip title="Primary Key">
          <KeyIcon color="primary" fontSize="small" sx={{ ml: 1 }} />
        </Tooltip>
      );
    }

    const labelText = (
      <Box sx={{ display: "flex", alignItems: "center" }}>
        {formatFieldKey(key)}
        {fieldIcon}
        {isRequired && (
          <Typography component="span" color="error" sx={{ ml: 0.5 }}>
            *
          </Typography>
        )}
      </Box>
    );

    switch (fieldType) {
      case AddStoreFormFieldType.DATE:
        return (
          <TextField
            fullWidth
            label={labelText}
            type="date"
            value={value ? moment(value).format("YYYY-MM-DD") : ""}
            InputProps={{ readOnly: true }}
            InputLabelProps={{ shrink: true }}
            disabled
            variant="filled"
            sx={{ backgroundColor: alpha(theme.palette.background.default, 0.5) }}
          />
        );
      case AddStoreFormFieldType.NUMERIC:
        return (
          <TextField
            fullWidth
            label={labelText}
            type="number"
            value={value || ""}
            InputProps={{ readOnly: true }}
            disabled
            variant="filled"
            sx={{ backgroundColor: alpha(theme.palette.background.default, 0.5) }}
          />
        );
      case AddStoreFormFieldType.YES_NO:
        return (
          <FormControl fullWidth disabled variant="filled" sx={{ backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
            <InputLabel>{formatFieldKey(key)}</InputLabel>
            <Select value={value} readOnly label={formatFieldKey(key)}>
              <MenuItem value="true">Yes</MenuItem>
              <MenuItem value="false">No</MenuItem>
              <MenuItem value="">-</MenuItem>
            </Select>
          </FormControl>
        );
      case AddStoreFormFieldType.EMAIL:
        return (
          <TextField
            fullWidth
            label={labelText}
            type="email"
            value={value || ""}
            InputProps={{ readOnly: true }}
            disabled
            variant="filled"
            sx={{ backgroundColor: alpha(theme.palette.background.default, 0.5) }}
          />
        );
      default:
        return (
          <TextField
            fullWidth
            label={labelText}
            value={value || ""}
            InputProps={{ readOnly: true }}
            disabled
            variant="filled"
            sx={{ backgroundColor: alpha(theme.palette.background.default, 0.5) }}
          />
        );
    }
  };

  return (
    <>
      {/* Enhanced Snackbar for notifications - now with higher z-index and better positioning */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={closeNotification}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          width: isMobile ? "100%" : "auto",
        }}
      >
        <Alert
          onClose={closeNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: "100%",
            boxShadow: 4,
            fontSize: "1rem",
            "& .MuiAlert-icon": {
              fontSize: "1.5rem",
            },
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Table for displaying store data */}
      <Box
        sx={{
          borderRadius: 1,
          overflow: "hidden",
          boxShadow: 3,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        }}
      >
        <Box
          sx={{
            maxHeight: "calc(100vh - 30rem)",
            overflow: "auto",
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <StyledTableCell width="60px" align="center">
                  <Tooltip title="Actions">
                    <span>Actions</span>
                  </Tooltip>
                </StyledTableCell>
                {Header.map((header, index) => (
                  <StyledTableCell
                    key={index}
                    sx={{
                      cursor: "default",
                    }}
                  >
                    <Tooltip title={`Sort by ${header}`}>
                      <span>{header}</span>
                    </Tooltip>
                  </StyledTableCell>
                ))}
              </TableRow>
            </TableHead>
            {renderTableContent()}
          </Table>
        </Box>
      </Box>

      {/* Dialog for viewing and editing item details */}
      <Dialog
        open={open}
        onClose={isSaving ? undefined : handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            overflow: "hidden",
          },
        }}
      >
        <DialogTitle
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            p: 3,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            bgcolor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
          }}
        >
          <Box>
            <Typography variant="h6" component="span">
              {tableName ? tableName.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) : "Item"} Details
            </Typography>
            {tableName && (
              <Typography variant="caption" color="inherit" display="block" sx={{ opacity: 0.8 }}>
                Table: {tableName}
              </Typography>
            )}
          </Box>
          {isAdmin && !isEditing && !isSaving && !readOnly && (
            <Tooltip title="Edit">
              <IconButton
                onClick={handleEdit}
                sx={{
                  color: theme.palette.primary.contrastText,
                  backgroundColor: alpha("#fff", 0.15),
                  "&:hover": {
                    backgroundColor: alpha("#fff", 0.25),
                  },
                }}
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
          )}
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {isSaving && (
            <Box
              sx={{
                position: "absolute",
                zIndex: 9999,
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: alpha(theme.palette.background.paper, 0.5),
                left: 0,
                top: 0,
              }}
            >
              <Paper elevation={3} sx={{ p: 3, display: "flex", alignItems: "center", backgroundColor: theme.palette.background.paper }}>
                <CircularProgress size={24} sx={{ mr: 2 }} />
                <Typography variant="h6">Saving changes...</Typography>
              </Paper>
            </Box>
          )}
          {selectedItem && !isEditing ? (
            <Grid container spacing={2}>
              {Object.entries(selectedItem).map(([key, value]) => (
                <Grid item xs={12} sm={6} key={key}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      backgroundColor: theme => alpha(theme.palette.primary.main, 0.03),
                      borderRadius: 1,
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      transition: "all 0.2s ease",
                      "&:hover": {
                        backgroundColor: theme => alpha(theme.palette.primary.main, 0.06),
                        boxShadow: 1,
                      },
                    }}
                  >
                    {renderReadOnlyField(key, value as string)}
                  </Paper>
                </Grid>
              ))}
            </Grid>
          ) : editedItem && isEditing ? (
            <Grid container spacing={2}>
              {Object.entries(editedItem).map(([key, value]) => (
                <Grid item xs={12} sm={6} key={key}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      borderRadius: 1,
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      transition: "all 0.2s ease",
                      "&:hover": {
                        boxShadow: 1,
                      },
                    }}
                  >
                    {renderEditField(key, value as string)}
                  </Paper>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Typography variant="body1">No details available.</Typography>
          )}
        </DialogContent>

        {/* Dialog Actions */}
        <DialogActions
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: "divider",
            backgroundColor: alpha(theme.palette.background.default, 0.5),
          }}
        >
          {isEditing && (
            <Box sx={{ display: "flex", alignItems: "center", mr: 2, flexWrap: "wrap" }}>
              <Typography variant="caption" color="textSecondary" sx={{ display: "flex", alignItems: "center", mr: 2 }}>
                <KeyIcon fontSize="small" sx={{ mr: 0.5 }} /> Primary Key
              </Typography>
              <Typography variant="caption" color="textSecondary" sx={{ display: "flex", alignItems: "center", mr: 2 }}>
                <LockIcon fontSize="small" sx={{ mr: 0.5 }} /> Identity
              </Typography>
              <Typography variant="caption" color="error" sx={{ display: "flex", alignItems: "center" }}>
                * Required
              </Typography>
            </Box>
          )}
          <Button onClick={handleClose} variant="outlined" sx={{ borderRadius: 4 }} disabled={isSaving}>
            Close
          </Button>
          {isAdmin && isEditing && (
            <Button
              onClick={handleSave}
              variant="contained"
              color="primary"
              disabled={isSaving}
              startIcon={isSaving ? <CircularProgress size={20} /> : null}
              sx={{
                borderRadius: 4,
                minWidth: 120,
                boxShadow: 2,
              }}
            >
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default StoreMemberTable;
