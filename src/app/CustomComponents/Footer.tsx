import { Box, Pagination, PaginationItem, Slider, Typography } from "@mui/material";
import React from "react";

interface FooterProps {
  limit: number;
  totalPages: number;
  page: number;
  count: number;
  handleLimitChange: (event: Event, newValue: number | number[]) => void;
  handlePageChange: (event: React.ChangeEvent<unknown>, value: number) => void;
  disabled?: boolean;
}

const Footer: React.FC<FooterProps> = ({ limit, totalPages, page, count, handleLimitChange, handlePageChange, disabled = false }) => {
  const getLimitLabel = (value: number) => {
    if (value === 100) return "All";
    return `${value}`;
  };

  return (
    <Box
      component="footer"
      sx={{
        flexShrink: 0,
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        padding: "20px 40px",
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", width: "200px" }}>
        <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
          Limit:
        </Typography>
        <Slider
          value={limit === 1000 ? 1000 : limit}
          min={15}
          max={60}
          step={15}
          marks={[
            { value: 15, label: "15" },
            { value: 30, label: "30" },
            { value: 45, label: "45" },
            { value: 60, label: "60" },
          ]}
          valueLabelDisplay="auto"
          valueLabelFormat={getLimitLabel}
          onChange={handleLimitChange}
          disabled={disabled}
          sx={{
            color: "primary.main",
            "& .MuiSlider-thumb": {
              transition: "0.2s",
              "&:hover, &.Mui-focusVisible": {
                boxShadow: "0 0 0 8px rgba(0, 122, 255, 0.16)",
              },
            },
          }}
        />
      </Box>
      <Pagination
        count={totalPages}
        page={page}
        onChange={handlePageChange}
        color="primary"
        size="large"
        showFirstButton
        showLastButton
        siblingCount={1}
        boundaryCount={1}
        disabled={disabled}
        renderItem={(item) => (
          <PaginationItem
            {...item}
            sx={{
              borderRadius: "50%",
              color: "text.primary",
              "&.Mui-selected": {
                backgroundColor: "primary.main",
                color: "primary.contrastText",
                "&:hover": {
                  backgroundColor: "primary.dark",
                },
              },
            }}
          />
        )}
      />
      <Typography variant="body2" color="text.secondary">
        Total Items: {count}
      </Typography>
    </Box>
  );
};

export default Footer;
