import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { Box, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { alpha, styled } from "@mui/material/styles";
import moment, { MomentInput } from "moment-timezone";
import React, { useEffect, useState } from "react";
import { Store } from "../Types/store-types";

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: "calc(100vh - 300px)",
  "&::-webkit-scrollbar": {
    width: "0.4em",
    height: "0.4em",
  },
  "&::-webkit-scrollbar-track": {
    boxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
    webkitBoxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
  },
  "&::-webkit-scrollbar-thumb": {
    backgroundColor: alpha(theme.palette.primary.main, 0.2),
    borderRadius: "10px",
    "&:hover": {
      backgroundColor: alpha(theme.palette.primary.main, 0.4),
    },
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  whiteSpace: "nowrap",
  padding: theme.spacing(2),
  fontSize: "1.2rem",
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: alpha(theme.palette.primary.main, 0.03),
  },
  "&:hover": {
    backgroundColor: alpha(theme.palette.primary.main, 0.07),
    transition: theme.transitions.create("background-color", {
      duration: theme.transitions.duration.shortest,
    }),
  },
}));

type Value = string | number | null | boolean | Date;
const formatValue = (value: Value, field: string): string => {
  if (value === null) return "-";

  if (["openDate"].includes(field)) {
    const date = moment(value as MomentInput);
    if (date.isValid()) {
      // return date.tz("America/Chicago").format("MM/DD/YYYY");
      return date.tz("America/Chicago").format("MM/DD/YYYY");
    }
  }
  // console.log("field", field);
  if (field === "IsClosed") {
    return value === true ? "Yes" : "No";
  }
  return String(value);
};

interface StoreTableProps {
  stores: Store[];
  loading: boolean;
  limit: number;
  // handleClickOpen: (store: Store) => void;
  Header: string[];
  HeaderKeys: string[];
}

const StoreTable: React.FC<StoreTableProps> = ({ stores, loading, limit, Header = [], HeaderKeys = [] }) => {
  const [localStores, setLocalStores] = useState<Store[]>(stores);

  useEffect(() => {
    setLocalStores(stores);
  }, [stores]);

  const renderTableContent = () => {
    if (loading) {
      return (
        <TableBody>
          {[...Array(limit)].map((_, index) => (
            <StyledTableRow key={index}>
              {Header.map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton animation="wave" />
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }

    if (localStores.length === 0) {
      return (
        <TableBody>
          <StyledTableRow>
            <TableCell colSpan={Header.length} align="center">
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <ErrorOutlineIcon color="action" style={{ fontSize: 60, marginBottom: 16 }} />
                <Typography variant="h6" color="textSecondary">
                  No stores found
                </Typography>
              </Box>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {localStores.map(store => (
          <StyledTableRow key={store["SQL ID"]}>
            {/* <TableCell>
              <Tooltip title="View Details">
                <IconButton color="primary" onClick={() => handleClickOpen(store)} size="large">
                  <VisibilityIcon />
                </IconButton>
              </Tooltip>
            </TableCell> */}
            {HeaderKeys.slice(1).map(header => {
              const value = store[header as keyof Store] || "-";
              return (
                <TableCell sx={{ fontSize: "1.1rem" }} key={header}>
                  {formatValue(value, header)}
                </TableCell>
              );
            })}
          </StyledTableRow>
        ))}
      </TableBody>
    );
  };

  return (
    <StyledTableContainer>
      <Table stickyHeader>
        <TableHead>
          <TableRow>
            {Header.map(header => (
              <StyledTableCell
                key={header}
                sx={{
                  cursor: "default",
                }}
              >
                <span>{header}</span>
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        {renderTableContent()}
      </Table>
    </StyledTableContainer>
  );
};

export default StoreTable;
