import React, { useEffect, useState, useRef } from "react";
import { TextField, FormControl, InputLabel, Select, MenuItem, FormHelperText, Box, Typography, CircularProgress } from "@mui/material";
import { AddStoreFormFieldType } from "../utils/formFieldTypes";
import moment from "moment-timezone";
import debounce from "lodash/debounce";

interface InsertFormFieldProps {
  fieldKey: string;
  fieldType: string;
  value: string;
  onChange: (value: string) => void;
  isRequired: boolean;
  error: string;
  formatFieldKey: (key: string) => string;
  disabled: boolean;
  isPrimaryKey?: boolean;
  tableName?: string;
  onExistingValueCheck?: (key: string, value: string, exists: boolean) => void;
}

const InsertFormField: React.FC<InsertFormFieldProps> = ({
  fieldKey,
  fieldType,
  value,
  onChange,
  isRequired,
  error,
  formatFieldKey,
  disabled,
  isPrimaryKey = false,
  tableName,
  onExistingValueCheck,
}) => {
  const [isChecking, setIsChecking] = useState<boolean>(false);
  const [valueExists, setValueExists] = useState<boolean>(false);
  const hasError = !!error || valueExists;
  const errorMessage = valueExists ? `This ${formatFieldKey(fieldKey)} already exists` : error;
  const previousValueRef = useRef<string>(value);

  // Create a debounced function to check if value exists
  const checkExistingValue = React.useCallback(
    debounce(async (value: string) => {
      if (isPrimaryKey && value && tableName && onExistingValueCheck) {
        try {
          setIsChecking(true);
          onExistingValueCheck(fieldKey, value, false);
        } finally {
          setIsChecking(false);
        }
      }
    }, 500), // Increased debounce time to 800ms
    [fieldKey, isPrimaryKey, tableName, onExistingValueCheck]
  );

  // cancel debounce when user is typing
  useEffect(() => {
    return () => {
      checkExistingValue.cancel();
    };
  }, [checkExistingValue]);

  // Only trigger API call when value actually changes and meets conditions
  useEffect(() => {
    // Only check if the value has actually changed from previous render
    if (value !== previousValueRef.current) {
      previousValueRef.current = value;

      // Handle empty value case - clear any existing errors
      if (!value || value.trim() === "") {
        if (valueExists) {
          setValueExists(false);
        }
        return;
      }

      // Only proceed if this is a primary key with a value
      if (isPrimaryKey && tableName && onExistingValueCheck) {
        checkExistingValue(value);
      }
    }
  }, [value, isPrimaryKey, tableName, onExistingValueCheck, checkExistingValue, valueExists]);

  // Set valueExists based on error message
  useEffect(() => {
    const isExistsError = error && error.includes("already exists");
    if (isExistsError && !valueExists) {
      setValueExists(true);
    } else if (!isExistsError && valueExists) {
      setValueExists(false);
    }
  }, [error, valueExists]);

  const labelText = (
    <Box sx={{ display: "flex", alignItems: "center" }}>
      {formatFieldKey(fieldKey)}
      {isRequired && (
        <Typography component="span" color="error" sx={{ ml: 0.5 }}>
          *
        </Typography>
      )}
      {isPrimaryKey && (
        <Typography component="span" color="primary" sx={{ ml: 0.5, fontSize: "0.75rem" }}>
          (Key)
        </Typography>
      )}
    </Box>
  );

  const commonProps = {
    error: hasError,
    helperText: hasError ? errorMessage : "",
    disabled: disabled,
    required: isRequired,
    variant: "outlined" as const,
    sx: {
      opacity: disabled ? 0.7 : 1,
    },
    InputProps: isChecking
      ? {
          endAdornment: <CircularProgress color="inherit" size={20} />,
        }
      : undefined,
  };

  switch (fieldType) {
    case AddStoreFormFieldType.DATE:
      return (
        <TextField
          fullWidth
          label={labelText}
          type="date"
          value={value ? moment(value).format("YYYY-MM-DD") : ""}
          onChange={e => onChange(e.target.value)}
          InputLabelProps={{ shrink: true }}
          {...commonProps}
        />
      );

    case AddStoreFormFieldType.NUMERIC:
      return <TextField fullWidth label={labelText} type="number" value={value || ""} onChange={e => onChange(e.target.value)} {...commonProps} />;

    case AddStoreFormFieldType.YES_NO:
      return (
        <FormControl fullWidth error={hasError} disabled={disabled} required={isRequired} variant="outlined" sx={{ opacity: disabled ? 0.7 : 1 }}>
          <InputLabel>{formatFieldKey(fieldKey)}</InputLabel>
          <Select
            value={value === "true" ? "true" : value === "false" ? "false" : ""}
            onChange={e => onChange(e.target.value)}
            label={formatFieldKey(fieldKey)}
          >
            <MenuItem value="true">Yes</MenuItem>
            <MenuItem value="false">No</MenuItem>
          </Select>
          {hasError && <FormHelperText error>{errorMessage}</FormHelperText>}
        </FormControl>
      );

    case AddStoreFormFieldType.EMAIL:
      return <TextField fullWidth label={labelText} type="email" value={value || ""} onChange={e => onChange(e.target.value)} {...commonProps} />;

    default:
      return <TextField fullWidth label={labelText} value={value || ""} onChange={e => onChange(e.target.value)} {...commonProps} />;
  }
};

export default InsertFormField;
