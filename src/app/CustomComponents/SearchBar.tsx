import React from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { InputAdornment, IconButton, Box, Autocomplete, Chip } from "@mui/material";
import { StyledButton, StyledTextField } from "./StyledComponents";

interface SearchBarProps {
  searchText: string;
  setSearchText: (text: string) => void;
  handleSearch: () => void;
  handleClearSearch: () => void;
  placeholder?: string;
  disabled?: boolean;
  showFieldSelector?: boolean;
  searchColumns?: string[];
  availableColumns?: string[];
  setSearchColumns?: (columns: string[]) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  searchText,
  setSearchText,
  handleSearch,
  handleClearSearch,
  placeholder = "Search on any column",
  disabled = false,
  showFieldSelector = false,
  searchColumns = [],
  availableColumns = [],
  setSearchColumns,
}) => {
  return (
    <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
      <StyledTextField
        fullWidth
        value={searchText}
        onChange={e => setSearchText(e.target.value)}
        placeholder={placeholder}
        variant="outlined"
        disabled={disabled}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon color="action" />
            </InputAdornment>
          ),
          endAdornment: searchText && (
            <InputAdornment position="end">
              <IconButton onClick={handleClearSearch} edge="end" size="small" disabled={disabled}>
                <ClearIcon />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      {showFieldSelector && setSearchColumns && (
        <Box sx={{ mr: 1 }}>
          <Autocomplete
            multiple
            options={availableColumns}
            value={searchColumns}
            onChange={(_, newValue) => setSearchColumns(newValue)}
            renderInput={params => <StyledTextField {...params} placeholder="Select fields to search" variant="outlined" sx={{ ml: 1, minWidth: 220 }} />}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => <Chip variant="outlined" label={option} size="small" {...getTagProps({ index })} />)
            }
            disabled={disabled}
          />
        </Box>
      )}

      <StyledButton variant="contained" color="primary" onClick={handleSearch} disabled={disabled} sx={{ ml: 1 }}>
        Search
      </StyledButton>
    </Box>
  );
};

export default SearchBar;
