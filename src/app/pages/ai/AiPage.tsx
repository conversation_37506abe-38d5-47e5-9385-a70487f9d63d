import ReplayIcon from "@mui/icons-material/Replay";
import SendIcon from "@mui/icons-material/Send";
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import axios from "axios";
import "chart.js/auto";
import moment from "moment-timezone";
import React, { FC, useState } from "react";
import { Bar, Line, Pie } from "react-chartjs-2";
import { toAbsoluteUrl } from "../../../_metronic/helpers/AssetHelpers";
import { useThemeMode } from "../../../_metronic/partials";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const importantFields = ["store_name", "net_sales"];

const sampleQuestions = [
  // Sales Table Sample Queries
  "show total net sales for lake zurich for 1st of april 2024",
  "show total for river north for the month of march 2024",
  "show top 5 stores with highest number of sales in fiscal year 2023",
  "show top 10 stores where ezCater payment was used most for March-24",
  "what was the total delivery fee in the year 2024",
  "show top channels with net sales in previous quarter of 2024",
  "how many web orders where placed in between 1st Dec 2023 to 1st Jan 2024",
  "show total gratuity collected for each store",
  "show 2 stores with least sales",
  "show me the monthly sales trend for naperville store for the year 2024",
  "compare 1st june 2024 sale with 1st june 2023 sale",

  // Product Table Sample Queries
  "Show top 5 selling product in Mar 2024",
  "Show the top 3 products with highest discount between Mar 2024 to May 2024",
  "show total discount for all pizza category for current year",
  "show number of voided products in the Mar-2024",
  "top 10 voided products in year 2024 based on quantity",
  "show highest selling Beverages for year 2024",
  "show top 5 selling products for eatin/Dine in orders in latest year",
];

const capitalizeAndFormat = (str: string) => {
  return str
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const getRandomQuestions = (questions: string[], count: number) => {
  const shuffled = questions.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

const randomQuestions = getRandomQuestions(sampleQuestions, 3);

const parseFormattedNumber = (value: string | number): number => {
  if (typeof value === "string") {
    return parseFloat(value.replace(/[$,]/g, ""));
  }
  return value;
};

const AiPage: FC = () => {
  const [query, setQuery] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [results, setResults] = useState<any[]>([]);
  const [sqlQuery, setSqlQuery] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [tabKey, setTabKey] = useState<string>("results");
  const [visibleFields, setVisibleFields] = useState<string[]>(importantFields);
  const [showAllFields, setShowAllFields] = useState<boolean>(false);
  const [selectedLabel, setSelectedLabel] = useState<string>("store_name");
  const [chartType, setChartType] = useState<string>("Bar");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorStatus, setErrorStatus] = useState<number | null>(null);

  const API_URL = "https://llmfunctionappdev.azurewebsites.net/api";
  // const API_URL = "http://localhost:7071/api";

  const { mode } = useThemeMode();

  const handleQueryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(event.target.value);
  };
  type Result = {
    [key: string]: string | number;
  };

  const adjustFields = (result: Result): Result => {
    const fieldsToAdjust = [
      "final_total",
      "net_sales",
      "gross_sales",
      "pure_sales",
      "sold_amount",
      "tax_amount",
      "subtotal",
      "tax",
      "gratuity",
      "tax_excluded_amount",
      "service_fee_taxed",
      "service_fee_untaxed",
      "discount_total_amount",
      "order_discount_amount",
      "item_discount_amount",
      "delivery_fee",
      "product_price",
      "delivery",
      "eatin",
      "pickup",
      "price",
    ];

    return Object.keys(result).reduce((acc: Result, key: string) => {
      if (fieldsToAdjust.includes(key)) {
        const value = parseFloat(result[key] as string);
        acc[key] = `$${value.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      } else {
        acc[key] = result[key];
      }
      return acc;
    }, {});
  };

  const handleQuerySubmit = async (event?: React.FormEvent) => {
    if (event) event.preventDefault();
    setLoading(true); // Start loading
    setError(null);
    setErrorMessage(null);
    try {
      const response = await axios.post(`${API_URL}/query`, { question: query });
      const data = response.data;
      const adjustedResults = data.result.map((result: any) => {
        if (result?.order_date || result?.deleted_date) {
          return {
            ...adjustFields(result),
            order_date: moment.tz(result.order_date, "America/Chicago").format("YYYY-MM-DD HH:mm"),
            deleted_date: result.deleted_date ? moment.tz(result.deleted_date, "America/Chicago").format("YYYY-MM-DD HH:mm") : null,
          };
        }
        return adjustFields(result);
      });
      setResults(adjustedResults);
      setTabKey("results");
      setSqlQuery(data.sqlQuery);

      const newFields = Object.keys(data.result[0]).filter((field: string) => !importantFields.includes(field));
      setVisibleFields((prevFields) => {
        if (Object.keys(data.result[0]).includes(importantFields[0])) {
          return prevFields;
        }
        return [...prevFields, ...newFields];
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 500) {
          console.log(error);
          setErrorStatus(500);
          setError(error.response?.data.error || "500");
        } else {
          setError(error.response?.data.error || "An unexpected error occurred");
        }
        setErrorMessage(error.response?.data.error || "An unexpected error occurred");
        setSqlQuery(error.response?.data.sqlQuery || "");
        setTabKey("results");
        setResults([]);
      } else {
        setError("No data available");
      }
    } finally {
      setLoading(false); // Stop loading
    }
  };

  const handleRetry = async () => {
    setLoading(true); // Start loading
    setError(null);
    setErrorMessage(null);
    try {
      const response = await axios.post(`${API_URL}/fixquery`, { sqlQuery, errorMessage });
      const data = response.data;
      const adjustedResults = data.result.map((result: any) => {
        if (result?.order_date || result?.deleted_date) {
          return {
            ...result,
            order_date: moment.tz(result.order_date, "America/Chicago").format("YYYY-MM-DD HH:mm"),
            deleted_date: moment.tz(result.deleted_date, "America/Chicago").format("YYYY-MM-DD HH:mm"),
          };
        }
        return result;
      });
      setResults(adjustedResults);
      setTabKey("results");
      setSqlQuery(data.sqlQuery);

      const newFields = Object.keys(data.result[0]).filter((field: string) => !importantFields.includes(field));
      setVisibleFields((prevFields) => {
        if (Object.keys(data.result[0]).includes(importantFields[0])) {
          return prevFields;
        }
        return [...prevFields, ...newFields];
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 500) {
          setError("Internal Server Error");
        } else {
          setError(error.response?.data.error || "An unexpected error occurred");
        }
        setErrorMessage(error.response?.data.error || "An unexpected error occurred");
        setSqlQuery(error.response?.data.sqlQuery || "");
        setTabKey("sql");
        setResults([]);
      } else {
        setError("An unexpected error occurred");
      }
    } finally {
      setLoading(false); // Stop loading
    }
  };

  const handleFieldToggle = (field: string) => {
    if (field === "show_all_fields") {
      setShowAllFields(!showAllFields);
      setVisibleFields(!showAllFields ? Object.keys(results[0]) : importantFields);
    } else {
      setVisibleFields((prevFields) => (prevFields.includes(field) ? prevFields.filter((f) => f !== field) : [...prevFields, field]));
    }
  };

  const renderTableHeader = () => {
    if (results.length > 0) {
      const keys = Object.keys(results[0]);
      return (
        <TableHead
          sx={{
            "& th": {
              backgroundColor: "#e0e0e0",
              fontWeight: 700,
              fontSize: "1.3rem",
              maxHeight: "5rem",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              position: "sticky",
              top: 0,
              zIndex: 1,
            },
          }}
        >
          <TableRow>
            {keys
              .filter((key) => visibleFields.includes(key))
              .map((key) => (
                <TableCell key={key}>{capitalizeAndFormat(key)}</TableCell>
              ))}
          </TableRow>
        </TableHead>
      );
    }
    return null;
  };

  const renderTableBody = () => {
    if (results.length > 0) {
      const keys = Object.keys(results[0]);
      return (
        <TableBody
          sx={{
            "& td": {
              fontSize: "1.2rem",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
            },
          }}
        >
          {results.map((row, index) => (
            <TableRow key={index}>
              {keys
                .filter((key) => visibleFields.includes(key))
                .map((key) => (
                  <TableCell key={key}>{row[key] != null ? row[key].toString() : "N/A"}</TableCell>
                ))}
            </TableRow>
          ))}
        </TableBody>
      );
    }
    return null;
  };

  const getRandomColor = () => {
    const letters = "0123456789ABCDEF";
    let color = "#";
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };

  const renderChart = () => {
    if (results.length > 0) {
      const labels = results.map((result) => result[selectedLabel]);
      const data = {
        labels,
        datasets: [
          {
            label: "Net Sales",
            data: results.map((result) => parseFormattedNumber(result.net_sales)),
            backgroundColor: results.map(() => getRandomColor()),
            borderColor: "rgba(0, 0, 0, 0.5)",
            borderWidth: 1,
          },
        ],
      };

      const options = {
        plugins: {
          legend: {
            position: chartType === "Pie" ? "right" : "top",
          },
        },
        scales:
          chartType !== "Pie"
            ? {
                x: {
                  grid: {
                    color: "rgba(0, 0, 0, 0.2)",
                  },
                },
                y: {
                  grid: {
                    color: "rgba(0, 0, 0, 0.2)",
                  },
                },
              }
            : {},
      };

      const ChartComponent = chartType === "Bar" ? Bar : chartType === "Line" ? Line : Pie;

      return (
        <Box
          mt={3}
          sx={{
            width: { xs: "80vw", sm: "90vw", md: "80vw" },
            height: { xs: "40vh", sm: "50vh", md: "60vh" },
            overflowY: "auto",
          }}
        >
          <ChartComponent data={data} options={options as any} />
        </Box>
      );
    }
    return null;
  };

  console.log(results);

  return (
    <ThemeProvider theme={theme}>
      {/* // Main Div */}
      <Box
        sx={{
          padding: { xs: "1rem", sm: "0rem 2rem 2rem 2rem" },
          height: "90vh",
          display: "flex",
          justifyContent: "space-between",
          flexDirection: "column",
          gap: 2,
        }}
      >
        {/* Result container */}
        <Box
          sx={{
            flexGrow: 9,
          }}
        >
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ width: "100%", height: "100%" }}>
              <Tabs value={tabKey} onChange={(e, newValue) => setTabKey(newValue)} centered>
                <Tab label="Results" value="results" sx={{ color: mode === "dark" ? "white" : theme.palette.grey[900] }} />
                {results.length > 0 && results.some((result) => result.store_name && result.net_sales) && (
                  <Tab label="Chart" value="chart" sx={{ color: mode === "dark" ? "white" : theme.palette.grey[900] }} />
                )}
                <Tab label="SQL Query" value="sql" sx={{ color: mode === "dark" ? "white" : theme.palette.grey[900] }} />
              </Tabs>
              {tabKey === "results" && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    height: { xs: "auto", md: "100%" },
                  }}
                >
                  {results.length > 0 ? (
                    <>
                      <Button
                        variant="outlined"
                        onClick={() => handleFieldToggle("show_all_fields")}
                        sx={{ color: mode === "dark" ? "white" : "#3f51b5", alignSelf: "flex-start", height: "3rem", fontSize: "1rem" }}
                      >
                        Toggle Fields
                      </Button>
                      <TableContainer
                        component={Paper}
                        sx={{
                          height: "100%",
                          maxHeight: { xs: "auto", md: "60vh" },
                        }}
                      >
                        <Table>
                          {renderTableHeader()}
                          {renderTableBody()}
                        </Table>
                      </TableContainer>
                    </>
                  ) : errorStatus === 500 ? (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "column",
                        height: "100%",
                        gap: 2,
                      }}
                    >
                      <img src={toAbsoluteUrl(`media/auth/500error.png`)} alt="Server error" style={{ maxWidth: "30%" }} />
                      <Typography variant="h4">Server error</Typography>
                    </Box>
                  ) : error ? (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "column",
                        height: "100%",
                        gap: 2,
                      }}
                    >
                      <img
                        src={mode === "dark" ? toAbsoluteUrl(`media/auth/404-error-dark.png`) : toAbsoluteUrl(`media/auth/404-error.png`)}
                        alt="No data available"
                        style={{ maxWidth: "100%", maxHeight: "60%" }}
                      />
                      <Typography variant="h4">No data available</Typography>
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                        height: "100%",
                        width: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Alert
                        sx={{
                          fontSize: "1.2rem",
                          height: "10%",
                          width: "auto",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        severity="info"
                      >
                        Type a question or query to get started.
                      </Alert>
                    </Box>
                  )}
                </Box>
              )}

              {tabKey === "chart" && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                    height: { xs: "auto", md: "100%" },
                  }}
                >
                  {results.length > 0 && results.some((result) => result.store_name && result.net_sales) ? (
                    <>
                      <Box>
                        <FormControl sx={{ m: 1, minWidth: 120 }}>
                          <InputLabel>Chart Type</InputLabel>
                          <Select value={chartType} label="Chart Type" onChange={(event) => setChartType(event.target.value)}>
                            <MenuItem value="Bar">Bar</MenuItem>
                            <MenuItem value="Line">Line</MenuItem>
                            <MenuItem value="Pie">Pie</MenuItem>
                          </Select>
                        </FormControl>
                        <FormControl sx={{ m: 1, minWidth: 120 }}>
                          <InputLabel>Label Field</InputLabel>
                          <Select value={selectedLabel} label="Label Field" onChange={(event) => setSelectedLabel(event.target.value)}>
                            {importantFields
                              .filter((key) => key !== "net_sales")
                              .map((key) => (
                                <MenuItem key={key} value={key}>
                                  {capitalizeAndFormat(key)}
                                </MenuItem>
                              ))}
                          </Select>
                        </FormControl>
                      </Box>
                      {renderChart()}
                    </>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                        height: "100%",
                        width: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Alert
                        sx={{
                          fontSize: "1.2rem",
                          height: "10%",
                          width: "auto",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        severity="warning"
                      >
                        Chart will only be displayed if there is Net Sales and Store Name present in the results.
                      </Alert>
                    </Box>
                  )}
                </Box>
              )}
              {tabKey === "sql" && (
                <Box
                  mt={3}
                  // sx={{
                  //   display: "flex",
                  //   justifyContent: "center",
                  //   alignItems: "center",
                  //   flexDirection: "column",
                  //   gap: 2,
                  // }}
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "column",
                    gap: 2,
                    height: { xs: "auto", md: "100%" },
                  }}
                >
                  {sqlQuery !== "" ? (
                    <>
                      <Typography variant="h4">Generated SQL Query</Typography>
                      <Box
                        sx={{
                          backgroundColor: "#f0f0f0",
                          px: 2,
                          pb: 2,
                          borderRadius: 1,
                          width: { xs: "90%", md: "50%" },
                          height: { xs: "auto", md: "auto" },
                        }}
                      >
                        <Typography
                          variant="body1"
                          sx={{ fontSize: "1.5rem", whiteSpace: "pre-wrap", wordWrap: "break-word", color: mode === "dark" ? "black" : "black" }}
                        >
                          {sqlQuery}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                        height: "100%",
                        width: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Alert
                        sx={{
                          fontSize: "1.2rem",
                          height: "10%",
                          width: "auto",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        severity="warning"
                      >
                        No SQL Query Generated
                      </Alert>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          )}
        </Box>

        {/* query container */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            justifyContent: "flex-end",
            alignItems: "center",
            gap: 2,
          }}
        >
          {error && (
            <Box sx={{ width: { xs: "90%", md: "70%" } }}>
              <Alert
                severity="error"
                sx={{ width: "100%", fontSize: "1.2rem", display: "flex", alignItems: "center", justifyContent: "flex-start", gap: 2 }}
              >
                {error}
                <Button variant="text" color="error" onClick={handleRetry} sx={{ ml: 1 }}>
                  <ReplayIcon />
                </Button>
              </Alert>
            </Box>
          )}

          <Box sx={{ width: { xs: "90%", md: "70%" }, mb: 2, display: "flex", flexDirection: "column", alignItems: "center", gap: 2 }}>
            {(results.length < 1 || loading) && query === "" && (
              <Box
                sx={{
                  width: "100%",
                  height: "100%",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", md: "row" },
                    gap: 2,
                  }}
                >
                  {randomQuestions.map((question, index) => (
                    <Button
                      key={index}
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: 1,
                        p: 2,
                        border: "1px solid #ccc",
                        borderRadius: 2,
                        backgroundColor: "#fff",
                        flex: 1,
                        boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                      }}
                      variant="text"
                      onClick={() => setQuery(question)}
                    >
                      {question}
                    </Button>
                  ))}
                </Box>
              </Box>
            )}

            <Box
              component="form"
              onSubmit={handleQuerySubmit}
              sx={{
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                width: "100%",
              }}
            >
              <FormControl fullWidth variant="outlined" sx={{ flexGrow: 1 }}>
                <TextField
                  variant="outlined"
                  focused
                  value={query}
                  onChange={handleQueryChange}
                  placeholder="Ask me anything related to Sales and Product"
                  sx={{ backgroundColor: "#fff", borderRadius: 2, boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" }}
                />
              </FormControl>
              <Box ml={{ sm: 2, xs: 0 }} mt={{ xs: 2, sm: 0 }}>
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={loading || !query}
                  sx={{ borderRadius: "50%", minWidth: "56px", minHeight: "56px", boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" }}
                >
                  <SendIcon />
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};
export default AiPage;
