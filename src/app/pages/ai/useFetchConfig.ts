import { useEffect, useState } from "react";
import axios from "axios";
import { ConfigResponse } from "./types";

export const useFetchConfig = (API_URL: string) => {
	const [config, setConfig] = useState<ConfigResponse | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchConfig = async () => {
			setLoading(true);
			try {
				const response = await axios.get<ConfigResponse>(`${API_URL}/dbconfig`);
				setConfig(response.data);
			} catch (error) {
				console.error("Error fetching config", error);
				setError("An unexpected error occurred");
			} finally {
				setLoading(false);
			}
		};
		fetchConfig();
	}, [API_URL]);

	return { config, loading, error };
};
