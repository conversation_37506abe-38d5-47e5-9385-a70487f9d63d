.pizza {
  background: url('../../../../public/media/lou/lou-malnatis-logo-clear.png') no-repeat center center;
  width: 500px;
  height: 200px;
  position: relative;
}

.pizza-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.loader {
  margin-top: 20px;
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader-spinner {
  border: 8px solid rgba(255, 255, 255, 0.3);
  border-top: 8px solid #007AFF;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spinner 1.2s linear infinite;
}
