import moment, { MomentInput } from "moment-timezone";

export const capitalizeAndFormat = (str: string) => {
  return str
    .toLowerCase()
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const addSpaceToCamelCase = (str: string): string => {
  // Add a space before every uppercase letter that is not the first character of the string
  return str
    .replace(/([A-Z])/g, " $1") // Add space before capital letters
    .replace(/^./, match => match.toUpperCase()) // Capitalize the first letter
    .trim(); // Remove any leading/trailing spaces
};

export const convertToDate = (value: any): Date | null => {
  if (value instanceof Date) {
    return value;
  }

  if (typeof value === "string" || typeof value === "number") {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  return null; // Return null if the value can't be converted to a valid Date
};

export const convertToMMDDYYYY = (dateString: any): string => {
  const date = new Date(dateString);

  // Extract the month, day, and year
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(date.getDate()).padStart(2, "0");
  const year = date.getFullYear();

  // Format it as MM/DD/YYYY
  return `${month}/${day}/${year}`;
};

export const formatDateToString = (value: any, dateFormat: string = "MM/DD/YYYY", timezone: string | null = null) => {
  const date = moment(value as MomentInput);
  let dateStr = "";
  if (date.isValid()) {
    // dateStr = date.tz("America/Chicago").format(dateFormat);
    dateStr = timezone ? date.tz("America/Chicago").format(dateFormat) : date.format(dateFormat);
  }
  return dateStr;
};

export const formatNumberToCurrency = (num: number, fixedPosition: number = 0): string => {
  const absValue = Math.abs(num);
  let formattedValue = "";
  if (absValue >= 1e9) {
    const str = (absValue / 1e9).toFixed(fixedPosition);
    formattedValue = removeTrailingZeros(str) + "B"; // Billions
  } else if (absValue >= 1e6) {
    const str = (absValue / 1e6).toFixed(fixedPosition);
    formattedValue = removeTrailingZeros(str) + "M"; // Millions
  } else if (absValue >= 1e3) {
    const str = (absValue / 1e3).toFixed(fixedPosition);
    formattedValue = removeTrailingZeros(str) + "K"; // Thousands
  } else {
    const str = absValue.toFixed(fixedPosition);
    formattedValue = removeTrailingZeros(str); // Less than thousand
  }
  return num < 0 ? `-${formattedValue}` : formattedValue;
};

export const removeTrailingZeros = (numStr: string): string => {
  // Convert the number to a string

  // Check if there is a decimal point
  if (numStr.includes(".")) {
    // Split the number into integer and fractional parts
    const [integerPart, fractionalPart] = numStr.split(".");

    // Check if the fractional part is all zeroes
    if (/^0*$/.test(fractionalPart)) {
      return integerPart; // Return the integer part only
    }
  }

  // Return the original number as a string
  return numStr;
};

export const convertFormattedCurrencyToNumber = (currency: string) => {
  if (currency.includes("K")) {
    const num = parseFloat(currency) * 1000;
    return num;
  } else if (currency.includes("M")) {
    const num = parseFloat(currency) * 1000000;
    return num;
  } else if (currency.includes("B")) {
    const num = parseFloat(currency) * 1000000000;
    return num;
  } else {
    return Number(currency);
  }
};

export const objectStringify = (record: Record<string, any>): string => {
  return Object.entries(record)
    .map(([key, value]) => `${key}: ${value}`)
    .join(", ");
};
