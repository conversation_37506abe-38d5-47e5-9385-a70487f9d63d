export type QueryResult = {
	[key: string]: any;
	order_date: string;
	establishment_id: number;
	order_id: number;
	site_number: string;
	store_name: string;
	web_order: boolean;
	dining_option: number;
	channel: string;
	final_total: number;
	subtotal: number;
	tax: number;
	delivery_fee: number;
	tax_excluded_amount: number;
	service_fee_taxed: number;
	service_fee_untaxed: number;
	discount_total_amount: number;
	order_discount_id: number | null;
	order_discount_reason: string;
	order_discount_amount: number | null;
	number_of_people: number;
	gratuity: number;
	eZcater_Flag: number;
	sold_amount: number;
	tax_amount: number;
	product_id: number;
	product_name: string;
	pure_sales: number;
	quantity: number;
	product_class_name: string;
	item_discount_id: number | null;
	item_discount_reason: string;
	item_discount_name: string | null;
	item_discount_amount: number | null;
	voided_by_id: number | null;
	deleted_date: string | null;
	itemTablePrimaryID: number;
	created_by_id: number;
	emp_Fname: string;
	emp_Lname: string;
	user_id: number;
	employee_email: string;
	external_id: string;
	customer_id: number;
	first_name: string;
	last_name: string;
	phone_number: string;
	email: string;
	company_name: string;
	address_1: string | null;
	address_2: string | null;
	city: string | null;
	state: string | null;
	zipcode: string | null;
	country: string | null;
	clientPlatform: string | null;
	source: string;
};

export type ApiResponse = {
	result: QueryResult[];
	summary: string;
	sqlQuery: string;
};

export type ConfigResponse = {
	dbName: string;
	schema: string;
	tableName: string;
};
