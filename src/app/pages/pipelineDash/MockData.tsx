export const pipelineStatusesMockData = {
  overallStats: {
    totalRuns: 100,
    successfulRuns: 99,
    failedRuns: 1,
    successRate: "99.00%",
    totalDuration: "550.08 minutes",
    startTime: "2025-04-23T17:29:03.664Z",
    endTime: "2025-04-24T17:29:03.665Z",
  },
  pipelineSpecificStats: [
    {
      pipelineName: "PL_Master_iSnap",
      totalRuns: 30,
      successfulRuns: 29,
      failedRuns: 1,
      totalDuration: "403.46 minutes",
      successRate: "96.67%",
    },
    {
      pipelineName: "PL_Copy_TOC_EDW_P00",
      totalRuns: 58,
      successfulRuns: 58,
      failedRuns: 0,
      totalDuration: "132.30 minutes",
      successRate: "100.00%",
    },
    {
      pipelineName: "PL_Writeback",
      totalRuns: 10,
      successfulRuns: 10,
      failedRuns: 0,
      totalDuration: "11.34 minutes",
      successRate: "100.00%",
    },
    {
      pipelineName: "PL_AZ_Missing_Orders",
      totalRuns: 1,
      successfulRuns: 1,
      failedRuns: 0,
      totalDuration: "2.17 minutes",
      successRate: "100.00%",
    },
    {
      pipelineName: "PL_TOC_BigC_TPO_Alerts",
      totalRuns: 1,
      successfulRuns: 1,
      failedRuns: 0,
      totalDuration: "0.82 minutes",
      successRate: "100.00%",
    },
  ],
};

export const pipelineListMockData = {
  count: 10,
  totalPipelines: 95,
  page: 1,
  totalPages: 10,
  pipelines: [
    {
      name: "PL_Capture_Max_Date_ID1",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Capture_Max_Date_ID1",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Archive/To be deleted Test",
      },
    },
    {
      name: "PL_Copy_Revel_Data_ADLSS",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Copy_Revel_Data_ADLSS",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Revel",
      },
    },
    {
      name: "PL_Oasis_Capture_Max_Date_ID1",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Oasis_Capture_Max_Date_ID1",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Oasis",
      },
    },
    {
      name: "PL_Oasis_Malna_Capture_Max_Date_ID1",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Oasis_Malna_Capture_Max_Date_ID1",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Oasis_Malna",
      },
    },
    {
      name: "PL_Copy_Oasis_Data_ADLSS",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Copy_Oasis_Data_ADLSS",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Oasis",
      },
    },
    {
      name: "PL_Copy_Oasis_Malna_Data_ADLSS",
      id: "/subscriptions/5cecbe25-06b8-4af3-b31f-ea06eb144f12/resourceGroups/DataTeamResources/providers/Microsoft.Synapse/workspaces/lmdatastudio/pipelines/PL_Copy_Oasis_Malna_Data_ADLSS",
      type: "Microsoft.Synapse/workspaces/pipelines",
      folder: {
        name: "Oasis_Malna",
      },
    },
  ],
  hasNextPage: true,
  hasPreviousPage: false,
  search: "",
  endpoint: "https://lmdatastudio.dev.azuresynapse.net",
};
