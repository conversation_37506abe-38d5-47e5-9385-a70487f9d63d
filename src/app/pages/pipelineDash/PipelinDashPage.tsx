import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import PipelineDash from "./PipelineDash";
import {
  setCardStatsLoader,
  setPagination,
  setPipelineListData,
  setPipelineRunsData,
  setPipelineSpecificStats,
  setOverviewStats,
  setLoading,
  setError,
  setTotalPipelines,
  PipelineRun,
} from "../../../__redux/pipelineDashSlice";
import { getAllPipelineStatuses, getPipelineList, getPipelineRuns, getPipelineStatus } from "../../api/pipelinDashApi";
import { AppDispatch, RootState } from "../../store";
import { pushNewAlert } from "../../../__redux/generalSlice";
import { formatDuration } from "./PiplineDashhelper";

const PipelinDashPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { pagination } = useSelector((state: RootState) => state.pipelineDash);
  const [categories, setCategories] = useState<string[]>(["All Categories"]);
  const [allPipelines, setAllPipelines] = useState<PipelineRun[]>([]);

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = () => {
    dispatch(setCardStatsLoader(true));
    fetchOverviewStats();
    fetchAllPipelines();
  };

  const fetchOverviewStats = async () => {
    try {
      const data = await getAllPipelineStatuses();

      const uniqueCategories = new Set<string>(data.pipelineSpecificStats.map(p => p.pipelineName.split("_")[0]));

      setCategories(["All Categories", ...Array.from(uniqueCategories)]);

      dispatch(
        setOverviewStats({
          totalRuns: data.overallStats.totalRuns,
          successRate: data.overallStats.successRate,
          totalDuration: data.overallStats.totalDuration,
          failedRuns: data.overallStats.failedRuns,
          startTime: data.overallStats.startTime,
          endTime: data.overallStats.endTime,
        })
      );
      dispatch(setPipelineSpecificStats(data.pipelineSpecificStats));
    } catch (error) {
      handleError("Failed to fetch overview statistics", error);
    } finally {
      dispatch(setCardStatsLoader(false));
    }
  };

  const fetchAllPipelines = async () => {
    try {
      dispatch(setLoading(true));

      const data = await getPipelineList(1, 10000, "");
      dispatch(setPipelineListData(data));
      dispatch(setTotalPipelines(data.totalPipelines));

      const pipelineRuns = await Promise.all(
        data.pipelines.map(pipeline => {
          return getPipelineRuns(pipeline.name);
        })
      );

      dispatch(setPipelineRunsData(pipelineRuns.flatMap(run => run.value)));

      const pipelineStatuses = await Promise.all(
        pipelineRuns
          .map(run => {
            if (run.value.length > 0) {
              return getPipelineStatus(run.value[run.value.length - 1].runId);
            }
            return null;
          })
          .filter(run => run !== null)
      );

      const mappedPipelines: PipelineRun[] = data.pipelines.map(pipeline => {
        const pipelineStatus = pipelineStatuses.find(p => p && p.pipelineName === pipeline.name);
        if (pipelineStatus) {
          return {
            name: pipeline.name,
            status: pipelineStatus?.status as "Succeeded" | "Failed" | "InProgress" | "Cancelled",
            lastRun: pipelineStatus?.lastUpdated || "N/A",
            started: pipelineStatus?.runStart ? moment(pipelineStatus.runStart).tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
            duration: pipelineStatus?.durationInMs ? formatDuration(pipelineStatus.durationInMs) : "N/A",
            durationSoFar: pipelineStatus?.durationInMs ? formatDuration(pipelineStatus.durationInMs) : "N/A",
            successRate: pipelineStatus?.activityStats.statusCounts.Succeeded
              ? `${((pipelineStatus.activityStats.statusCounts.Succeeded / pipelineStatus.activityStats.totalActivities) * 100).toFixed(2)}%`
              : "N/A",
            errorMessage: pipelineStatus?.activityStats.errors ? pipelineStatus.activityStats.errors.map(error => error.errorMessage).join(", ") : undefined,
            runId: pipelineStatus?.runId,
            progressPercentage: pipelineStatus?.progressPercentage,
            folder: pipeline.folder.name,
          };
        } else {
          return {
            name: pipeline.name,
            status: "N/A",
            lastRun: "N/A",
            started: "N/A",
            duration: "N/A",
            durationSoFar: "N/A",
            successRate: "N/A",
            errorMessage: undefined,
            runId: "",
            progressPercentage: undefined,
            folder: pipeline.folder.name,
          };
        }
      });

      const sortedPipelines = mappedPipelines.sort((a, b) => {
        const statusOrder = { Failed: 1, InProgress: 2, Succeeded: 3, Cancelled: 4, "N/A": 5 };
        return (statusOrder[a.status] || 4) - (statusOrder[b.status] || 4);
      });

      setAllPipelines(sortedPipelines);

      dispatch(
        setPagination({
          totalItems: data.totalPipelines,
          totalPages: Math.ceil(data.totalPipelines / pagination.itemsPerPage),
        })
      );
    } catch (error) {
      handleError("Failed to fetch pipeline list", error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleError = (message: string, error: unknown) => {
    console.error(message, error);
    dispatch(setError(message));
    dispatch(
      pushNewAlert({
        type: "error",
        message: message + ". Please try again later.",
        show: true,
        heading: "Error",
        errMessage: error instanceof Error ? error.message : "",
        errDescription: "",
      })
    );
  };

  return <PipelineDash allPipelines={allPipelines} categories={categories} fetchAllPipelines={fetchAllPipelines} />;
};

export default PipelinDashPage;
