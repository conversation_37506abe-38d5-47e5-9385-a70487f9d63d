import { message } from "antd";
import React, { useEffect, useState, useMemo, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { pushNewAlert } from "../../../__redux/generalSlice";
import { PipelineFilters, PipelineRun, setCurrentPage, setError, setFilters, setPagination, setPipelines } from "../../../__redux/pipelineDashSlice";
import { cancelPipelineRun, getPipelineStatus, triggerPipeline } from "../../api/pipelinDashApi";
import { AppDispatch, RootState } from "../../store";
import DashboardTabs from "./components/PipelineOverview/DashboardTabs";
import PipelineDetailDialog from "./components/PipelineOverview/PipelineDetailDialog";
import PipelineOverviewContent from "./components/PipelineOverview/PipelineOverviewContent";
import ScheduledPipelinesContent from "./components/ScheduledPipelines/ScheduledPipelinesContent";

interface PipelineDashProps {
  allPipelines: PipelineRun[];
  categories: string[];
  fetchAllPipelines: () => Promise<void>;
}

const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay !== null) {
      const intervalId = setInterval(() => savedCallback.current(), delay);
      return () => clearInterval(intervalId);
    }
    return undefined;
  }, [delay]);
};

const PipelineDash: React.FC<PipelineDashProps> = ({ allPipelines, categories, fetchAllPipelines }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { overview, pipelines, loading, filters, pagination, cardStatsLoader, totalPipelines } = useSelector((state: RootState) => state.pipelineDash);

  const [activeTab, setActiveTab] = useState<string>("overview");
  const [selectedPipeline, setSelectedPipeline] = useState<PipelineRun | null>(null);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);

  const [inProgressPipelines, setInProgressPipelines] = useState<Record<string, string>>({});

  useEffect(() => {
    const inProgress = allPipelines.reduce<Record<string, string>>((acc, pipeline) => {
      if (pipeline.status === "InProgress" && pipeline.runId) {
        acc[pipeline.name] = pipeline.runId;
      }
      return acc;
    }, {});

    setInProgressPipelines(inProgress);
  }, [allPipelines]);

  const updateInProgressPipelines = useCallback(async () => {
    const runIds = Object.values(inProgressPipelines);
    if (runIds.length === 0) return;

    try {
      const statusUpdates = await Promise.all(runIds.map(runId => getPipelineStatus(runId)));

      let hasUpdates = false;
      const updatedPipelineNames: string[] = [];

      allPipelines.forEach(pipeline => {
        if (pipeline.status === "InProgress" && pipeline.runId) {
          const statusUpdate = statusUpdates.find(update => update.runId === pipeline.runId);

          if (statusUpdate) {
            if (statusUpdate.status !== pipeline.status || statusUpdate.progressPercentage !== pipeline.progressPercentage) {
              hasUpdates = true;
              updatedPipelineNames.push(pipeline.name);
            }
          }
        }
      });

      if (hasUpdates) {
        if (updatedPipelineNames.length > 0) {
          message.info(`Updated progress for ${updatedPipelineNames.length > 1 ? `${updatedPipelineNames.length} pipelines` : updatedPipelineNames[0]}`);
          fetchAllPipelines();
        }
      }
    } catch (error) {
      console.error("Error updating in-progress pipelines:", error);
    }
  }, [inProgressPipelines, allPipelines, fetchAllPipelines]);

  useInterval(updateInProgressPipelines, Object.keys(inProgressPipelines).length > 0 ? 30000 : null);

  const filteredPipelines = useMemo(() => {
    if (allPipelines.length === 0) return [];

    let filtered = [...allPipelines];

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        pipeline => pipeline.name.toLowerCase().includes(searchLower) || (pipeline.folder && pipeline.folder.toLowerCase().includes(searchLower))
      );
    }

    if (filters.category && filters.category !== "All Categories") {
      filtered = filtered.filter(pipeline => pipeline.name.split("_")[0] === filters.category);
    }

    if (filters.status && filters.status !== "All Status") {
      filtered = filtered.filter(pipeline => pipeline.status === filters.status);
    }

    const totalItems = filtered.length;

    if (totalItems !== pagination.totalItems) {
      dispatch(
        setPagination({
          totalItems,
          totalPages: Math.ceil(totalItems / pagination.itemsPerPage),
        })
      );
    }

    const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage;
    const paginatedPipelines = filtered.slice(startIndex, startIndex + pagination.itemsPerPage);

    return paginatedPipelines;
  }, [allPipelines, filters, pagination.currentPage, pagination.itemsPerPage, dispatch]);

  useEffect(() => {
    if (filteredPipelines.length > 0 || allPipelines.length > 0) {
      dispatch(setPipelines(filteredPipelines));
    }
  }, [filteredPipelines, dispatch, allPipelines]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handleFilterChange = (key: keyof PipelineFilters, value: string) => {
    dispatch(setFilters({ [key]: value }));
    dispatch(setCurrentPage(1));
  };

  const handlePageChange = (page: number) => {
    dispatch(setCurrentPage(page));
  };

  const handleRunNow = async (pipelineName: string) => {
    try {
      await triggerPipeline(pipelineName, { param1: "", param2: "" });
      message.success(`Pipeline "${pipelineName}" triggered successfully`);
      await fetchAllPipelines();
    } catch (error) {
      message.error(error instanceof Error ? error.message : "An unknown error occurred");
      handleError(`Failed to trigger pipeline "${pipelineName}"`, error);
    }
  };

  const handleCancelRun = async (runId: string) => {
    try {
      await cancelPipelineRun(runId);
      await fetchAllPipelines();
    } catch (error) {
      handleError(`Failed to cancel pipeline run`, error);
    }
  };

  const handleRetry = async (pipelineName: string) => {
    try {
      await triggerPipeline(pipelineName, { param1: "", param2: "" });
      message.success(`Pipeline "${pipelineName}" retry initiated successfully`);
      await fetchAllPipelines();
    } catch (error) {
      message.error(error instanceof Error ? error.message : "An unknown error occurred");
      handleError(`Failed to retry pipeline "${pipelineName}"`, error);
    }
  };

  const handleViewDetails = (pipeline: PipelineRun) => {
    setDetailsLoading(true);
    setSelectedPipeline(pipeline);
    setTimeout(() => {
      setDetailsLoading(false);
    }, 300);
  };

  const handleCloseDetails = () => {
    setSelectedPipeline(null);
  };

  const handleError = (message: string, error: unknown) => {
    console.error(message, error);
    dispatch(setError(message));
    dispatch(
      pushNewAlert({
        type: "error",
        message: message + ". Please try again later.",
        show: true,
        heading: "Error",
        errMessage: error instanceof Error ? error.message : "",
        errDescription: "",
      })
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <PipelineOverviewContent
            stats={overview}
            fetchAllPipelines={fetchAllPipelines}
            pipelines={pipelines}
            loading={loading}
            totalPipelines={totalPipelines || 0}
            cardStatsLoader={cardStatsLoader}
            filters={filters}
            onFilterChange={handleFilterChange}
            onRunNow={handleRunNow}
            onViewDetails={handleViewDetails}
            onRetry={handleRetry}
            pagination={{
              current: pagination.currentPage,
              total: pagination.totalItems,
              pageSize: pagination.itemsPerPage,
              onChange: handlePageChange,
            }}
            onCancelRun={handleCancelRun}
            categories={categories}
          />
        );
      case "schedule":
        return <ScheduledPipelinesContent />;
      default:
        return null;
    }
  };

  return (
    <div style={{ padding: "24px", width: "100%", margin: "auto", height: "100%" }}>
      <DashboardTabs activeTab={activeTab} onTabChange={handleTabChange} />
      {renderContent()}

      <PipelineDetailDialog visible={selectedPipeline !== null} pipeline={selectedPipeline} onClose={handleCloseDetails} loading={detailsLoading} />
    </div>
  );
};

export default PipelineDash;
