import React, { useEffect, useState, useMemo } from "react";
import { Card, Row, Col, DatePicker, Select, Spin, Typography, Empty, Alert, Tabs } from "antd";
import moment from "moment";
import { Line, Bar, Pie } from "react-chartjs-2";
import { ArrowUpOutlined, ArrowDownOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { Chart, registerables } from "chart.js";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import { pushNewAlert } from "../../../../../__redux/generalSlice";
import { getDailyPipelineMetrics } from "../../../../api/pipelinDashApi";
import dayjs from "dayjs";
// Register chart.js components
Chart.register(...registerables);

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title, Text } = Typography;

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string | string[];
    borderColor?: string;
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
  }[];
}

interface DailyMetric {
  date: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
  pipelineMetrics: PipelineMetric[];
}

interface PipelineMetric {
  pipelineName: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
}

interface OverallMetrics {
  startDate: string;
  endDate: string;
  totalDays: number;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: string;
  avgDuration: string;
  totalDuration: string;
}

interface MetricsData {
  overallMetrics: OverallMetrics;
  dailyMetrics: DailyMetric[];
}

const PerformanceMetricsContent: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { pipelineSpecificStats } = useSelector((state: RootState) => state.pipelineDash);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([dayjs().subtract(1, "days"), dayjs()]);
  const [selectedPipeline, setSelectedPipeline] = useState<string>("all");
  const [metricsData, setMetricsData] = useState<MetricsData | null>(null);
  const [activeTab, setActiveTab] = useState<string>("overview");

  // Color palette
  const colors = {
    success: "rgba(82, 196, 26, 0.8)",
    successLight: "rgba(82, 196, 26, 0.2)",
    failed: "rgba(255, 77, 79, 0.8)",
    failedLight: "rgba(255, 77, 79, 0.2)",
    running: "rgba(24, 144, 255, 0.8)",
    runningLight: "rgba(24, 144, 255, 0.2)",
    bgGradientStart: "rgba(250, 250, 250, 1)",
    bgGradientEnd: "rgba(245, 245, 245, 0.8)",
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY");
  };

  // Convert duration string to minutes for comparison
  const durationToMinutes = (duration: string): number => {
    const match = duration.match(/(\d+)\s*min/);
    return match ? parseInt(match[1], 10) : 0;
  };

  // Fetch metrics data when date range or selected pipeline changes
  useEffect(() => {
    fetchMetricsData();
  }, [dateRange, selectedPipeline]);

  const fetchMetricsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const startTime = dateRange[0].format("YYYY-MM-DD");
      const endTime = dateRange[1].format("YYYY-MM-DD");
      const pipelineName = selectedPipeline !== "all" ? selectedPipeline : undefined;

      const data = await getDailyPipelineMetrics(startTime, endTime, pipelineName);
      setMetricsData(data);
    } catch (error) {
      console.error("Error fetching metrics data:", error);
      setError("Failed to fetch metrics data. Please try again later.");
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to fetch metrics data. Please try again later.",
          show: true,
          heading: "Error",
          errMessage: error instanceof Error ? error.message : "",
          errDescription: "",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  // Get unique pipeline names for the dropdown
  const pipelineOptions = useMemo(() => {
    const options = pipelineSpecificStats.map(stat => stat.pipelineName);
    return ["all", ...options];
  }, [pipelineSpecificStats]);

  // Summary stats cards data
  const summaryStats = useMemo(() => {
    if (!metricsData) return null;

    const { overallMetrics } = metricsData;
    return [
      {
        title: "Total Runs",
        value: overallMetrics.totalRuns,
        subtext: `${overallMetrics.totalDays} days`,
        icon: null,
        color: colors.running,
      },
      {
        title: "Success Rate",
        value: overallMetrics.successRate,
        subtext: `${overallMetrics.successfulRuns} successful runs`,
        icon: <ArrowUpOutlined />,
        color: colors.success,
        trend: parseFloat(overallMetrics.successRate) >= 90 ? "up" : "down",
      },
      {
        title: "Failed Runs",
        value: overallMetrics.failedRuns,
        subtext: `${((overallMetrics.failedRuns / overallMetrics.totalRuns) * 100).toFixed(1)}% of total`,
        icon: <ArrowDownOutlined />,
        color: colors.failed,
      },
      {
        title: "Avg Duration",
        value: overallMetrics.avgDuration,
        subtext: `Total: ${overallMetrics.totalDuration}`,
        icon: <ClockCircleOutlined />,
        color: colors.running,
      },
    ];
  }, [metricsData]);

  // Daily runs chart data
  const dailyRunsChartData = useMemo((): ChartData | null => {
    if (!metricsData) return null;

    const labels = metricsData.dailyMetrics.map((day: DailyMetric) => formatDate(day.date));
    const successData = metricsData.dailyMetrics.map((day: DailyMetric) => day.successfulRuns);
    const failedData = metricsData.dailyMetrics.map((day: DailyMetric) => day.failedRuns);

    return {
      labels,
      datasets: [
        {
          label: "Successful Runs",
          data: successData,
          backgroundColor: colors.successLight,
          borderColor: colors.success,
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
        {
          label: "Failed Runs",
          data: failedData,
          backgroundColor: colors.failedLight,
          borderColor: colors.failed,
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
      ],
    };
  }, [metricsData]);

  // Success rate chart data
  const successRateChartData = useMemo((): ChartData | null => {
    if (!metricsData) return null;

    const labels = metricsData.dailyMetrics.map((day: DailyMetric) => formatDate(day.date));
    const successRates = metricsData.dailyMetrics.map((day: DailyMetric) => parseFloat(day.successRate.replace("%", "")));

    return {
      labels,
      datasets: [
        {
          label: "Success Rate (%)",
          data: successRates,
          backgroundColor: colors.runningLight,
          borderColor: colors.running,
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
      ],
    };
  }, [metricsData]);

  // Average duration chart data
  const durationChartData = useMemo((): ChartData | null => {
    if (!metricsData) return null;

    const labels = metricsData.dailyMetrics.map((day: DailyMetric) => formatDate(day.date));
    const durations = metricsData.dailyMetrics.map((day: DailyMetric) => durationToMinutes(day.avgDuration));

    return {
      labels,
      datasets: [
        {
          label: "Average Duration (minutes)",
          data: durations,
          backgroundColor: colors.running,
          borderColor: colors.running,
          borderWidth: 1,
        },
      ],
    };
  }, [metricsData]);

  // Pipeline comparison chart data
  const pipelineComparisonData = useMemo((): ChartData | null => {
    if (!metricsData || !metricsData.dailyMetrics || metricsData.dailyMetrics.length === 0) return null;

    // Combine all pipeline metrics across all days
    const pipelineMap = new Map<string, { successfulRuns: number; failedRuns: number; totalRuns: number }>();

    metricsData.dailyMetrics.forEach((day: DailyMetric) => {
      if (day.pipelineMetrics && day.pipelineMetrics.length > 0) {
        day.pipelineMetrics.forEach((pipeline: PipelineMetric) => {
          if (!pipelineMap.has(pipeline.pipelineName)) {
            pipelineMap.set(pipeline.pipelineName, {
              successfulRuns: 0,
              failedRuns: 0,
              totalRuns: 0,
            });
          }

          const current = pipelineMap.get(pipeline.pipelineName)!;
          current.successfulRuns += pipeline.successfulRuns;
          current.failedRuns += pipeline.failedRuns;
          current.totalRuns += pipeline.totalRuns;
          pipelineMap.set(pipeline.pipelineName, current);
        });
      }
    });

    // Convert to chart data, limit to top 10 pipelines by total runs
    const pipelineEntries = Array.from(pipelineMap.entries())
      .sort((a, b) => {
        // if (b[1].failedRuns !== a[1].failedRuns) {
        //   return b[1].failedRuns - a[1].failedRuns;
        // }
        return b[1].totalRuns - a[1].totalRuns;
      })
      .slice(0, 10);

    const labels = pipelineEntries.map(entry => {
      // Shorten long pipeline names
      const name = entry[0];
      return name.length > 20 ? `${name.substring(0, 17)}...` : name;
    });

    const successData = pipelineEntries.map(entry => entry[1].successfulRuns);
    const failedData = pipelineEntries.map(entry => entry[1].failedRuns);

    return {
      labels,
      datasets: [
        {
          label: "Successful Runs",
          data: successData,
          backgroundColor: colors.success,
          borderWidth: 0,
        },
        {
          label: "Failed Runs",
          data: failedData,
          backgroundColor: colors.failed,
          borderWidth: 0,
        },
      ],
    };
  }, [metricsData]);

  // Success vs failure distribution pie chart
  const successFailurePieData = useMemo((): ChartData | null => {
    if (!metricsData) return null;

    const { overallMetrics } = metricsData;

    return {
      labels: ["Successful", "Failed"],
      datasets: [
        {
          label: "Runs",
          data: [overallMetrics.successfulRuns, overallMetrics.failedRuns],
          backgroundColor: [colors.success, colors.failed],
          borderWidth: 0,
        },
      ],
    };
  }, [metricsData]);

  // Chart options
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      x: {
        stacked: false,
      },
      y: {
        stacked: false,
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right" as const,
      },
    },
  };

  const renderSummaryCards = () => {
    if (!summaryStats) return null;

    return (
      <Row gutter={[16, 16]}>
        {summaryStats.map((stat, index) => (
          <Col xs={24} sm={12} md={6} key={index}>
            <Card
              className="stat-card"
              style={{
                borderRadius: "12px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
                background: `linear-gradient(135deg, ${colors.bgGradientStart}, ${colors.bgGradientEnd})`,
              }}
            >
              <div style={{ textAlign: "center" }}>
                <Text type="secondary">{stat.title}</Text>
                <Title level={3} style={{ margin: "8px 0", color: stat.color }}>
                  {stat.value}
                </Title>
                <Text type="secondary" style={{ display: "block" }}>
                  {stat.icon && <span style={{ marginRight: "4px" }}>{stat.icon}</span>}
                  {stat.subtext}
                </Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  const renderOverviewTab = () => {
    return (
      <>
        <Row gutter={[16, 16]} style={{ marginTop: "20px" }}>
          <Col xs={24} md={24} lg={16}>
            <Card title="Daily Pipeline Runs" style={{ height: "300px", borderRadius: "12px", boxShadow: "0 2px 8px rgba(0,0,0,0.05)" }}>
              {dailyRunsChartData ? <Line data={dailyRunsChartData} options={lineChartOptions} /> : <Empty description="No data available" />}
            </Card>
          </Col>
          <Col xs={24} md={24} lg={8}>
            <Card title="Success vs Failure" style={{ height: "300px", borderRadius: "12px", boxShadow: "0 2px 8px rgba(0,0,0,0.05)" }}>
              {successFailurePieData ? <Pie data={successFailurePieData} options={pieChartOptions} /> : <Empty description="No data available" />}
            </Card>
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
          <Col xs={24} md={12}>
            <Card title="Success Rate Trend" style={{ height: "300px", borderRadius: "12px", boxShadow: "0 2px 8px rgba(0,0,0,0.05)" }}>
              {successRateChartData ? <Line data={successRateChartData} options={lineChartOptions} /> : <Empty description="No data available" />}
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title="Average Duration" style={{ height: "300px", borderRadius: "12px", boxShadow: "0 2px 8px rgba(0,0,0,0.05)" }}>
              {durationChartData ? <Bar data={durationChartData} options={barChartOptions} /> : <Empty description="No data available" />}
            </Card>
          </Col>
        </Row>
      </>
    );
  };

  const renderPipelineTab = () => {
    return (
      <Row gutter={[16, 16]} style={{ marginTop: "20px" }}>
        <Col span={24}>
          <Card title="Pipeline Performance Comparison (Top 10)" style={{ height: "400px", borderRadius: "12px", boxShadow: "0 2px 8px rgba(0,0,0,0.05)" }}>
            {pipelineComparisonData ? (
              <Bar data={pipelineComparisonData} options={barChartOptions} />
            ) : (
              <Empty description="No pipeline comparison data available" />
            )}
          </Card>
        </Col>
      </Row>
    );
  };

  // Create tab items for the Tabs component
  const tabItems = [
    {
      key: "overview",
      label: "Overview",
      children: renderOverviewTab(),
    },
    {
      key: "pipeline",
      label: "Pipeline Comparison",
      children: renderPipelineTab(),
    },
  ];

  return (
    <div style={{ padding: "16px" }}>
      <div style={{ marginBottom: "20px" }}>
        <Title level={4}>Pipeline Performance Metrics</Title>
        <Text type="secondary">Analyze pipeline performance metrics over time to identify trends and potential issues.</Text>
      </div>

      <Row gutter={16} style={{ marginBottom: "20px" }}>
        <Col>
          <RangePicker
            onChange={dates => {
              if (dates && dates[0] && dates[1]) {
                // Convert to moment objects to maintain compatibility
                setDateRange([dayjs(dates[0].toString()), dayjs(dates[1].toString())]);
              }
            }}
            defaultValue={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
            style={{ marginRight: "16px" }}
          />
        </Col>
        <Col>
          <Select value={selectedPipeline} onChange={setSelectedPipeline} style={{ width: "240px" }} placeholder="Select Pipeline">
            {pipelineOptions.map(pipeline => (
              <Option key={pipeline} value={pipeline}>
                {pipeline === "all" ? "All Pipelines" : pipeline}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      {error && <Alert message="Error" description={error} type="error" showIcon style={{ marginBottom: "16px" }} />}

      {loading ? (
        <div style={{ display: "flex", justifyContent: "center", padding: "40px" }}>
          <Spin size="large" />
        </div>
      ) : (
        <>
          {renderSummaryCards()}

          {/* Replace TabPane components with items prop */}
          <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: "24px" }} items={tabItems} />
        </>
      )}
    </div>
  );
};

export default PerformanceMetricsContent;
