import { CloseOutlined, PlayCircleOutlined, SyncOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Empty, Pagination, Progress, Row, Spin, Tag, Typography } from "antd";
import moment from "moment";
import React, { memo } from "react";
import { useSelector } from "react-redux";
import { PipelineRun } from "../../../../../__redux/pipelineDashSlice";
import { RootState } from "../../../../store";
import { isAdmin } from "../../../../utils/roleUtils";

const { Text, Title } = Typography;

// Define all possible pipeline statuses for type safety
type PipelineStatus = "Succeeded" | "Failed" | "InProgress" | "Cancelled" | string | undefined;

interface PipelineTableProps {
  pipelines: PipelineRun[];
  loading: boolean;
  onRunNow: (pipelineName: string) => void;
  onViewDetails: (pipeline: PipelineRun) => void;
  onRetry: (pipelineName: string) => void;
  pagination: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  onCancelRun: (runId: string) => void;
}

// Memoize the component to prevent unnecessary re-renders
const PipelineTable: React.FC<PipelineTableProps> = memo(({ pipelines, loading, onRunNow, onViewDetails, onRetry, pagination, onCancelRun }) => {
  const { userRole } = useSelector((state: RootState) => state.auth);

  const getStatusColor = (status: PipelineStatus) => {
    switch (status) {
      case "Succeeded":
        return "success";
      case "Failed":
        return "error";
      case "InProgress":
        return "warning";
      case "Cancelled":
        return "default";
      default:
        return "default";
    }
  };

  const getProgressPercent = (pipeline: PipelineRun) => {
    if (pipeline.progressPercentage !== undefined && pipeline.progressPercentage !== null) {
      return pipeline.progressPercentage;
    }
    return 0;
  };

  const getProgressStatus = (status: PipelineStatus) => {
    switch (status) {
      case "Succeeded":
        return "success";
      case "Failed":
        return "exception";
      case "InProgress":
        return "active";
      case "Cancelled":
        return "normal";
      default:
        return "normal";
    }
  };

  const getProgressColor = (status: PipelineStatus) => {
    switch (status) {
      case "Succeeded":
        return "#52c41a";
      case "Failed":
        return "#f5222d";
      case "InProgress":
        return "#faad14";
      case "Cancelled":
        return "#d9d9d9";
      default:
        return "#1890ff";
    }
  };

  const formatDateTime = (date: string | undefined | null) => {
    if (!date) return "N/A";
    return moment(date).isValid() ? moment(date).format("MM-DD-YYYY HH:mm:ss") : "N/A";
  };

  const displayValue = (value: string | number | undefined | null) => {
    if (value === undefined || value === null || value === "") {
      return "N/A";
    }
    return value;
  };

  const hasNoPipelineRuns = (pipeline: PipelineRun) => {
    return pipeline.lastRun === "N/A" && pipeline.started === "N/A";
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px 0" }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Loading pipelines...</div>
      </div>
    );
  }

  if (pipelines.length === 0) {
    return <Empty description="No matching pipelines found" image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        {pipelines.map(pipeline => (
          <Col xs={24} sm={12} lg={8} key={pipeline.name || `pipeline-${Math.random().toString(36).substring(7)}`}>
            <Card
              variant="outlined"
              style={{
                borderRadius: "8px",
                boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
                height: "100%",
                padding: "20px",
              }}
            >
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                  <Title level={5} ellipsis={{ rows: 1 }} style={{ margin: 0, maxWidth: "80%" }}>
                    {displayValue(pipeline.name)}
                  </Title>
                  <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
                    <Tag color={getStatusColor(pipeline.status)} style={{ alignSelf: "flex-end" }}>
                      {displayValue(pipeline.status)}
                    </Tag>
                    <Tag color="blue" style={{ alignSelf: "flex-end" }}>
                      {displayValue(pipeline.folder)}
                    </Tag>
                  </div>
                </div>
              </div>

              {hasNoPipelineRuns(pipeline) ? (
                <Alert
                  message="No Pipeline Runs"
                  description="No pipeline runs are available for this pipeline yet. Click 'Run Now' to start the run."
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              ) : (
                <div style={{ marginBottom: 16 }}>
                  <Row>
                    <Col span={12}>
                      <Text type="secondary">{pipeline.status === "Succeeded" ? "Last run:" : "Started:"}</Text>
                    </Col>
                    <Col span={12} style={{ textAlign: "right" }}>
                      <Text>{pipeline.status === "Succeeded" ? formatDateTime(pipeline.lastRun) : formatDateTime(pipeline.started)}</Text>
                    </Col>
                  </Row>
                  <Row style={{ marginTop: 8 }}>
                    <Col span={12}>
                      <Text type="secondary">Duration:</Text>
                    </Col>
                    <Col span={12} style={{ textAlign: "right" }}>
                      <Text>{displayValue(pipeline.durationSoFar || pipeline.duration)}</Text>
                    </Col>
                  </Row>
                  {pipeline.status !== "Failed" && pipeline.status !== "Cancelled" && (
                    <Row style={{ marginTop: 8 }}>
                      <Col span={12}>
                        <Text type="secondary">Progress:</Text>
                      </Col>
                      <Col span={12} style={{ textAlign: "right" }}>
                        <Text>
                          {pipeline.status === "Succeeded" ? "100%" : pipeline.status === "InProgress" ? displayValue(pipeline.progressPercentage) + "%" : "0%"}
                        </Text>
                      </Col>
                    </Row>
                  )}
                  {pipeline.status === "Failed" && pipeline.errorMessage && (
                    <Row style={{ marginTop: 8 }}>
                      <Col span={12}>
                        <Text type="secondary">Error message:</Text>
                      </Col>
                      <Col span={12} style={{ textAlign: "right" }}>
                        <Text type="danger" ellipsis={{ tooltip: pipeline.errorMessage }}>
                          {displayValue(pipeline.errorMessage)}
                        </Text>
                      </Col>
                    </Row>
                  )}
                </div>
              )}

              <div style={{ marginBottom: 20 }}>
                <Progress
                  percent={getProgressPercent(pipeline)}
                  status={getProgressStatus(pipeline.status)}
                  showInfo={false}
                  strokeWidth={8}
                  strokeColor={getProgressColor(pipeline.status)}
                  trailColor="#f0f0f0"
                />
              </div>

              <Button onClick={() => onViewDetails(pipeline)}>View Details</Button>

              {isAdmin(userRole) && (
                <div style={{ display: "flex", justifyContent: "flex-end", gap: "8px" }}>
                  {(pipeline.status === "Failed" || pipeline.status === "Cancelled") && (
                    <Button danger icon={<SyncOutlined />} onClick={() => onRetry(pipeline.name)}>
                      Retry
                    </Button>
                  )}
                  {pipeline.status !== "InProgress" && pipeline.status !== "Failed" && pipeline.status !== "Cancelled" && (
                    <Button type="primary" icon={<PlayCircleOutlined />} onClick={() => onRunNow(pipeline.name)}>
                      Run Now
                    </Button>
                  )}
                  {pipeline.status === "InProgress" && pipeline?.runId && (
                    <Button type="primary" danger icon={<CloseOutlined />} onClick={() => onCancelRun(pipeline.runId)}>
                      Cancel Run
                    </Button>
                  )}
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Row>

      {pagination.total > 0 && (
        <div style={{ textAlign: "center", marginTop: 24 }}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={pagination.onChange}
            showSizeChanger={false}
            showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} pipelines`}
          />
        </div>
      )}
    </div>
  );
});

export default PipelineTable;
