import React from "react";
import { Card, Row, Col, Typography, Statistic } from "antd";
import { DatabaseOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { PipelineOverviewStats } from "../../../../../__redux/pipelineDashSlice";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Tooltip } from "antd";
// import moment from "moment";
const { Title } = Typography;

interface StatisticsCardsProps {
  stats: PipelineOverviewStats;
  totalPipelines?: number;
  loading: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({ stats, totalPipelines, loading }) => {
  const { pipelineSpecificStats } = useSelector((state: RootState) => state.pipelineDash);

  const failedRunsPipeline = pipelineSpecificStats.filter(pipeline => pipeline.failedRuns > 0).map(pipeline => pipeline.pipelineName);
  console.log(failedRunsPipeline);

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} md={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={loading}
            title={<Title level={5}>Total Pipelines</Title>}
            value={totalPipelines}
            valueStyle={{ color: "#1890ff", fontSize: "30px" }}
            prefix={<DatabaseOutlined style={{ fontSize: 24, color: "#1890ff", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={loading}
            title={<Title level={5}>Success Rate</Title>}
            value={stats.successRate}
            valueStyle={{ color: "#52c41a", fontSize: "30px" }}
            prefix={<CheckCircleOutlined style={{ fontSize: 24, color: "#52c41a", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={loading}
            title={<Title level={5}>Total Duration</Title>}
            value={stats.totalDuration}
            valueStyle={{ color: "#faad14", fontSize: "30px" }}
            prefix={<ClockCircleOutlined style={{ fontSize: 24, color: "#faad14", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Tooltip title={failedRunsPipeline.length > 0 ? failedRunsPipeline.join(", ") : ""}>
            <Statistic
              loading={loading}
              title={<Title level={5}>Failed Runs</Title>}
              value={stats.failedRuns}
              valueStyle={{ color: "#f5222d", fontSize: "30px" }}
              prefix={<CloseCircleOutlined style={{ fontSize: 24, color: "#f5222d", marginRight: 8 }} />}
            />
          </Tooltip>
        </Card>
      </Col>
    </Row>
  );
};

export default StatisticsCards;
