import { DatabaseOutlined, ScheduleOutlined } from "@ant-design/icons";
import { Tabs } from "antd";
import React from "react";

interface DashboardTabsProps {
  activeTab: string;
  onTabChange: (key: string) => void;
}

const DashboardTabs: React.FC<DashboardTabsProps> = ({ activeTab, onTabChange }) => {
  const tabItems = [
    {
      key: "overview",
      label: (
        <span>
          <DatabaseOutlined className="text-lg mr-2" />
          Pipeline Overview
        </span>
      ),
    },
    {
      key: "schedule",
      label: (
        <span>
          <ScheduleOutlined className="text-lg mr-2" />
          Scheduled Pipelines
        </span>
      ),
    },
  ];

  return <Tabs activeKey={activeTab} onChange={onTabChange} style={{ marginBottom: 24 }} size="large" type="line" items={tabItems} />;
};

export default DashboardTabs;
