import React, { useState, useEffect } from "react";
import { Modal, Typography, Tag, Row, Col, Spin, Table, DatePicker, Button, Empty, Card, Statistic, Space, Progress, Divider } from "antd";
import { CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { PipelineRun } from "../../../../../__redux/pipelineDashSlice";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Line } from "@ant-design/charts";
import moment from "moment";
import { getPipelineRuns, PipelineRunApi, getPipelineActivityRuns } from "../../../../api/pipelinDashApi";
import type { Dayjs } from "dayjs";
import type { RangePickerProps } from "antd/es/date-picker";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface PipelineDetailDialogProps {
  visible: boolean;
  pipeline: PipelineRun | null;
  onClose: () => void;
  loading: boolean;
}

interface RunHistoryItem {
  runId: string;
  status: string;
  startTime: string;
  endTime: string;
  durationInMs: number;
  formattedDuration: string;
}

interface ChartDataItem {
  date: string;
  status: string;
  duration: string;
  runId: string;
  index: number;
}

interface ActivityItem {
  activityName: string;
  activityType: string;
  status: string;
  durationInMs: number;
  error: {
    errorCode: string;
    message: string;
    failureType: string;
    target: string;
    details: string;
  };
  input: Record<string, unknown>;
  output: Record<string, unknown>;
  retryAttempt: null;
}

interface PipelineActivityRunsResponse {
  pipelineName: string;
  runId: string;
  progressPercentage: number;
  totalActivities: number;
  completedActivities: number;
  activities: ActivityItem[];
}

const formatDuration = (durationInMs: number): string => {
  const seconds = Math.floor(durationInMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours} hours ${minutes % 60} minutes`;
  } else if (minutes > 0) {
    return `${minutes} minutes`;
  } else {
    return `${seconds} seconds`;
  }
};

// Activity Detail Modal Component
const ActivityDetailModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  activityData: ActivityItem | null;
}> = ({ visible, onClose, activityData }) => {
  if (!activityData) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Succeeded":
        return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
      case "Failed":
        return <CloseCircleOutlined style={{ color: "#f5222d" }} />;
      case "InProgress":
        return <ClockCircleOutlined style={{ color: "#faad14" }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Succeeded":
        return "success";
      case "Failed":
        return "error";
      case "InProgress":
        return "warning";
      default:
        return "default";
    }
  };

  return (
    <Modal title={<Title level={4}>Activity Details</Title>} open={visible} onCancel={onClose} footer={null} width={800}>
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic title="Activity Name" value={activityData.activityName} valueStyle={{ fontSize: "16px" }} />
          </Col>
          <Col span={12}>
            <Statistic title="Activity Type" value={activityData.activityType} valueStyle={{ fontSize: "16px" }} />
          </Col>
          <Col span={12}>
            <div>
              <Text type="secondary">Status</Text>
              <div>
                <Tag icon={getStatusIcon(activityData.status)} color={getStatusColor(activityData.status)}>
                  {activityData.status}
                </Tag>
              </div>
            </div>
          </Col>
          <Col span={12}>
            <Statistic title="Duration" value={formatDuration(activityData.durationInMs)} valueStyle={{ fontSize: "16px" }} />
          </Col>
        </Row>
      </Card>

      {activityData.error && activityData.error.message && (
        <Card title="Error" style={{ marginBottom: 16 }} headStyle={{ backgroundColor: "#fff1f0" }}>
          <Text type="danger">{activityData.error.message}</Text>
          <div style={{ marginTop: 8 }}>
            <Text>Error Code: {activityData.error.errorCode}</Text>
            <br />
            <Text>Failure Type: {activityData.error.failureType}</Text>
            <br />
            <Text>Target: {activityData.error.target}</Text>
          </div>
        </Card>
      )}

      <Card title="Input" style={{ marginBottom: 16 }}>
        <pre style={{ maxHeight: "200px", overflow: "auto" }}>{JSON.stringify(activityData.input, null, 2)}</pre>
      </Card>

      <Card title="Output">
        <pre style={{ maxHeight: "200px", overflow: "auto" }}>{JSON.stringify(activityData.output, null, 2)}</pre>
      </Card>
    </Modal>
  );
};

const PipelineDetailDialog: React.FC<PipelineDetailDialogProps> = ({ visible, pipeline, onClose, loading }) => {
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);
  const [runHistory, setRunHistory] = useState<RunHistoryItem[]>([]);
  const [selectedRunId, setSelectedRunId] = useState<string | null>(null);
  const [activityData, setActivityData] = useState<PipelineActivityRunsResponse | null>(null);
  const [activityLoading, setActivityLoading] = useState<boolean>(false);
  const [activityModalVisible, setActivityModalVisible] = useState<boolean>(false);
  const [selectedActivity, setSelectedActivity] = useState<ActivityItem | null>(null);
  // const [isActivityLoading, setIsActivityLoading] = useState<boolean>(false);
  // Access pipeline runs data from Redux
  const pipelineRunsData = useSelector((state: RootState) => state.pipelineDash.pipelineRunsData);

  // Filter runs for the current pipeline
  useEffect(() => {
    if (pipeline && pipelineRunsData && pipelineRunsData.length > 0) {
      const filteredRuns = pipelineRunsData
        .filter((run: PipelineRunApi) => run.pipelineName === pipeline.name)
        .map((run: PipelineRunApi) => ({
          runId: run.runId,
          status: run.status,
          startTime: run.runStart ? moment.tz(run.runStart, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
          endTime: run.runEnd ? moment.tz(run.runEnd, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
          durationInMs: run.durationInMs || 0,
          formattedDuration: run.durationInMs ? formatDuration(run.durationInMs) : "N/A",
        }))
        .sort((a: RunHistoryItem, b: RunHistoryItem) => moment(a.startTime).diff(moment(b.startTime)));

      setRunHistory(filteredRuns);
    }

    return () => {
      setRunHistory([]);
      setActivityData(null);
      setActivityLoading(false);
      setActivityModalVisible(false);
      setSelectedActivity(null);
      setSelectedRunId(null);
      setDateRange(null);
    };
  }, [pipeline, pipelineRunsData]);

  // Handle date range change
  const handleDateRangeChange: RangePickerProps["onChange"] = async (dates, dateStrings) => {
    if (!pipeline) return;

    if (!dates) {
      setDateRange(null);
      // Reset to all runs
      if (pipelineRunsData && pipelineRunsData.length > 0) {
        const filteredRuns = pipelineRunsData
          .filter((run: PipelineRunApi) => run.pipelineName === pipeline.name)
          .map((run: PipelineRunApi) => ({
            runId: run.runId,
            status: run.status,
            startTime: run.runStart ? moment.tz(run.runStart, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
            endTime: run.runEnd ? moment.tz(run.runEnd, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
            durationInMs: run.durationInMs || 0,
            formattedDuration: run.durationInMs ? formatDuration(run.durationInMs) : "N/A",
          }))
          .sort((a: RunHistoryItem, b: RunHistoryItem) => moment(a.startTime).diff(moment(b.startTime)));

        setRunHistory(filteredRuns);
      }
      return;
    }

    setDateRange([dates[0] as Dayjs, dates[1] as Dayjs]);
    setHistoryLoading(true);

    try {
      // Format dates for API
      const startTime = dateStrings[0];
      const endTime = dateStrings[1];

      // Fetch runs for the selected date range
      const response = await getPipelineRuns(pipeline.name, startTime, endTime);

      // Process and set the run history
      const formattedRuns = response.value
        .map((run: PipelineRunApi) => ({
          runId: run.runId,
          status: run.status,
          startTime: run.runStart ? moment.tz(run.runStart, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
          endTime: run.runEnd ? moment.tz(run.runEnd, "America/Chicago").format("YYYY-MM-DD HH:mm:ss") : "N/A",
          durationInMs: run.durationInMs || 0,
          formattedDuration: run.durationInMs ? formatDuration(run.durationInMs) : "N/A",
        }))
        .sort((a: RunHistoryItem, b: RunHistoryItem) => moment(a.startTime).diff(moment(b.startTime)));

      setRunHistory(formattedRuns);
    } catch (error) {
      console.error("Error fetching pipeline runs by date range:", error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Reset date filter
  const handleResetDateFilter = () => {
    setDateRange(null);
    handleDateRangeChange(null, ["", ""]);
  };

  // Show Activity Modal
  const showActivityModal = (activity: ActivityItem) => {
    setSelectedActivity(activity);
    setActivityModalVisible(true);
  };

  // Fetch activity data for a selected run
  const fetchActivityRuns = async (runId: string) => {
    if (!pipeline) return;

    setSelectedRunId(runId);
    setActivityLoading(true);
    setActivityData(null);

    try {
      const response = await getPipelineActivityRuns(pipeline.name, runId);

      setActivityData(response as unknown as PipelineActivityRunsResponse);
    } catch (error) {
      console.error("Error fetching pipeline activity runs:", error);
    } finally {
      setActivityLoading(false);
    }
  };

  if (!pipeline) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Succeeded":
        return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
      case "Failed":
        return <CloseCircleOutlined style={{ color: "#f5222d" }} />;
      case "InProgress":
        return <ClockCircleOutlined style={{ color: "#faad14" }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Succeeded":
        return "success";
      case "Failed":
        return "error";
      case "InProgress":
        return "warning";
      default:
        return "default";
    }
  };

  // Table columns for run history
  const columns = [
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status}
        </Tag>
      ),
    },
    {
      title: "Start Time",
      dataIndex: "startTime",
      key: "startTime",
      render: (startTime: string) => (startTime === "N/A" ? "N/A" : moment.tz(startTime, "America/Chicago").format("MM/DD/YYYY HH:mm")),
    },
    {
      title: "End Time",
      dataIndex: "endTime",
      key: "endTime",
      render: (endTime: string) => (endTime === "N/A" ? "N/A" : moment.tz(endTime, "America/Chicago").format("MM/DD/YYYY HH:mm")),
    },
    {
      title: "Duration",
      dataIndex: "formattedDuration",
      key: "duration",
    },
    {
      title: "Run ID",
      dataIndex: "runId",
      key: "runId",
      ellipsis: true,
    },
    {
      title: "Actions",
      key: "actions",
      render: (text: string, record: RunHistoryItem) => (
        <Button type="link" onClick={() => fetchActivityRuns(record.runId)} disabled={activityLoading && selectedRunId === record.runId}>
          View Activities
        </Button>
      ),
    },
  ];

  // Activity table columns
  const activityColumns = [
    {
      title: "Activity Name",
      dataIndex: "activityName",
      key: "activityName",
      width: "30%",
      ellipsis: true,
    },
    {
      title: "Type",
      dataIndex: "activityType",
      key: "activityType",
      width: "20%",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: "15%",
      render: (status: string) => (
        <Tag icon={getStatusIcon(status)} color={getStatusColor(status)}>
          {status}
        </Tag>
      ),
    },
    {
      title: "Duration",
      dataIndex: "durationInMs",
      key: "duration",
      width: "15%",
      render: (durationInMs: number) => formatDuration(durationInMs),
    },
    {
      title: "Actions",
      key: "actions",
      width: "20%",
      render: (_: string, record: ActivityItem) => (
        <Button type="link" onClick={() => showActivityModal(record)}>
          View Details
        </Button>
      ),
    },
  ];

  console.log("runHistory", runHistory);

  // Prepare data for the chart
  const chartData: ChartDataItem[] = runHistory.map((run, index) => ({
    date: moment(run.startTime).format("DD MMM"),
    status: run.status,
    duration: run.formattedDuration,
    runId: run.runId,
    index: runHistory.length - index,
  }));

  // Count runs by status
  const statusCounts = runHistory.reduce((acc: Record<string, number>, run) => {
    acc[run.status] = (acc[run.status] || 0) + 1;
    return acc;
  }, {});

  return (
    <>
      <Modal title={<Title level={4}>Pipeline Details</Title>} open={visible} onCancel={onClose} footer={null} width={900}>
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px 0" }}>
            <Spin size="large" />
          </div>
        ) : (
          <>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Card>
                  <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 16 }}>
                    <div>
                      <Text strong>Total Runs: {runHistory.length}</Text>

                      <div>
                        {Object.entries(statusCounts).map(([status, count]) => (
                          <Tag key={status} icon={getStatusIcon(status)} color={getStatusColor(status)} style={{ marginRight: 8 }}>
                            {status}: {count}
                          </Tag>
                        ))}
                      </div>
                    </div>
                    <div>
                      <RangePicker onChange={handleDateRangeChange} value={dateRange} style={{ marginRight: 8 }} />
                      <Button onClick={handleResetDateFilter} icon={<SearchOutlined />}>
                        Reset Filter
                      </Button>
                    </div>
                  </div>

                  {runHistory.length > 98 && (
                    <div style={{ marginBottom: 16, alignSelf: "flex-end", width: "100%" }}>
                      <Tag color="blue" icon={<InfoCircleOutlined />}>
                        It can only show 100 runs from the start date
                      </Tag>
                    </div>
                  )}

                  {historyLoading ? (
                    <div style={{ textAlign: "center", padding: "20px 0" }}>
                      <Spin />
                    </div>
                  ) : chartData.length > 0 ? (
                    <Line
                      data={chartData}
                      xField="date"
                      yField="duration"
                      height={200}
                      point={{
                        size: 5,
                        shape: "circle",
                      }}
                      meta={{
                        date: {
                          alias: "Run Date",
                        },
                        duration: {
                          alias: "Duration",
                        },
                      }}
                      xAxis={{
                        label: {
                          formatter: (text: string) => {
                            return moment(text).format("DD MMM YYYY");
                          },
                        },
                      }}
                    />
                  ) : (
                    <Empty description="No run history available" />
                  )}
                </Card>
              </Col>

              <Col span={24}>
                <Card title={<Text strong>Run History</Text>}>
                  <Table
                    dataSource={runHistory}
                    columns={columns}
                    rowKey="runId"
                    pagination={{ pageSize: 5 }}
                    loading={historyLoading}
                    size="small"
                    rowClassName={record => (record.runId === selectedRunId ? "ant-table-row-selected" : "")}
                  />
                </Card>
              </Col>

              {/* button to clear activity data */}
              {selectedRunId && activityData ? (
                <>
                  <Divider>Activity Table</Divider>
                  <div style={{ display: "flex", justifyContent: "flex-end", marginBottom: 16, width: "100%" }}>
                    <Button onClick={() => setActivityData(null)}>Clear Activity Data</Button>
                  </div>

                  <Col span={24}>
                    <Card
                      title={<Text strong>Pipeline Activities</Text>}
                      extra={
                        <Space>
                          <Text type="secondary">Run ID: {selectedRunId}</Text>
                          <Progress
                            percent={activityData.progressPercentage || 0}
                            size="small"
                            status={activityData.progressPercentage < 100 ? "active" : "success"}
                            style={{ width: 100, marginLeft: 16 }}
                          />
                        </Space>
                      }
                    >
                      {activityLoading ? (
                        <div style={{ textAlign: "center", padding: "20px 0" }}>
                          <Spin />
                          <div style={{ marginTop: 16 }}>Loading activities...</div>
                        </div>
                      ) : activityData.activities && activityData.activities.length > 0 ? (
                        <div>
                          <div style={{ marginBottom: 16 }}>
                            <Row gutter={16}>
                              <Col span={8}>
                                <Card size="small">
                                  <Statistic title="Total Activities" value={activityData.totalActivities} valueStyle={{ fontSize: "14px" }} />
                                </Card>
                              </Col>
                              <Col span={8}>
                                <Card size="small">
                                  <Statistic title="Completed Activities" value={activityData.completedActivities} valueStyle={{ fontSize: "14px" }} />
                                </Card>
                              </Col>
                              <Col span={8}>
                                <Card size="small">
                                  <Statistic title="Progress" value={`${activityData.progressPercentage}%`} valueStyle={{ fontSize: "14px" }} />
                                </Card>
                              </Col>
                            </Row>
                          </div>

                          <Table
                            dataSource={activityData.activities}
                            columns={activityColumns}
                            rowKey="activityName"
                            pagination={{ pageSize: 10 }}
                            size="small"
                          />
                        </div>
                      ) : (
                        <Empty description="No activity data available." />
                      )}
                    </Card>
                  </Col>
                </>
              ) : activityLoading ? (
                <div style={{ textAlign: "center", padding: "20px 0", width: "100%" }}>
                  <Spin />
                  <div style={{ marginTop: 16 }}>Loading activities...</div>
                </div>
              ) : null}
            </Row>
          </>
        )}
      </Modal>

      <ActivityDetailModal visible={activityModalVisible} onClose={() => setActivityModalVisible(false)} activityData={selectedActivity} />
    </>
  );
};

export default PipelineDetailDialog;
