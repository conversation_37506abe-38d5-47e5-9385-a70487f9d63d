import React, { useCallback, useEffect, useState } from "react";
import { Row, Col, Input, Select, Button } from "antd";
import { SearchOutlined, FilterOutlined, ClearOutlined, ReloadOutlined } from "@ant-design/icons";
import { PipelineFilters as FilterTypes } from "../../../../../__redux/pipelineDashSlice";
import debounce from "lodash/debounce";

const { Search } = Input;
const { Option } = Select;

interface PipelineFiltersProps {
  filters: FilterTypes;
  onFilterChange: (key: keyof FilterTypes, value: string) => void;
  // categories: string[];
  statuses: string[];
  fetchAllPipelines: () => void;
}

const PipelineFilters: React.FC<PipelineFiltersProps> = ({
  filters,
  onFilterChange,
  // categories = ["All Categories"],
  statuses = ["All Status", "Succeeded", "InProgress", "Failed"],
  fetchAllPipelines,
}) => {
  const [searchInputValue, setSearchInputValue] = useState(filters.searchTerm);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onFilterChange("searchTerm", value);
    }, 500),
    [onFilterChange]
  );

  useEffect(() => {
    setSearchInputValue(filters.searchTerm);
  }, [filters.searchTerm]);

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInputValue(value);
    debouncedSearch(value);
  };

  const handleSearchSubmit = (value: string) => {
    debouncedSearch.cancel();
    onFilterChange("searchTerm", value);
  };

  // const handleCategoryChange = (value: string) => {
  //   onFilterChange("category", value);
  // };

  const handleStatusChange = (value: string) => {
    onFilterChange("status", value);
  };

  const handleClearFilters = () => {
    onFilterChange("searchTerm", "");
    onFilterChange("category", "All Categories");
    onFilterChange("status", "All Status");
  };

  const hasActiveFilters = filters.searchTerm || filters.category !== "All Categories" || filters.status !== "All Status";

  const handleRefreshPipelines = () => {
    fetchAllPipelines();
    handleClearFilters();
  };

  return (
    <Row gutter={[16, 16]} align="middle" style={{ marginBottom: 24 }}>
      <Col xs={24} md={12} lg={12}>
        <Search
          placeholder="Search pipelines..."
          onSearch={handleSearchSubmit}
          onChange={handleSearchInputChange}
          value={searchInputValue}
          style={{ width: "100%" }}
          prefix={<SearchOutlined />}
          allowClear
        />
      </Col>
      {/* <Col xs={12} md={6} lg={4}>
        <Select style={{ width: "100%" }} value={filters.category} onChange={handleCategoryChange} suffixIcon={<FilterOutlined />}>
          {categories.map(category => (
            <Option key={category} value={category}>
              {category}
            </Option>
          ))}
        </Select>
      </Col> */}
      <Col xs={12} md={6} lg={4}>
        <Select style={{ width: "100%" }} value={filters.status} onChange={handleStatusChange} suffixIcon={<FilterOutlined />}>
          {statuses.map(status => (
            <Option key={status} value={status}>
              {status}
            </Option>
          ))}
        </Select>
      </Col>
      <Col xs={24} lg={4}>
        <Button icon={<ReloadOutlined />} type="primary" onClick={handleRefreshPipelines} style={{ width: "100%" }}>
          Refresh Pipelines
        </Button>
      </Col>
      <Col xs={0} lg={4}>
        {hasActiveFilters && (
          <Button icon={<ClearOutlined />} onClick={handleClearFilters}>
            Clear Filters
          </Button>
        )}
      </Col>
    </Row>
  );
};

export default PipelineFilters;
