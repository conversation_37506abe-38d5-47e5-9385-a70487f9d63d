import React from "react";
import { Typography, Space, Tag } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import StatisticsCards from "./StatisticsCards";
import PipelineFilters from "./PipelineFilters";
import PipelineTable from "./PipelineTable";
import { PipelineFilters as FilterTypes, PipelineOverviewStats, PipelineRun } from "../../../../../__redux/pipelineDashSlice";
import { RootState } from "../../../../store";
import { useSelector } from "react-redux";
import moment from "moment";

const { Title } = Typography;

interface PipelineOverviewContentProps {
  stats: PipelineOverviewStats;
  pipelines: PipelineRun[];
  loading: boolean;
  cardStatsLoader: boolean;
  filters: FilterTypes;
  onFilterChange: (key: keyof FilterTypes, value: string) => void;
  onRunNow: (pipelineName: string) => void;
  onViewDetails: (pipeline: PipelineRun) => void;
  onRetry: (pipelineName: string) => void;
  pagination: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  categories: string[];
  totalPipelines: number;
  onCancelRun: (pipelineName: string) => void;
  fetchAllPipelines: () => void;
}

const PipelineOverviewContent: React.FC<PipelineOverviewContentProps> = ({
  stats,
  pipelines,
  loading,
  cardStatsLoader,
  filters,
  onFilterChange,
  onRunNow,
  onViewDetails,
  onRetry,
  pagination,
  // categories,
  totalPipelines,
  onCancelRun,
  fetchAllPipelines,
}) => {
  const { overview } = useSelector((state: RootState) => state.pipelineDash);
  return (
    <div>
      <Space direction="vertical" size={8} style={{ marginBottom: 16 }}>
        <Title level={2}>Pipeline Dashboard</Title>
        {overview.startTime && overview.endTime && (
          <Tag icon={<InfoCircleOutlined />} color="processing">
            Metrics for 100 runs starting from {moment(overview.startTime).format("DD MMM")} to {moment(overview.endTime).format("DD MMM YYYY")}
          </Tag>
        )}
      </Space>

      <StatisticsCards stats={stats} totalPipelines={totalPipelines} loading={cardStatsLoader} />

      <PipelineFilters
        filters={filters}
        onFilterChange={onFilterChange}
        // categories={categories}
        statuses={["All Status", "Succeeded", "InProgress", "Failed", "Cancelled"]}
        fetchAllPipelines={fetchAllPipelines}
      />

      <PipelineTable
        pipelines={pipelines}
        loading={loading}
        onRunNow={onRunNow}
        onViewDetails={onViewDetails}
        onRetry={onRetry}
        pagination={pagination}
        onCancelRun={onCancelRun}
      />
    </div>
  );
};

export default PipelineOverviewContent;
