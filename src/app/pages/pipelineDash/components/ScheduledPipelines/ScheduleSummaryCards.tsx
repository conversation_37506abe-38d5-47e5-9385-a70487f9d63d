import { CheckCircleOutlined, CloseCircleOutlined, UnorderedListOutlined } from "@ant-design/icons";
import { Card, Col, Row, Statistic, Typography } from "antd";
import React from "react";
import { PipelineSchedule } from "../../../../../__redux/pipelineDashSlice";

const { Title } = Typography;

interface ScheduleSummaryCardsProps {
  pipelineSchedules: PipelineSchedule[];
  schedulesLoading: boolean;
}

const ScheduleSummaryCards: React.FC<ScheduleSummaryCardsProps> = ({ pipelineSchedules, schedulesLoading }) => {
  const totalSchedules = pipelineSchedules?.length || 0;
  const activeSchedules = pipelineSchedules?.filter(schedule => schedule?.status === "Started").length || 0;
  const stoppedSchedules = pipelineSchedules?.filter(schedule => schedule?.status === "Stopped").length || 0;

  const uniquePipelines = new Set<string>();
  pipelineSchedules?.forEach(schedule => {
    schedule?.properties?.pipelines?.forEach(pipeline => {
      if (pipeline?.pipelineReference?.referenceName) {
        uniquePipelines.add(pipeline.pipelineReference.referenceName);
      }
    });
  });
  const totalUniquePipelines = uniquePipelines.size;

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} lg={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={schedulesLoading}
            title={<Title level={5}>Total Schedules</Title>}
            value={totalSchedules}
            valueStyle={{ color: "#1890ff", fontSize: "30px" }}
            prefix={<UnorderedListOutlined style={{ fontSize: 24, color: "#1890ff", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={schedulesLoading}
            title={<Title level={5}>Active Schedules</Title>}
            value={activeSchedules}
            valueStyle={{ color: "#52c41a", fontSize: "30px" }}
            prefix={<CheckCircleOutlined style={{ fontSize: 24, color: "#52c41a", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={schedulesLoading}
            title={<Title level={5}>Stopped Schedules</Title>}
            value={stoppedSchedules}
            valueStyle={{ color: "#f5222d", fontSize: "30px" }}
            prefix={<CloseCircleOutlined style={{ fontSize: 24, color: "#f5222d", marginRight: 8 }} />}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card variant="outlined" style={{ height: "100%" }}>
          <Statistic
            loading={schedulesLoading}
            title={<Title level={5}>Unique Pipelines</Title>}
            value={totalUniquePipelines}
            valueStyle={{ color: "#722ed1", fontSize: "30px" }}
            prefix={<UnorderedListOutlined style={{ fontSize: 24, color: "#722ed1", marginRight: 8 }} />}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default ScheduleSummaryCards;
