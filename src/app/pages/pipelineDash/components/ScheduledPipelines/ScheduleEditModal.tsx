import { Button, Col, Form, Input, Modal, Row, Select, Space, Typography, notification } from "antd";
import React, { useEffect, useState } from "react";
import { PipelineSchedule } from "../../../../../__redux/pipelineDashSlice";
import { ScheduleFormValues } from "./types";
import { formatTime, formatWeekdays, isBusinessHoursPattern, formatFrequency, formatMonthDays, formatMonths } from "./utils/formatters";
import { FREQUENCIES, SCHEDULE_PATTERNS, TIME_ZONES, WEEKDAYS, MONTHS, MONTH_DAYS } from "./utils/schedulePatterns";

interface ScheduleEditModalProps {
  visible: boolean;
  currentSchedule: PipelineSchedule | null;
  updating: boolean;
  onCancel: () => void;
  onUpdate: (values: ScheduleFormValues) => void;
}

const ScheduleEditModal: React.FC<ScheduleEditModalProps> = ({ visible, currentSchedule, updating, onCancel, onUpdate }) => {
  const [form] = Form.useForm();
  const [currentFrequency, setCurrentFrequency] = useState<string>("Week");

  useEffect(() => {
    if (visible && currentSchedule) {
      try {
        const recurrence = currentSchedule?.properties?.recurrence || {};
        const scheduleData = recurrence?.schedule || {};
        const frequency = recurrence?.frequency || "Week";

        form.setFieldsValue({
          triggerName: currentSchedule?.name || "",
          frequency: frequency,
          interval: recurrence?.interval || 1,
          timeZone: recurrence?.timeZone || "UTC",
          weekDays: scheduleData?.weekDays || [],
          hours: scheduleData?.hours || [],
          minutes: scheduleData?.minutes || [],
          monthDays: scheduleData?.monthDays ? scheduleData.monthDays : [],
          months: scheduleData?.months ? scheduleData.months : [],
          description: currentSchedule?.description || "",
        });

        setCurrentFrequency(frequency);
      } catch (error) {
        console.error("Error setting form fields:", error);
        notification.error({
          message: "Error Loading Schedule Data",
          description: "Could not load schedule data. Please try again.",
        });
      }
    }
  }, [visible, currentSchedule, form]);

  const applyPattern = (type: "weekDays" | "hours" | "minutes" | "monthDays" | "months", pattern: string) => {
    try {
      const fieldName = type;
      const patternValues = SCHEDULE_PATTERNS[type][pattern as keyof (typeof SCHEDULE_PATTERNS)[typeof type]];
      form.setFieldValue(fieldName, patternValues);
    } catch (error) {
      console.error(`Error applying ${type} pattern ${pattern}:`, error);
      notification.error({
        message: "Error Applying Pattern",
        description: `Could not apply the selected pattern. Please try selecting options manually.`,
      });
    }
  };

  const generateScheduleSummary = () => {
    try {
      const formValues = form.getFieldsValue();

      const requiredFieldsMap = {
        Minute: [],
        Hour: ["minutes"],
        Day: ["hours", "minutes"],
        Week: ["weekDays", "hours", "minutes"],
        Month: ["monthDays", "hours", "minutes"],
        Year: ["months", "monthDays", "hours", "minutes"],
      };

      const requiredFields = requiredFieldsMap[formValues.frequency as keyof typeof requiredFieldsMap] || [];
      const missingRequiredFields = requiredFields.filter(field => !formValues[field]?.length);

      if (missingRequiredFields.length > 0) {
        return (
          <div style={{ marginTop: 16, padding: 16, background: "#f8f8f8", borderRadius: 4 }}>
            <Typography.Text>Please complete the schedule configuration. Missing: {missingRequiredFields.join(", ")}</Typography.Text>
          </div>
        );
      }

      const frequencyText = formatFrequency(formValues.frequency, formValues.interval);
      const { isBusinessHours, period } = isBusinessHoursPattern(formValues.hours || []);
      const timeFormat = isBusinessHours ? period : formatTime(formValues.hours || [], formValues.minutes || []);

      const weekdaysText = formatWeekdays(formValues.weekDays);
      const monthDaysText = formatMonthDays(formValues.monthDays);
      const monthsText = formatMonths(formValues.months);

      let summaryText = "";

      switch (formValues.frequency) {
        case "Minute":
          summaryText = `Runs ${frequencyText}`;
          break;
        case "Hour":
          summaryText = `Runs ${frequencyText} at ${(formValues.minutes || []).map((m: number) => m.toString().padStart(2, "0")).join(", ")} minute(s)`;
          break;
        case "Day":
          summaryText = `Runs ${frequencyText}, ${timeFormat}`;
          break;
        case "Week":
          summaryText = `Runs ${frequencyText} on ${weekdaysText}, ${timeFormat}`;
          break;
        case "Month":
          summaryText = `Runs ${frequencyText} on ${monthDaysText}, ${timeFormat}`;
          break;
        case "Year":
          summaryText = `Runs ${frequencyText} in ${monthsText} on ${monthDaysText}, ${timeFormat}`;
          break;
        default:
          summaryText = `Runs ${frequencyText}`;
      }

      return (
        <div style={{ marginTop: 16, padding: 16, background: "#f8f8f8", borderRadius: 4 }}>
          <Typography.Title level={5}>Schedule Summary</Typography.Title>
          <Typography.Text>{summaryText}</Typography.Text>
          <br />
          <Typography.Text type="secondary">{`Time Zone: ${formValues.timeZone || "UTC"}`}</Typography.Text>
        </div>
      );
    } catch (error) {
      console.error("Error generating schedule summary:", error);
      return (
        <div style={{ marginTop: 16, padding: 16, background: "#fff0f0", borderRadius: 4 }}>
          <Typography.Text type="danger">Error generating schedule summary. Please complete all required fields.</Typography.Text>
        </div>
      );
    }
  };

  const hourOptions = Array.from({ length: 24 }, (_, i) => i);

  const minuteOptions = Array.from({ length: 60 }, (_, i) => i);

  return (
    <Modal
      title="Edit Schedule"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={updating} onClick={() => form.submit()}>
          Update Schedule
        </Button>,
      ]}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onUpdate}
        onValuesChange={changedValues => {
          if (changedValues.frequency) {
            setCurrentFrequency(changedValues.frequency);
          }

          form.validateFields(["weekDays", "hours", "minutes"]).catch(() => {});
        }}
      >
        <Form.Item name="triggerName" label="Schedule Name" rules={[{ required: true, message: "Please enter the schedule name" }]}>
          <Input disabled />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <Input.TextArea rows={2} />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="frequency" label="Frequency" rules={[{ required: true, message: "Please select a frequency" }]}>
              <Select>
                {FREQUENCIES.map(freq => (
                  <Select.Option key={freq} value={freq}>
                    {freq}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="interval" label="Interval" rules={[{ required: true, message: "Please enter an interval" }]}>
              <Input type="number" min={1} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="timeZone" label="Time Zone" rules={[{ required: true, message: "Please select a time zone" }]}>
          <Select>
            {TIME_ZONES.map(tz => (
              <Select.Option key={tz} value={tz}>
                {tz}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Only show weekDays for Week frequency */}
        {["Week"].includes(currentFrequency) && (
          <Form.Item label="Days of Week">
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Form.Item
                  name="weekDays"
                  rules={[
                    {
                      required: ["Week"].includes(currentFrequency),
                      message: "Please select at least one day",
                    },
                  ]}
                  noStyle
                >
                  <Select mode="multiple">
                    {WEEKDAYS.map(day => (
                      <Select.Option key={day} value={day}>
                        {day}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Space>
                  <Button size="small" onClick={() => applyPattern("weekDays", "WEEKDAYS")}>
                    Weekdays
                  </Button>
                  <Button size="small" onClick={() => applyPattern("weekDays", "WEEKEND")}>
                    Weekend
                  </Button>
                  <Button size="small" onClick={() => applyPattern("weekDays", "ALL_WEEK")}>
                    All Week
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form.Item>
        )}

        {/* Only show monthDays for Month and Year frequencies */}
        {["Month", "Year"].includes(currentFrequency) && (
          <Form.Item label="Days of Month">
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Form.Item
                  name="monthDays"
                  rules={[
                    {
                      required: ["Month", "Year"].includes(currentFrequency),
                      message: "Please select at least one day of month",
                    },
                  ]}
                  noStyle
                >
                  <Select mode="multiple">
                    {MONTH_DAYS.map(day => (
                      <Select.Option key={`day-${day}`} value={day}>
                        {day}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Space>
                  <Button size="small" onClick={() => applyPattern("monthDays", "START_OF_MONTH")}>
                    Start of Month
                  </Button>
                  <Button size="small" onClick={() => applyPattern("monthDays", "MID_MONTH")}>
                    Mid-Month
                  </Button>
                  <Button size="small" onClick={() => applyPattern("monthDays", "END_OF_MONTH")}>
                    End of Month
                  </Button>
                  <Button size="small" onClick={() => applyPattern("monthDays", "QUARTERLY")}>
                    Quarterly
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form.Item>
        )}

        {/* Only show months for Year frequency */}
        {["Year"].includes(currentFrequency) && (
          <Form.Item label="Months">
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Form.Item
                  name="months"
                  rules={[
                    {
                      required: ["Year"].includes(currentFrequency),
                      message: "Please select at least one month",
                    },
                  ]}
                  noStyle
                >
                  <Select mode="multiple">
                    {MONTHS.map(month => (
                      <Select.Option key={month} value={month}>
                        {month}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Space>
                  <Button size="small" onClick={() => applyPattern("months", "ALL_MONTHS")}>
                    All Months
                  </Button>
                  <Button size="small" onClick={() => applyPattern("months", "FIRST_QUARTER")}>
                    Q1
                  </Button>
                  <Button size="small" onClick={() => applyPattern("months", "SECOND_QUARTER")}>
                    Q2
                  </Button>
                  <Button size="small" onClick={() => applyPattern("months", "THIRD_QUARTER")}>
                    Q3
                  </Button>
                  <Button size="small" onClick={() => applyPattern("months", "FOURTH_QUARTER")}>
                    Q4
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form.Item>
        )}

        {/* Show Hours for Day, Week, Month, Year frequencies */}
        {["Day", "Week", "Month", "Year"].includes(currentFrequency) && (
          <Form.Item label="Hours">
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Form.Item
                  name="hours"
                  rules={[
                    {
                      required: ["Day", "Week", "Month", "Year"].includes(currentFrequency),
                      message: "Please select at least one hour",
                    },
                  ]}
                  noStyle
                >
                  <Select mode="multiple">
                    {hourOptions.map(hour => (
                      <Select.Option key={`hour-${hour}`} value={hour}>
                        {hour.toString().padStart(2, "0")}:00 ({hour < 12 ? (hour === 0 ? "12 AM" : `${hour} AM`) : hour === 12 ? "12 PM" : `${hour - 12} PM`})
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Space wrap>
                  <Button size="small" onClick={() => applyPattern("hours", "ALL_DAY")}>
                    All Day
                  </Button>
                  <Button size="small" onClick={() => applyPattern("hours", "BUSINESS_HOURS")}>
                    Business Hours (9AM-5PM)
                  </Button>
                  <Button size="small" onClick={() => applyPattern("hours", "EXTENDED_BUSINESS")}>
                    Extended Business (8AM-6PM)
                  </Button>
                  <Button size="small" onClick={() => applyPattern("hours", "MORNING")}>
                    Morning (6AM-11AM)
                  </Button>
                  <Button size="small" onClick={() => applyPattern("hours", "AFTERNOON")}>
                    Afternoon (12PM-5PM)
                  </Button>
                  <Button size="small" onClick={() => applyPattern("hours", "EVENING")}>
                    Evening (6PM-11PM)
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form.Item>
        )}

        {/* Show Minutes for Hour, Day, Week, Month, Year frequencies */}
        {["Hour", "Day", "Week", "Month", "Year"].includes(currentFrequency) && (
          <Form.Item label="Minutes">
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Form.Item
                  name="minutes"
                  rules={[
                    {
                      required: ["Hour", "Day", "Week", "Month", "Year"].includes(currentFrequency),
                      message: "Please select at least one minute",
                    },
                  ]}
                  noStyle
                >
                  <Select mode="multiple">
                    {minuteOptions.map(minute => (
                      <Select.Option key={`minute-${minute}`} value={minute}>
                        {minute.toString().padStart(2, "0")}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Space wrap>
                  <Button size="small" onClick={() => applyPattern("minutes", "EVERY_HOUR")}>
                    On the hour
                  </Button>
                  <Button size="small" onClick={() => applyPattern("minutes", "EVERY_30_MIN")}>
                    Every 30 minutes
                  </Button>
                  <Button size="small" onClick={() => applyPattern("minutes", "EVERY_15_MIN")}>
                    Every 15 minutes
                  </Button>
                  <Button size="small" onClick={() => applyPattern("minutes", "EVERY_10_MIN")}>
                    Every 10 minutes
                  </Button>
                  <Button size="small" onClick={() => applyPattern("minutes", "EVERY_5_MIN")}>
                    Every 5 minutes
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form.Item>
        )}

        {/* Schedule Summary */}
        {generateScheduleSummary()}
      </Form>
    </Modal>
  );
};

export default ScheduleEditModal;
