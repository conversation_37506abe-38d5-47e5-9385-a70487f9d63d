import { Alert, Card, Input, Space, Typography, notification } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PipelineSchedule, setPipelineSchedules, setSchedulesError, setSchedulesLoading } from "../../../../../__redux/pipelineDashSlice";
import { UpdateTriggerScheduleRequestBody, getPipelineSchedules, updateTriggerSchedule } from "../../../../api/pipelinDashApi";
import { RootState } from "../../../../store";
import ScheduleEditModal from "./ScheduleEditModal";
import ScheduleSummaryCards from "./ScheduleSummaryCards";
import ScheduleTable from "./ScheduleTable";
import { ScheduleFormValues } from "./types";

const { Title, Text } = Typography;
const { Search } = Input;

const ScheduledPipelinesContent: React.FC = () => {
  const dispatch = useDispatch();
  const { pipelineSchedules, schedulesLoading, schedulesError } = useSelector((state: RootState) => state.pipelineDash);
  const [searchText, setSearchText] = useState<string>("");
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [currentSchedule, setCurrentSchedule] = useState<PipelineSchedule | null>(null);
  const [updating, setUpdating] = useState<boolean>(false);

  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        dispatch(setSchedulesLoading(true));
        dispatch(setSchedulesError(null));
        const data = await getPipelineSchedules();
        dispatch(setPipelineSchedules(data?.schedules || []));
      } catch (error) {
        dispatch(setSchedulesError("Failed to fetch pipeline schedules"));
        console.error("Error fetching pipeline schedules:", error);
      } finally {
        dispatch(setSchedulesLoading(false));
      }
    };

    fetchSchedules();
  }, [dispatch]);

  const handleEdit = (schedule: PipelineSchedule) => {
    try {
      setCurrentSchedule(schedule);
      setEditModalVisible(true);
    } catch (error) {
      console.error("Error opening edit modal:", error);
      notification.error({
        message: "Error Opening Editor",
        description: "There was an error loading the schedule data. Please try again.",
      });
    }
  };

  const handleUpdateSchedule = async (values: ScheduleFormValues) => {
    if (!currentSchedule) {
      notification.error({
        message: "Update Failed",
        description: "No schedule selected for update.",
      });
      return;
    }

    try {
      setUpdating(true);

      const requiredFieldsMap = {
        Minute: [],
        Hour: ["minutes"],
        Day: ["hours", "minutes"],
        Week: ["weekDays", "hours", "minutes"],
        Month: ["monthDays", "hours", "minutes"],
        Year: ["months", "monthDays", "hours", "minutes"],
      };

      const requiredFields = requiredFieldsMap[values.frequency as keyof typeof requiredFieldsMap] || [];
      const missingRequiredFields = requiredFields.filter(field => {
        const fieldValue = values[field as keyof ScheduleFormValues];
        return !fieldValue || (Array.isArray(fieldValue) && fieldValue.length === 0);
      });

      if (missingRequiredFields.length > 0) {
        notification.error({
          message: "Validation Error",
          description: `Please make sure you have selected at least one option for ${missingRequiredFields.join(", ")}.`,
        });
        setUpdating(false);
        return;
      }

      const scheduleObj: {
        weekDays?: string[];
        hours?: number[];
        minutes?: number[];
        monthDays?: number[];
        months?: string[];
      } = {};

      if (values.frequency === "Week") {
        scheduleObj.weekDays = values.weekDays;
      }

      if (["Day", "Week", "Month", "Year"].includes(values.frequency)) {
        scheduleObj.hours = values.hours;
      }

      if (["Hour", "Day", "Week", "Month", "Year"].includes(values.frequency)) {
        scheduleObj.minutes = values.minutes;
      }

      if (["Month", "Year"].includes(values.frequency) && values.monthDays) {
        scheduleObj.monthDays = values.monthDays;
      }

      if (["Year"].includes(values.frequency) && values.months) {
        scheduleObj.months = values.months;
      }

      const objectToUpdate: UpdateTriggerScheduleRequestBody = {
        triggerName: values.triggerName,
        scheduleConfig: {
          recurrence: {
            frequency: values.frequency,
            interval: Number(values.interval),
            startTime: currentSchedule?.properties?.recurrence?.startTime || new Date().toISOString(),
            timeZone: values.timeZone,
            schedule: scheduleObj,
          },
        },
      };

      const validateSchedule = () => {
        const schedule = objectToUpdate.scheduleConfig.recurrence.schedule;

        switch (values.frequency) {
          case "Minute":
            if (Object.keys(schedule).length > 0) {
              return { valid: false, message: "Minute frequency doesn't support schedule properties" };
            }
            break;
          case "Hour":
            if (!schedule.minutes || schedule.minutes.length === 0) {
              return { valid: false, message: "Hour frequency requires minutes" };
            }
            if (schedule.weekDays || schedule.monthDays || schedule.months) {
              return { valid: false, message: "Hour frequency only supports minutes" };
            }
            break;
          case "Day":
            if (!schedule.hours || schedule.hours.length === 0 || !schedule.minutes || schedule.minutes.length === 0) {
              return { valid: false, message: "Day frequency requires hours and minutes" };
            }
            if (schedule.weekDays || schedule.monthDays || schedule.months) {
              return { valid: false, message: "Day frequency only supports hours and minutes" };
            }
            break;
          case "Week":
            if (
              !schedule.weekDays ||
              schedule.weekDays.length === 0 ||
              !schedule.hours ||
              schedule.hours.length === 0 ||
              !schedule.minutes ||
              schedule.minutes.length === 0
            ) {
              return { valid: false, message: "Week frequency requires weekDays, hours, and minutes" };
            }
            if (schedule.monthDays || schedule.months) {
              return { valid: false, message: "Week frequency only supports weekDays, hours, and minutes" };
            }
            break;
          case "Month":
            if (!schedule.hours || schedule.hours.length === 0 || !schedule.minutes || schedule.minutes.length === 0) {
              return { valid: false, message: "Month frequency requires hours and minutes" };
            }
            if (!schedule.monthDays || schedule.monthDays.length === 0) {
              return { valid: false, message: "Month frequency requires monthDays" };
            }
            if (schedule.weekDays || schedule.months) {
              return { valid: false, message: "Month frequency only supports monthDays, hours, and minutes" };
            }
            break;
          case "Year":
            if (!schedule.hours || schedule.hours.length === 0 || !schedule.minutes || schedule.minutes.length === 0) {
              return { valid: false, message: "Year frequency requires hours and minutes" };
            }
            if (!schedule.monthDays || schedule.monthDays.length === 0) {
              return { valid: false, message: "Year frequency requires monthDays" };
            }
            if (!schedule.months || schedule.months.length === 0) {
              return { valid: false, message: "Year frequency requires months" };
            }
            break;
        }

        return { valid: true };
      };

      const validationResult = validateSchedule();
      if (!validationResult.valid) {
        notification.error({
          message: "Validation Error",
          description: validationResult.message,
        });
        setUpdating(false);
        return;
      }

      const updateResponse = await updateTriggerSchedule(objectToUpdate);

      const data = await getPipelineSchedules();
      dispatch(setPipelineSchedules(data?.schedules || []));

      const formatUpdatedSchedule = () => {
        if (!updateResponse.updatedTrigger) return "";

        const { properties } = updateResponse.updatedTrigger;
        const recurrence = properties.recurrence;
        if (!recurrence) return "";

        let summary = "";

        summary += `${recurrence.frequency}${recurrence.interval > 1 ? ` (every ${recurrence.interval} ${recurrence.frequency.toLowerCase()}s)` : ""}`;

        if (recurrence.frequency === "Year" && recurrence.schedule?.months?.length) {
          const months = recurrence.schedule.months.join(", ");
          summary += ` in ${months}`;
        }

        if (recurrence.schedule?.monthDays?.length) {
          summary += ` on day${recurrence.schedule.monthDays.length > 1 ? "s" : ""} ${recurrence.schedule.monthDays.join(", ")}`;
        }

        if (recurrence.schedule?.weekDays?.length) {
          summary += ` on ${recurrence.schedule.weekDays.join(", ")}`;
        }

        if (recurrence.schedule?.hours?.length && recurrence.schedule?.minutes?.length) {
          const hours = recurrence.schedule.hours.map(
            (h: number) => `${h < 10 ? "0" : ""}${h}:${recurrence.schedule.minutes.map((m: number) => `${m < 10 ? "0" : ""}${m}`).join(", ")}`
          );

          if (hours.length <= 3) {
            summary += ` at ${hours.join(", ")}`;
          } else {
            summary += ` at ${recurrence.schedule.hours.length} hour(s) × ${recurrence.schedule.minutes.length} minute(s)`;
          }
        }

        return summary;
      };

      notification.success({
        message: "Schedule Updated",
        description: (
          <div>
            <p>The schedule "{updateResponse.triggerName}" has been successfully updated.</p>
            <p>New schedule: {formatUpdatedSchedule()}</p>
            <p>Time Zone: {updateResponse.updatedTrigger.properties.recurrence.timeZone}</p>
            <p>Status: {updateResponse.updatedTrigger.properties.runtimeState}</p>
          </div>
        ),
        duration: 8,
      });

      setEditModalVisible(false);
      setCurrentSchedule(null);
    } catch (error) {
      console.error("Error updating schedule:", error);
      notification.error({
        message: "Update Failed",
        description: "Failed to update the schedule. Please try again.",
      });
    } finally {
      setUpdating(false);
    }
  };

  const filteredSchedules =
    searchText && pipelineSchedules
      ? pipelineSchedules.filter(
          schedule =>
            (schedule?.name || "").toLowerCase().includes(searchText.toLowerCase()) ||
            schedule?.properties?.pipelines?.some(pipeline =>
              (pipeline?.pipelineReference?.referenceName || "").toLowerCase().includes(searchText.toLowerCase())
            ) ||
            false
        )
      : pipelineSchedules || [];

  if (schedulesError) {
    return <Alert message="Error Loading Pipeline Schedules" description={schedulesError} type="error" showIcon />;
  }

  return (
    <div>
      <Space direction="vertical" size={8} style={{ marginBottom: 16 }}>
        <Title level={2}>Pipeline Schedules</Title>
        <Text type="secondary">View and manage scheduled pipeline triggers</Text>
      </Space>

      {/* Statistics Cards */}
      <ScheduleSummaryCards pipelineSchedules={pipelineSchedules || []} schedulesLoading={schedulesLoading} />

      <Card
        extra={<Search placeholder="Search schedules" allowClear onChange={e => setSearchText(e.target.value)} style={{ width: 250, marginLeft: "10px" }} />}
        style={{ borderRadius: "8px", boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)" }}
      >
        <ScheduleTable schedules={filteredSchedules} loading={schedulesLoading} onEdit={handleEdit} />
      </Card>

      <ScheduleEditModal
        visible={editModalVisible}
        currentSchedule={currentSchedule}
        updating={updating}
        onCancel={() => setEditModalVisible(false)}
        onUpdate={handleUpdateSchedule}
      />
    </div>
  );
};

export default ScheduledPipelinesContent;
