import moment from "moment-timezone";
import { BusinessHoursResult } from "../types";
import { PipelineSchedule } from "../../../../../../__redux/pipelineDashSlice";

export const formatTime = (hours?: number[], minutes?: number[]) => {
  if (!hours?.length || !minutes?.length) return "N/A";

  const sortedHours = [...hours].sort((a, b) => a - b);
  const sortedMinutes = [...minutes].sort((a, b) => a - b);

  if (hours.length > 6 || minutes.length > 4) {
    if (hours.length === 24 && sortedHours.every((h, idx) => h === idx)) {
      if (minutes.length === 60 && sortedMinutes.every((m, idx) => m === idx)) {
        return "Every minute of every hour";
      }

      if (minutes.length === 1) {
        return `At ${sortedMinutes[0].toString().padStart(2, "0")} minutes past every hour`;
      }

      if (minutes.length <= 6) {
        return `Every hour at minutes: ${sortedMinutes.map(m => m.toString().padStart(2, "0")).join(", ")}`;
      }

      const isEveryXMinutes = areEvenlySpaced(sortedMinutes);
      if (isEveryXMinutes) {
        return `Every ${isEveryXMinutes} minute(s) of every hour`;
      }

      return `${sortedMinutes.length} minute(s) every hour`;
    }

    const minuteInterval = getInterval(sortedMinutes);
    if (minuteInterval) {
      if (hours.length === 24) {
        return `Every ${minuteInterval} minute(s), 24 hours a day`;
      }

      if (areSequential(sortedHours)) {
        const start = sortedHours[0];
        const end = sortedHours[sortedHours.length - 1];
        return `Every ${minuteInterval} minute(s) from ${formatHour(start)} to ${formatHour(end)}`;
      }

      return `Every ${minuteInterval} minute(s) during ${sortedHours.length} specific hours`;
    }

    if (minutes.length > 15) {
      return `Multiple minutes across ${sortedHours.length} hour(s)`;
    }
  }

  if (hours.length <= 3 && minutes.length <= 3) {
    return sortedHours
      .map(hour => sortedMinutes.map(minute => `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`).join(", "))
      .join(", ");
  }

  return `${sortedMinutes.length} minute(s) during ${sortedHours.length} hour(s)`;
};

export const formatHour = (hour: number) => {
  if (hour === 0) return "12 AM";
  if (hour === 12) return "12 PM";
  return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
};

export const areEvenlySpaced = (numbers: number[]): number | false => {
  if (numbers.length <= 1) return false;

  const commonIntervals = [5, 10, 15, 20, 30];
  for (const interval of commonIntervals) {
    const fitsInterval = numbers.every(n => n % interval === 0);
    if (fitsInterval && numbers.length * interval <= 60) {
      return interval;
    }
  }

  const interval = numbers[1] - numbers[0];
  if (interval <= 0) return false;

  for (let i = 1; i < numbers.length; i++) {
    if (numbers[i] - numbers[i - 1] !== interval) {
      return false;
    }
  }

  return interval;
};

export const getInterval = (minutes: number[]): number | null => {
  const intervals = [5, 10, 15, 20, 30];

  for (const interval of intervals) {
    if (minutes.every(m => m % interval === 0)) {
      const expectedCount = 60 / interval;
      if (minutes.length === expectedCount) {
        return interval;
      }
    }
  }

  const diff = minutes[1] - minutes[0];
  if (diff > 0 && minutes.every((m, i) => i === 0 || m - minutes[i - 1] === diff)) {
    return diff;
  }

  return null;
};

export const areSequential = (numbers: number[]): boolean => {
  if (numbers.length <= 1) return true;

  for (let i = 1; i < numbers.length; i++) {
    if (numbers[i] !== numbers[i - 1] + 1) {
      return false;
    }
  }

  return true;
};

export const formatFrequency = (frequency?: string, interval?: number) => {
  if (!frequency) return "N/A";
  return `${frequency}${interval && interval > 1 ? ` (every ${interval} ${frequency.toLowerCase()}s)` : ""}`;
};

export const formatDateString = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return moment(dateString).isValid() ? moment.tz(dateString, "America/Chicago").format("MM/DD/YYYY HH:mm") : "N/A";
  } catch (error) {
    return "N/A";
  }
};

export const formatWeekdays = (weekDays?: string[]) => {
  if (!weekDays?.length) return "";

  const daysOrder = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
  const sortedDays = [...weekDays].sort((a, b) => daysOrder.indexOf(a) - daysOrder.indexOf(b));

  if (
    sortedDays.length === 5 &&
    sortedDays.includes("Monday") &&
    sortedDays.includes("Tuesday") &&
    sortedDays.includes("Wednesday") &&
    sortedDays.includes("Thursday") &&
    sortedDays.includes("Friday") &&
    !sortedDays.includes("Saturday") &&
    !sortedDays.includes("Sunday")
  ) {
    return "Monday to Friday (weekdays)";
  }

  if (
    sortedDays.length === 2 &&
    sortedDays.includes("Saturday") &&
    sortedDays.includes("Sunday") &&
    !sortedDays.includes("Monday") &&
    !sortedDays.includes("Tuesday") &&
    !sortedDays.includes("Wednesday") &&
    !sortedDays.includes("Thursday") &&
    !sortedDays.includes("Friday")
  ) {
    return "Weekends (Saturday, Sunday)";
  }

  if (areConsecutiveDays(sortedDays)) {
    return `${sortedDays[0]} to ${sortedDays[sortedDays.length - 1]}`;
  }

  return sortedDays.join(", ");
};

export const areConsecutiveDays = (days: string[]): boolean => {
  if (days.length <= 1) return true;

  const daysOrder = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  for (let i = 1; i < days.length; i++) {
    const prevIdx = daysOrder.indexOf(days[i - 1]);
    const currIdx = daysOrder.indexOf(days[i]);

    if (currIdx !== prevIdx + 1) {
      return false;
    }
  }

  return true;
};

export const isBusinessHoursPattern = (hours: number[]): BusinessHoursResult => {
  const sortedHours = [...hours].sort((a, b) => a - b);

  if (hours.length === 9 && sortedHours[0] === 9 && sortedHours[sortedHours.length - 1] === 17) {
    return { isBusinessHours: true, period: "9 AM to 5 PM (business hours)" };
  }

  if (hours.length === 11 && sortedHours[0] === 8 && sortedHours[sortedHours.length - 1] === 18) {
    return { isBusinessHours: true, period: "8 AM to 6 PM (extended business hours)" };
  }

  if (hours.length === 12 && sortedHours[0] === 12 && sortedHours[sortedHours.length - 1] === 23) {
    return { isBusinessHours: true, period: "12 PM to 11 PM" };
  }

  if (hours.length === 9 && sortedHours[0] === 8 && sortedHours[sortedHours.length - 1] === 16) {
    return { isBusinessHours: true, period: "8 AM to 4 PM" };
  }

  if (areSequential(sortedHours)) {
    const start = formatHour(sortedHours[0]);
    const end = formatHour(sortedHours[sortedHours.length - 1]);
    return { isBusinessHours: true, period: `${start} to ${end}` };
  }

  return { isBusinessHours: false, period: "" };
};

export const getScheduleDescription = (properties: PipelineSchedule["properties"] | undefined): string => {
  if (!properties?.recurrence?.schedule) return "No schedule information";

  const { hours, minutes, weekDays, monthDays, months } = properties.recurrence.schedule;

  if (!hours?.length || !minutes?.length) return "Invalid schedule";

  const sortedHours = [...hours].sort((a, b) => a - b);
  const sortedMinutes = [...minutes].sort((a, b) => a - b);

  const { isBusinessHours, period } = isBusinessHoursPattern(sortedHours);

  const weekdaysFormatted = formatWeekdays(weekDays);
  const monthsFormatted = formatMonths(months);
  const monthDaysFormatted = formatMonthDays(monthDays);

  let minutesFormatted = "";
  if (minutes.length === 1) {
    minutesFormatted = `at ${minutes[0]} minute(s) past the hour`;
  } else if (minutes.length > 1) {
    const interval = getInterval(sortedMinutes);
    if (interval) {
      minutesFormatted = `every ${interval} minute(s)`;
    } else {
      minutesFormatted = `at minutes ${sortedMinutes.join(", ")}`;
    }
  }

  let timeDescription = "";
  if (isBusinessHours) {
    timeDescription = `${period}, ${minutesFormatted}`;
  } else {
    timeDescription = formatTime(hours, minutes);
  }

  const parts = [];
  if (weekdaysFormatted) parts.push(weekdaysFormatted);
  if (monthDaysFormatted) parts.push(monthDaysFormatted);
  if (monthsFormatted) parts.push(monthsFormatted);
  parts.push(timeDescription);

  return parts.filter(Boolean).join(", ");
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "Started":
      return "success";
    case "Stopped":
      return "error";
    default:
      return "default";
  }
};

export const formatMonthDays = (monthDays?: number[]) => {
  if (!monthDays?.length) return "";

  const sortedDays = [...monthDays].sort((a, b) => a - b);

  if (sortedDays.length > 10) {
    return `${sortedDays.length} days of the month`;
  }

  // Check if days are consecutive
  if (areSequentialNumbers(sortedDays)) {
    return `Days ${sortedDays[0]} to ${sortedDays[sortedDays.length - 1]} of the month`;
  }

  return `Day${sortedDays.length > 1 ? "s" : ""} ${sortedDays.join(", ")} of the month`;
};

export const formatMonths = (months?: string[]) => {
  if (!months?.length) return "";

  const monthOrder = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

  const sortedMonths = [...months].sort((a, b) => monthOrder.indexOf(a) - monthOrder.indexOf(b));

  if (sortedMonths.length === 12) {
    return "Every month";
  }

  // Check for quarters
  if (sortedMonths.length === 3) {
    if (sortedMonths.every(m => ["January", "February", "March"].includes(m))) {
      return "Q1 (Jan-Mar)";
    }
    if (sortedMonths.every(m => ["April", "May", "June"].includes(m))) {
      return "Q2 (Apr-Jun)";
    }
    if (sortedMonths.every(m => ["July", "August", "September"].includes(m))) {
      return "Q3 (Jul-Sep)";
    }
    if (sortedMonths.every(m => ["October", "November", "December"].includes(m))) {
      return "Q4 (Oct-Dec)";
    }
  }

  // Check if months are consecutive
  if (areConsecutiveMonths(sortedMonths)) {
    return `${sortedMonths[0]} to ${sortedMonths[sortedMonths.length - 1]}`;
  }

  return sortedMonths.join(", ");
};

export const areConsecutiveMonths = (months: string[]): boolean => {
  if (months.length <= 1) return true;

  const monthOrder = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

  for (let i = 1; i < months.length; i++) {
    const prevIdx = monthOrder.indexOf(months[i - 1]);
    const currIdx = monthOrder.indexOf(months[i]);

    if (currIdx !== prevIdx + 1) {
      return false;
    }
  }

  return true;
};

export const areSequentialNumbers = (numbers: number[]): boolean => {
  if (numbers.length <= 1) return true;

  for (let i = 1; i < numbers.length; i++) {
    if (numbers[i] !== numbers[i - 1] + 1) {
      return false;
    }
  }

  return true;
};
