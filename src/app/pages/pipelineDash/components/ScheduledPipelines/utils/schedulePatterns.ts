// Common schedule patterns to help with selection
export const SCHEDULE_PATTERNS = {
  hours: {
    ALL_DAY: Array.from({ length: 24 }, (_, i) => i),
    BUSINESS_HOURS: [9, 10, 11, 12, 13, 14, 15, 16, 17],
    EXTENDED_BUSINESS: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
    MORNING: [6, 7, 8, 9, 10, 11],
    AFTERNOON: [12, 13, 14, 15, 16, 17],
    EVENING: [18, 19, 20, 21, 22, 23],
  },
  minutes: {
    EVERY_HOUR: [0],
    EVERY_30_MIN: [0, 30],
    EVERY_15_MIN: [0, 15, 30, 45],
    EVERY_10_MIN: [0, 10, 20, 30, 40, 50],
    EVERY_5_MIN: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55],
  },
  weekDays: {
    WEEKDAYS: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    WEEKEND: ["Saturday", "Sunday"],
    ALL_WEEK: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
  },
  monthDays: {
    START_OF_MONTH: [1],
    MID_MONTH: [15],
    END_OF_MONTH: [28, 29, 30, 31],
    QUARTERLY: [1, 8, 15, 22],
  },
  months: {
    ALL_MONTHS: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    FIRST_QUARTER: ["January", "February", "March"],
    SECOND_QUARTER: ["April", "May", "June"],
    THIRD_QUARTER: ["July", "August", "September"],
    FOURTH_QUARTER: ["October", "November", "December"],
  },
};

export const FREQUENCIES = ["Day", "Week", "Month", "Year"];

export const TIME_ZONES = ["Central Standard Time"];

export const WEEKDAYS = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

export const MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

// 1-31 for month days
export const MONTH_DAYS = Array.from({ length: 31 }, (_, i) => i + 1);
