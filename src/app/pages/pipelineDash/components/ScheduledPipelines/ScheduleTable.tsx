import { EditOutlined, FieldTimeOutlined, PlayCircleOutlined, PauseCircleOutlined } from "@ant-design/icons";
import { Button, Empty, Space, Spin, Table, Tag, Tooltip, Typography, message } from "antd";
import React, { useState } from "react";
import { PipelineSchedule, setPipelineSchedules } from "../../../../../__redux/pipelineDashSlice";
import { formatFrequency, getScheduleDescription, getStatusColor } from "./utils/formatters";
import { isAdmin } from "../../../../utils/roleUtils";
import { RootState } from "../../../../store";
import { useSelector, useDispatch } from "react-redux";
import { getPipelineSchedules, startTrigger, stopTrigger } from "../../../../api/pipelinDashApi";
import { AppDispatch } from "../../../../store";

const { Text } = Typography;

interface ScheduleTableProps {
  schedules: PipelineSchedule[];
  loading: boolean;
  onEdit: (schedule: PipelineSchedule) => void;
}

const ScheduleTable: React.FC<ScheduleTableProps> = ({ schedules, loading, onEdit }) => {
  const { userRole } = useSelector((state: RootState) => state.auth);
  const [loadingTriggers, setLoadingTriggers] = useState<Record<string, boolean>>({});
  const dispatch = useDispatch<AppDispatch>();

  const handleStartTrigger = async (triggerName: string) => {
    try {
      setLoadingTriggers(prev => ({ ...prev, [triggerName]: true }));
      await startTrigger(triggerName);
      const data = await getPipelineSchedules();
      dispatch(setPipelineSchedules(data?.schedules || []));
      message.success(`Started trigger: ${triggerName}`);
    } catch (error) {
      message.error(`Failed to start trigger: ${triggerName}, ${error}`);
    } finally {
      setLoadingTriggers(prev => ({ ...prev, [triggerName]: false }));
    }
  };

  const handleStopTrigger = async (triggerName: string) => {
    try {
      setLoadingTriggers(prev => ({ ...prev, [triggerName]: true }));
      await stopTrigger(triggerName);
      const data = await getPipelineSchedules();
      dispatch(setPipelineSchedules(data?.schedules || []));
      message.success(`Stopped trigger: ${triggerName}`);
    } catch (error) {
      message.error(`Failed to stop trigger: ${triggerName}, ${error}`);
    } finally {
      setLoadingTriggers(prev => ({ ...prev, [triggerName]: false }));
    }
  };

  const columns = [
    {
      title: "Schedule Name",
      dataIndex: "name",
      key: "name",
      width: 150,
      render: (text: string) => <Text strong>{text || "Unnamed Schedule"}</Text>,
    },
    {
      title: "Pipeline",
      dataIndex: "properties",
      key: "pipeline",
      width: 150,
      render: (properties: PipelineSchedule["properties"]) => (
        <Space direction="vertical">
          {properties?.pipelines?.length ? (
            properties.pipelines.map((pipeline, index) => <Text key={index}>{pipeline?.pipelineReference?.referenceName || "Unnamed Pipeline"}</Text>)
          ) : (
            <Text type="secondary">No pipeline reference</Text>
          )}
        </Space>
      ),
    },
    {
      title: "Trigger Type",
      dataIndex: "properties",
      key: "triggerType",
      width: 100,
      render: (properties: PipelineSchedule["properties"], record: PipelineSchedule) => (
        <Space>
          <Tag color="blue">{record?.type || properties?.type || "Schedule"}</Tag>
        </Space>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => <Tag color={getStatusColor(status)}>{status || "Unknown"}</Tag>,
    },
    {
      title: "Schedule",
      dataIndex: "properties",
      key: "schedule",
      width: 150,
      render: (properties: PipelineSchedule["properties"]) => {
        const { hours, minutes, weekDays, monthDays, months } = properties?.recurrence?.schedule || {};
        console.log(properties?.recurrence?.schedule);
        return (
          <Tooltip
            title={
              hours?.length && hours?.length > 0
                ? `Hours: ${hours?.join(", ")}, Minutes: ${minutes?.join(", ")}, Weekdays: ${weekDays?.join(", ") || "N/A"}${
                    monthDays?.length && monthDays?.length > 0 ? `, Month Days: ${monthDays?.join(", ")}` : ""
                  }${months?.length && months?.length > 0 ? `, Months: ${months?.join(", ")}` : ""}`
                : undefined
            }
          >
            <Text>{getScheduleDescription(properties)}</Text>
          </Tooltip>
        );
      },
    },
    {
      title: "Frequency",
      dataIndex: "properties",
      key: "frequency",
      width: 100,
      render: (properties: PipelineSchedule["properties"]) => (
        <Space>
          <FieldTimeOutlined />
          <Text>{formatFrequency(properties?.recurrence?.frequency, properties?.recurrence?.interval)}</Text>
        </Space>
      ),
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      width: 50,
      render: (description: string) => <Text ellipsis={{ tooltip: description }}>{description || "No description available"}</Text>,
    },
    isAdmin(userRole)
      ? {
          title: "Actions",
          key: "actions",
          width: 150,
          render: (_: unknown, record: PipelineSchedule) => {
            const triggerName = record.name;
            const isStarted = record.status === "Started";
            const isLoading = loadingTriggers[triggerName] || false;

            return (
              <Space>
                <Button type="primary" icon={<EditOutlined />} size="small" onClick={() => onEdit(record)}>
                  Edit
                </Button>
                {isStarted ? (
                  <Tooltip title="Stop Trigger">
                    <Button
                      danger
                      type="primary"
                      icon={<PauseCircleOutlined />}
                      size="small"
                      loading={isLoading}
                      onClick={() => handleStopTrigger(triggerName)}
                    />
                  </Tooltip>
                ) : (
                  <Tooltip title="Start Trigger">
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      size="small"
                      loading={isLoading}
                      onClick={() => handleStartTrigger(triggerName)}
                      style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
                    />
                  </Tooltip>
                )}
              </Space>
            );
          },
        }
      : {},
  ];

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px 0" }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Loading pipeline schedules...</div>
      </div>
    );
  }

  if (!schedules || schedules.length === 0) {
    return <Empty description="No pipeline schedules found" image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  return (
    <Table
      dataSource={schedules}
      columns={columns}
      rowKey={record => record?.name || Math.random().toString()}
      pagination={{ pageSize: 10 }}
      scroll={{ x: "max-content" }}
    />
  );
};

export default ScheduleTable;
