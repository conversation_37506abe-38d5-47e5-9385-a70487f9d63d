export interface ScheduleFormValues {
  triggerName: string;
  frequency: string;
  interval: number;
  timeZone: string;
  weekDays: string[];
  hours: number[];
  minutes: number[];
  monthDays?: number[];
  months?: string[];
  monthlyOccurrences?: Array<{
    day: string;
    occurrence: number;
  }>;
  description?: string;
}

export interface BusinessHoursResult {
  isBusinessHours: boolean;
  period: string;
}
