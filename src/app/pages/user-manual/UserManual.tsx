import { ExpandMore, FilterListOutlined, SearchOutlined, SortOutlined, TableChartOutlined } from "@mui/icons-material";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Container,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  alpha,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { FC } from "react";

// Typography constants
const TYPOGRAPHY = {
  MAIN_TITLE: {
    xs: "2.5rem",
    sm: "3rem",
    md: "3.5rem",
  },
  SECTION_TITLE: "2rem",
  BODY_TEXT: "1.2rem",
  SUBHEADING: "1.5rem",
} as const;

// Spacing constants
const SPACING = {
  SECTION_PADDING: 4,
  SECTION_MARGIN: 4,
  PAGE_PADDING: 8,
  TITLE_MARGIN: 6,
} as const;

// Page content configuration
interface FeatureItem {
  icon?: React.ReactNode;
  primary: string;
  secondary?: string;
}

interface SubSection {
  title: string;
  content: string;
  features?: FeatureItem[];
}

interface PageSection {
  title: string;
  content: string;
  subSections?: SubSection[];
  features?: FeatureItem[];
}

// Add common table controls section
const COMMON_TABLE_CONTROLS: FeatureItem[] = [
  {
    icon: <SearchOutlined sx={{ fontSize: "1.5rem" }} />,
    primary: "Search Functionality",
    secondary: "Use the search bar to filter records across any column",
  },
  {
    icon: <TableChartOutlined sx={{ fontSize: "1.5rem" }} />,
    primary: "View Table",
    secondary: "To View the underlying tables that are being queried",
  },
  {
    icon: <SortOutlined sx={{ fontSize: "1.5rem" }} />,
    primary: "Sort",
    secondary: "To organize data by any column",
  },
  {
    icon: <FilterListOutlined sx={{ fontSize: "1.5rem" }} />,
    primary: "Show Filters",
    secondary: "To apply specific data filters",
  },
  {
    primary: "Pagination",
    secondary: "Navigate through pages using the controls at the bottom. Current page is highlighted in blue",
  },
  {
    primary: "Records Per Page",
    secondary: "Adjust the number of visible records (15, 30, 45, or 60) using the limit slider",
  },
];

const MANUAL_PAGES: PageSection[] = [
  {
    title: "Introduction",
    content:
      "This application offers an intuitive interface for visualizing business data stored in an SQL database for LOU Malnati's stores. Users with specific roles are granted the ability to perform CRU (Create, Read, Update) operations on the database tables directly from the user interface.",
  },
  {
    title: "Login Screen",
    content:
      "Only users with Azure accounts in Lou Malnati's Active Directory will be eligible to log in to the application. Three distinct user roles can be assigned:",
    features: [
      {
        primary: "Admin and Developer roles",
        secondary: "Full access to all pages and records",
      },
      {
        primary: "Reader role",
        secondary: "Read-only access to all pages without editing privileges",
      },
    ],
  },
  {
    title: "Common Table Controls",
    content: "All data tables in the application share these common controls for easy data management:",
    features: COMMON_TABLE_CONTROLS,
  },
  {
    title: "Store Logistic Manager",
    content:
      "The Store Logistic Manager Page in Azure UI is a powerful tool designed to help users manage store details efficiently. This user manual provides a comprehensive guide on how to navigate the page, edit existing store information, and add new stores. Users will encounter a user-friendly interface that displays essential store details, enabling effective management and oversight.",
    subSections: [
      {
        title: "Landing on the Page",
        content:
          "Upon accessing the Store Logistic Manager Page, users will be greeted with a table displaying all existing stores along with their respective active details. The following attributes are prominently displayed:",
        features: [
          {
            primary: "Key Attributes",
            secondary: "Store Code, Phone, Address, Concept Name, Region, District, Market",
          },
        ],
      },
      {
        title: "Editing Store Details",
        content: "To edit or update details for an existing store:",
        features: [
          {
            primary: "Action Button",
            secondary: "Find the action button represented by an eye symbol on the left side of the respective store record.",
          },
          {
            primary: "Pop-Up Window",
            secondary:
              "Click on this action button to open a pop-up window that consolidates information from ten different tables/pages related to that store.",
          },
        ],
      },
      {
        title: "Using the Pop-Up Interface",
        content: "The pop-up interface is designed for ease of use:",
        features: [
          {
            primary: "Navigation",
            secondary: "Users can navigate through various tables using the Save and Next buttons.",
          },
          {
            primary: "Editing Fields",
            secondary: "Make necessary edits in any of the fields available across these tables.",
          },
          {
            primary: "Saving Changes",
            secondary: "Ensure you click Save after making any changes before moving to another table.",
          },
        ],
      },
      {
        title: "Adding New Stores",
        content: "To add a new store:",
        features: [
          {
            primary: "Process",
            secondary: "Look for an 'Add New Store' button typically located at the top right corner of the page.",
          },
          {
            primary: "Required Fields",
            secondary: "Fill in all required fields such as: Store Code, Phone, Address, Concept Name, Region, District, Market",
          },
        ],
      },
      {
        title: "Data Refresh and Reporting",
        content:
          "All changes made will reflect in reports during the next run, as these changes are captured in a daily pipeline run and report refresh. The updates occur within the Lous_EMS database, ensuring that all data remains current and accurate.",
      },
    ],
  },
  {
    title: "Discount & GL Code Management",
    content:
      "The Discount and GL Code page in Azure UI is designed to facilitate the management of General Ledger (GL) codes associated with discounts in the Revel and Toast POS systems. This user manual provides a step-by-step guide on how to navigate the page, edit existing GL codes, and add new discounts.",
    subSections: [
      {
        title: "Home Page Overview",
        content:
          "Upon entering the Discount and GL Code page, users will see a comprehensive list of existing discounts along with their respective GL codes. The key functionalities available on this page include:",
        features: [
          {
            primary: "Search Bar",
            secondary: "Quickly locate specific discounts by entering relevant keywords.",
          },
          {
            primary: "Action Button",
            secondary: "Edit existing records or add new entries.",
          },
        ],
      },
      {
        title: "Editing Existing Discounts",
        content: "To edit the GL code for an existing discount:",
        features: [
          {
            primary: "Search for the Discount",
            secondary: "Use the search bar to find the discount you wish to update.",
          },
          {
            primary: "Click on the Action Button",
            secondary: "Locate the action button on the left side of the record (represented by an icon).",
          },
          {
            primary: "Update GL Code",
            secondary: "In the pop-up window that appears, update the GL code as needed.",
          },
          {
            primary: "Save Changes",
            secondary: "Click on the Save button to commit your changes.",
          },
        ],
      },
      {
        title: "Adding New Discounts",
        content: "To add a new discount to the dataset:",
        features: [
          {
            primary: "Click on 'Add New Record'",
            secondary: "This button is typically located at the top right corner of the page.",
          },
          {
            primary: "Enter Required Details",
            secondary: "Fill in all necessary fields, including: Discount Name, GL Account Number, Source",
          },
          {
            primary: "Avoid Duplicate Entries",
            secondary: "Ensure that the discount name is unique across both the Toast and Revel POS systems, as duplicate entries are not allowed.",
          },
          {
            primary: "Save New Record",
            secondary: "After entering all required details, click on the Save button to create the new discount entry.",
          },
        ],
      },
      {
        title: "Important Notes",
        content:
          "All changes made on this page will be reflected in future reports as they are processed through Azure's data management systems. Regularly check for updates or changes in policies regarding discount management to ensure compliance with financial regulations.",
      },
    ],
  },
  {
    title: "Fiscal Calendar",
    content:
      "The Fiscal Calendar page in Azure UI is designed to facilitate the management of the Lou Fiscal Calendar. This page provides users with essential information regarding the current fiscal year and allows for effective date filtering and record editing.",
    subSections: [
      {
        title: "Accessing the Fiscal Calendar Page",
        content:
          "Once logged into the Azure UI, users will be directed to the Fiscal Calendar page, which displays the following details for the current fiscal year:",
        features: [
          {
            primary: "Calendar Date",
            secondary: "The current date according to the fiscal calendar.",
          },
          {
            primary: "Fiscal Year",
            secondary: "The year that defines the fiscal period.",
          },
          {
            primary: "Fiscal Week",
            secondary: "The current week within the fiscal year.",
          },
          {
            primary: "Fiscal Quarter",
            secondary: "The quarter of the fiscal year.",
          },
          {
            primary: "Fiscal Period Start and End Dates",
            secondary: "The start and end dates of the current fiscal period.",
          },
          {
            primary: "Holidays",
            secondary: "Any holidays observed within the current fiscal year.",
          },
        ],
      },
      {
        title: "Filtering Options",
        content: "Users can filter displayed dates by following these steps:",
        features: [
          {
            primary: "Show Filter",
            secondary: "Click on Show Filter to access filtering options.",
          },
          {
            primary: "Available Filters",
            secondary: "Select from the available filters: Fiscal Year, Fiscal Quarter, Fiscal Period, Fiscal Week",
          },
        ],
      },
      {
        title: "Editing Existing Records",
        content: "To modify existing records in the Fiscal Calendar:",
        features: [
          {
            primary: "Action Button",
            secondary: "Click on the Action Button located on the left side of each record.",
          },
          {
            primary: "Edit Window",
            secondary: "A pop-up window will appear, allowing you to modify the selected record.",
          },
          {
            primary: "Save Changes",
            secondary: "After making changes, click on Save to commit the updates.",
          },
        ],
      },
      {
        title: "Data Availability",
        content:
          "The Fiscal Calendar page contains data up to the year 2030, ensuring that users have access to future fiscal periods for planning and analysis.",
      },
    ],
  },
  {
    title: "Report Page",
    content: "The Report Page provides comprehensive insights into sales performance and promotional data across different time frames. It offers detailed comparisons with previous years' data and various performance metrics.",
    subSections: [
      {
        title: "Page Structure and Navigation",
        content: "The Report Page is organized into distinct sections for easy navigation and analysis:",
        features: [
          {
            primary: "Promotions Section",
            secondary: "Located at the top, displaying current promotions and historical promotional data from the previous year",
          },
          {
            primary: "Key Visuals",
            secondary: "Two main visuals: Fiscal Day Visual for daily data, and a combined visual for Fiscal Week, Period, and Year metrics",
          },
          {
            primary: "Trend Tables",
            secondary: "Comparative tables showing ticket and sales trends across different time periods",
          }
        ]
      },
      {
        title: "Understanding Trend Metrics",
        content: "The report uses several key metrics to track performance:",
        features: [
          {
            primary: "TYWTD (This Year Week-to-Date)",
            secondary: "Compares current fiscal week's performance with the same week last year ((This Year / Previous Year) - 1)",
          },
          {
            primary: "LYWTD (Last Year Week-to-Date)",
            secondary: "Compares previous year's fiscal week with the year before that",
          },
          {
            primary: "Period and Year Metrics",
            secondary: "Similar comparisons for Period-to-Date (PTD) and Year-to-Date (YTD) using TYPTD, LYPTD, TYYTD, and LYYTD",
          }
        ]
      },
      {
        title: "Data Segmentation",
        content: "Report data can be analyzed across multiple dimensions:",
        features: [
          {
            primary: "Segmentation Categories",
            secondary: "Cohort, Segment, Geography, District Manager, and Regional Manager",
          },
          {
            primary: "Comp Store Definition",
            secondary: "Metrics are calculated for stores open more than 18 months from the current date",
          }
        ]
      },
      {
        title: "Visual Indicators",
        content: "The report uses visual cues to highlight performance:",
        features: [
          {
            primary: "Color Coding",
            secondary: "Negative trends are highlighted in red, while positive trends are displayed in black",
          },
          {
            primary: "Trend Visualization",
            secondary: "Graphical representations of daily, weekly, period, and yearly performance metrics",
          }
        ]
      }
    ]
  }
];

const UserManual: FC = () => {
  const theme = useTheme();

  const Section: FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
    <Accordion
      sx={{
        my: SPACING.SECTION_MARGIN,
        borderRadius: 2,
        backgroundColor: alpha(theme.palette.background.paper, 0.6),
        backdropFilter: "blur(10px)",
        transition: "transform 0.2s ease-in-out",
        "&:hover": {
          transform: "translateY(-2px)",
        },
        "&::before": {
          display: "none",
        },
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMore />}
        sx={{
          p: SPACING.SECTION_PADDING,
        }}
      >
        <Typography variant="h4" component="h2" fontWeight="500" sx={{ fontSize: TYPOGRAPHY.SECTION_TITLE }}>
          {title}
        </Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ p: SPACING.SECTION_PADDING, pt: 0 }}>
        <Divider sx={{ mb: 3 }} />
        {children}
      </AccordionDetails>
    </Accordion>
  );

  const FeatureList: FC<{ items: FeatureItem[] }> = ({ items }) => (
    <List>
      {items.map((item, index) => (
        <ListItem key={index} sx={{ pl: 0 }}>
          {item.icon && <ListItemIcon>{item.icon}</ListItemIcon>}
          <ListItemText
            primary={
              <Typography variant="body1" fontWeight="500" sx={{ fontSize: TYPOGRAPHY.BODY_TEXT }}>
                {item.primary}
              </Typography>
            }
            secondary={
              <Typography variant="body1" sx={{ fontSize: TYPOGRAPHY.BODY_TEXT }}>
                {item.secondary}
              </Typography>
            }
          />
        </ListItem>
      ))}
    </List>
  );

  const renderSubSection = (subSection: SubSection) => (
    <Box key={subSection.title} sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontSize: TYPOGRAPHY.SUBHEADING }}>
        {subSection.title}
      </Typography>
      {subSection.content && (
        <Typography variant="body1" sx={{ fontSize: TYPOGRAPHY.BODY_TEXT }}>
          {subSection.content}
        </Typography>
      )}
      {subSection.features && <FeatureList items={subSection.features} />}
    </Box>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: SPACING.PAGE_PADDING }}>
        <Typography
          variant="h1"
          component="h1"
          gutterBottom
          align="center"
          fontWeight="bold"
          sx={{
            fontSize: TYPOGRAPHY.MAIN_TITLE,
            mb: SPACING.TITLE_MARGIN,
          }}
        >
          LOU Web App User Manual
        </Typography>

        {MANUAL_PAGES.map((page) => (
          <Section key={page.title} title={page.title}>
            <Typography variant="body1" sx={{ fontSize: TYPOGRAPHY.BODY_TEXT, lineHeight: 1.8 }}>
              {page.content}
            </Typography>
            {page.features && <FeatureList items={page.features} />}
            {page.subSections?.map(renderSubSection)}
          </Section>
        ))}
      </Box>
    </Container>
  );
};

export default UserManual;
