// import AddIcon from "@mui/icons-material/Add";
// import FilterListIcon from "@mui/icons-material/FilterList";
// import { Box, Button, Dialog, DialogContent, DialogTitle, Grid, TextField } from "@mui/material";
// import { createTheme, ThemeProvider } from "@mui/material/styles";
// import { ChangeEvent, FC, useEffect, useState } from "react";
// import { addStore, fetchConfig, fetchStores, searchStores, updateStore } from "../../api/api";
// import ConfirmationDialog from "../../components/confirmationDialog/ConfirmationDialog";
// import FilterSection from "../../CustomComponents/FilterSection";
// import Footer from "../../CustomComponents/Footer";
// import SearchBar from "../../CustomComponents/SearchBar";
// import StoreTable from "../../CustomComponents/StoreTable";
// import { StyledButton, StyledPaper } from "../../CustomComponents/StyledComponents";
// import { Config, filterList, Store } from "../../Types/types";

// const theme = createTheme({
//   palette: {
//     primary: {
//       main: "#007AFF",
//     },
//     background: {
//       default: "#f5f5f5",
//     },
//   },
//   typography: {
//     fontFamily: "Roboto, sans-serif",
//     h4: {
//       fontWeight: 700,
//       fontSize: "2rem",
//     },
//     h6: {
//       fontWeight: 500,
//       fontSize: "1.25rem",
//     },
//     body1: {
//       fontSize: "1rem",
//     },
//   },
// });

// const LouStoreListPage: FC = () => {
//   const [stores, setStores] = useState<Store[]>([]);
//   const [loading, setLoading] = useState<boolean>(false);
//   const [page, setPage] = useState<number>(1);
//   const [totalPages, setTotalPages] = useState<number>(1);
//   const [limit, setLimit] = useState<number>(15);
//   const [open, setOpen] = useState<boolean>(false);
//   const [formData, setFormData] = useState<Store>({
//     StoreNum: "",
//     StoreName: "",
//     Abbr: "",
//     OpenDate: "",
//     AssetType: "",
//     PricingTiers: "",
//     CompDate: "",
//     CompStatus: "",
//     RM: "",
//     DM: "",
//     GM: "",
//     Region: "",
//     District: "",
//     StoreType: "",
//     SSCohort: "",
//     CompCohort: "",
//     Market: "",
//     Geography: "",
//   });
//   const [isAddMode, setIsAddMode] = useState<boolean>(false);
//   const [refresh, setRefresh] = useState<boolean>(false);
//   const [searchText, setSearchText] = useState<string>("");
//   const [config, setConfig] = useState<Config>({
//     AssetType: [],
//     Market: [],
//     Geography: [],
//     RM: [],
//     DM: [],
//   });
//   const [selectedAssetTypes, setSelectedAssetTypes] = useState<string[]>([]);
//   const [selectedMarkets, setSelectedMarkets] = useState<string[]>([]);
//   const [selectedGeographies, setSelectedGeographies] = useState<string[]>([]);
//   const [selectedRMs, setSelectedRMs] = useState<string[]>([]);
//   const [selectedDMs, setSelectedDMs] = useState<string[]>([]);
//   const [filters, setFilters] = useState<filterList | null>(null);
//   const [showFilters, setShowFilters] = useState(false);
//   const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
//   const [confirmDialogLoading, setConfirmDialogLoading] = useState(false);
//   const [confirmDialogError, setConfirmDialogError] = useState<string | null>(null);
//   const [confirmDialogSuccess, setConfirmDialogSuccess] = useState(false);

//   useEffect(() => {
//     if (filters) {
//       fetchStoresData(page, 200, filters).then(() => {
//         setSearchText("");
//       });
//     } else {
//       fetchStoresData(page, limit).then(() => {
//         setSearchText("");
//       });
//     }
//     fetchConfigData();
//   }, [page, limit, refresh, filters]);

//   const fetchStoresData = async (page: number, limit: number, filters?: filterList) => {
//     setLoading(true);
//     try {
//       const data = await fetchStores(page, limit, filters);
//       setStores(data.result);
//       setTotalPages(data.totalPages);
//     } catch (error) {
//       console.error("Error fetching stores:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const fetchConfigData = async () => {
//     try {
//       const config = await fetchConfig();
//       setConfig(config);
//     } catch (error) {
//       console.error("Error fetching config:", error);
//     }
//   };

//   const handleSearch = async () => {
//     if (searchText.trim() === "") {
//       alert("Search text is required.");
//       return;
//     }

//     setLoading(true);
//     try {
//       const results = await searchStores(searchText);
//       setStores(results);
//       setTotalPages(1);
//     } catch (error) {
//       console.error("Error searching stores:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleLimitChange = (_event: Event, newValue: number | number[]) => {
//     const value = Array.isArray(newValue) ? newValue[0] : newValue;
//     setLimit(value === 100 ? 1000 : value);
//     setPage(1);
//   };

//   const handlePageChange = (event: ChangeEvent<unknown>, value: number) => {
//     setPage(value);
//   };

//   const handleClickOpen = (store: Store) => {
//     setIsAddMode(false);
//     setFormData(store);
//     setOpen(true);
//   };

//   const handleAddOpen = () => {
//     setIsAddMode(true);
//     setFormData({
//       StoreNum: "",
//       StoreName: "",
//       Abbr: "",
//       OpenDate: "",
//       AssetType: "",
//       PricingTiers: "",
//       CompDate: "",
//       CompStatus: "",
//       RM: "",
//       DM: "",
//       GM: "",
//       Region: "",
//       District: "",
//       StoreType: "",
//       SSCohort: "",
//       CompCohort: "",
//       Market: "",
//       Geography: "",
//       SubChicagolandSuburbanMarkets: "",
//       PeerGroup: "",
//       F23Gross$: "",
//     });
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//     setFormData({
//       StoreNum: "",
//       StoreName: "",
//       Abbr: "",
//       OpenDate: "",
//       AssetType: "",
//       PricingTiers: "",
//       CompDate: "",
//       CompStatus: "",
//       RM: "",
//       DM: "",
//       GM: "",
//       Region: "",
//       District: "",
//       StoreType: "",
//       SSCohort: "",
//       CompCohort: "",
//       Market: "",
//       Geography: "",
//       SubChicagolandSuburbanMarkets: "",
//       PeerGroup: "",
//       F23Gross$: "",
//     });
//   };

//   const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
//     const { name, value } = e.target;
//     setFormData({ ...formData, [name]: value });
//   };

//   const handleSubmit = () => {
//     setConfirmDialogOpen(true);
//   };

//   const handleConfirmSubmit = async () => {
//     setConfirmDialogLoading(true);
//     setConfirmDialogError(null);
//     setConfirmDialogSuccess(false);

//     try {
//       if (isAddMode) {
//         await addStore(formData);
//       } else {
//         await updateStore(formData);
//       }
//       setConfirmDialogSuccess(true);
//       handleClose();
//       setRefresh(!refresh);
//     } catch (error) {
//       console.error("Error updating store:", error);
//       setConfirmDialogError("An error occurred while processing your request. Please try again.");
//     } finally {
//       setConfirmDialogLoading(false);
//       setConfirmDialogOpen(false);
//       setConfirmDialogSuccess(false);
//     }
//   };

//   const handleCancelConfirm = () => {
//     setConfirmDialogOpen(false);
//     setConfirmDialogError(null);
//     setConfirmDialogSuccess(false);
//   };

//   const isSubmitDisabled = () => {
//     return !formData.StoreNum || !formData.StoreName || !formData.Abbr || !formData.OpenDate;
//   };

//   const handleApplyFilter = () => {
//     const filters = {
//       AssetType: selectedAssetTypes,
//       Market: selectedMarkets,
//       Geography: selectedGeographies,
//       RM: selectedRMs,
//       DM: selectedDMs,
//     };
//     // Remove empty filters
//     Object.keys(filters).forEach((key) => {
//       if (filters[key as keyof typeof filters].length === 0) {
//         delete filters[key as keyof typeof filters];
//       }
//     });
//     setFilters(filters);
//     setPage(1);
//     setShowFilters(false);
//   };

//   const handleResetFilter = () => {
//     setSelectedAssetTypes([]);
//     setSelectedMarkets([]);
//     setSelectedGeographies([]);
//     setSelectedRMs([]);
//     setSelectedDMs([]);
//     setSearchText("");
//     fetchStoresData(1, limit);
//   };

//   const handleToggleFilters = () => {
//     setShowFilters((prev) => !prev);
//   };

//   const handleClearSearch = () => {
//     setSearchText("");
//     fetchStoresData(1, limit);
//   };

//   return (
//     <ThemeProvider theme={theme}>
//       <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
//         <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
//           <Grid container spacing={2} alignItems="center">
//             <Grid
//               item
//               xs={12}
//               md={6}
//               sx={{
//                 display: "flex",
//                 justifyContent: "flex-start",
//                 alignItems: "center",
//                 gap: 2,
//               }}
//             >
//               <SearchBar searchText={searchText} setSearchText={setSearchText} handleSearch={handleSearch} handleClearSearch={handleClearSearch} />
//             </Grid>
//             <Grid item xs={12} md={6}>
//               <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
//                 <StyledButton variant="outlined" color="primary" onClick={handleToggleFilters} startIcon={<FilterListIcon />}>
//                   {showFilters ? "Hide Filters" : "Show Filters"}
//                 </StyledButton>
//                 <StyledButton variant="contained" color="primary" onClick={handleAddOpen} startIcon={<AddIcon />}>
//                   Add a new store
//                 </StyledButton>
//               </Box>
//             </Grid>
//           </Grid>

//           <FilterSection
//             showFilters={showFilters}
//             config={config}
//             selectedAssetTypes={selectedAssetTypes}
//             selectedMarkets={selectedMarkets}
//             selectedGeographies={selectedGeographies}
//             selectedRMs={selectedRMs}
//             selectedDMs={selectedDMs}
//             setSelectedAssetTypes={setSelectedAssetTypes}
//             setSelectedMarkets={setSelectedMarkets}
//             setSelectedGeographies={setSelectedGeographies}
//             setSelectedRMs={setSelectedRMs}
//             setSelectedDMs={setSelectedDMs}
//             handleResetFilter={handleResetFilter}
//             handleApplyFilter={handleApplyFilter}
//           />
//         </StyledPaper>
//         <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
//           <StoreTable stores={stores} loading={loading} limit={limit} handleClickOpen={handleClickOpen} />
//         </Box>

//         <Footer
//           limit={limit}
//           totalPages={totalPages}
//           page={page}
//           stores={stores}
//           handleLimitChange={handleLimitChange}
//           handlePageChange={handlePageChange}
//         />
//       </Box>
//       <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
//         <DialogTitle>{isAddMode ? "Add Store" : "Store Details"}</DialogTitle>
//         <DialogContent dividers>
//           <Grid container spacing={2}>
//             {Object.keys(formData).map((key) => (
//               <Grid item xs={12} sm={4} key={key}>
//                 <TextField
//                   margin="dense"
//                   label={key}
//                   name={key}
//                   value={formData[key as keyof Store] || ""}
//                   onChange={handleInputChange}
//                   fullWidth
//                   required={["StoreNum", "StoreName", "Abbr", "OpenDate"].includes(key)}
//                   disabled={!isAddMode && ["StoreNum", "StoreName", "Abbr", "OpenDate"].includes(key)}
//                 />
//               </Grid>
//             ))}
//           </Grid>
//           <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
//             <Button onClick={handleClose}>Cancel</Button>
//             <Button variant="contained" color="primary" onClick={handleSubmit} disabled={isSubmitDisabled()}>
//               {isAddMode ? "Add" : "Update"}
//             </Button>
//           </Box>
//         </DialogContent>
//       </Dialog>
//       <ConfirmationDialog
//         open={confirmDialogOpen}
//         title={isAddMode ? "Confirm Add Store" : "Confirm Update Store"}
//         content={`Are you sure you want to ${isAddMode ? "add" : "update"} this store?`}
//         onConfirm={handleConfirmSubmit}
//         onCancel={handleCancelConfirm}
//         isLoading={confirmDialogLoading}
//         error={confirmDialogError}
//         success={confirmDialogSuccess}
//       />
//     </ThemeProvider>
//   );
// };

// export default LouStoreListPage;
