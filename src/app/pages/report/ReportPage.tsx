import React, { useEffect, useState } from "react";
import { Box, Container, Typography, Paper, Grid, Card } from "@mui/material";
import { styled } from "@mui/system";
import { getCohortReport, getGeographyReport, getRegionReport, getDistrictReport, getSegmentReport, getTYPtdYtdReport, TyReportEndPoint, LyReportEndPoint, getLYWtdReport, getLYPtdReport, getLYYtdReport, LyPtdReportEndPoint, LyYtdReportEndPoint } from "../../api/report";
import { ReportItem, ReportState, TyPtdYtdReport, TyPtdYtdItem, LyWtdItem, LyWtdReport, LyYtdPtdItem, LyYtdPtdReport} from "../../../__redux/reportSlice";
import ReportPercentageTable from "./components/ReportPercentageTable";
import ReportValueTable from "./components/ReportValueTable";
import LoadingOverlay from "../../components/loadingOverlay/LoadingOverlay";
import { ReportDataType } from "../../constants/constants";


interface ReportResponse {
  result: ReportItem[];
}

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundColor: "rgba(255, 255, 255, 0.05)",
  backdropFilter: "blur(10px)",
  borderRadius: theme.shape.borderRadius * 2,
  // padding: theme.spacing(3),
  marginBottom: theme.spacing(4),
  boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
}));

const ReportPage: React.FC = () => {
  const [cohortReport, setCohortReport] = useState<ReportState<ReportItem>>({ data: [], loading: true, error: null });
  const [geographyReport, setGeographyReport] = useState<ReportState<ReportItem>>({ data: [], loading: true, error: null });
  const [regionReport, setRegionReport] = useState<ReportState<ReportItem>>({ data: [], loading: true, error: null });
  const [districtReport, setDistrictReport] = useState<ReportState<ReportItem>>({ data: [], loading: true, error: null });
  const [segmentReport, setSegmentReport] = useState<ReportState<ReportItem>>({ data: [], loading: true, error: null });
  const [combinedTYYtdData, setCombinedTYYtdData] = useState<TyPtdYtdReport>();
  const [combinedTYPtdData, setCombinedTYPtdData] = useState<TyPtdYtdReport>();
  // TY Report Data
  const [cohortTYYtdData, setCohortTYYtdData ] = useState<TyPtdYtdItem[]>([]);
  const [cohortTYPtdData, setCohortTYPtdData ] = useState<TyPtdYtdItem[]>([]);
  const [segmentTYYtdData, setSegmentTYYtdData ] = useState<TyPtdYtdItem[]>([]);
  const [segmentTYPtdData, setSegmentTYPtdData ] = useState<TyPtdYtdItem[]>([]);
  const [geographyTYYtdData, setGeographyTYYtdData ] = useState<TyPtdYtdItem[]>([]);
  const [geographyTYPtdData, setGeographyTYPtdData ] = useState<TyPtdYtdItem[]>([]);
  const [regionTYYtdData, setRegionTYYtdData ] = useState<TyPtdYtdItem[]>([]);
  const [regionTYPtdData, setRegionTYPtdData ] = useState<TyPtdYtdItem[]>([]);
  const [regionDistrictTYYtdData, setRegionDistrictTYYtdData ] = useState<TyPtdYtdItem[]>([]);
  const [regionDistrictTYPtdData, setRegionDistrictTYPtdData ] = useState<TyPtdYtdItem[]>([]);

  // LY Report Data
  const [cohortLYWtdData, setCohortLYWtdData ] = useState<LyWtdItem[]>([]);
  const [segmentLYWtdData, setSegmentLYWtdData ] = useState<LyWtdItem[]>([]);
  const [geographyLYWtdData, setGeographyLYWtdData ] = useState<LyWtdItem[]>([]);
  const [regionLYWtdData, setRegionLYWtdData ] = useState<LyWtdItem[]>([]);
  const [regionDistrictLYWtdData, setRegionDistrictLYWtdData ] = useState<LyWtdItem[]>([]);
  // LY PTD Report Data
  const [cohortLYPtdData, setCohortLYPtdData ] = useState<LyYtdPtdItem[]>([]);
  const [segmentLYPtdData, setSegmentLYPtdData ] = useState<LyYtdPtdItem[]>([]);
  const [geographyLYPtdData, setGeographyLYPtdData ] = useState<LyYtdPtdItem[]>([]);
  const [regionLYPtdData, setRegionLYPtdData ] = useState<LyYtdPtdItem[]>([]);
  const [regionDistrictLYPtdData, setRegionDistrictLYPtdData ] = useState<LyYtdPtdItem[]>([]);
  // LY YTD Report Data
  const [cohortLYYtdData, setCohortLYYtdData ] = useState<LyYtdPtdItem[]>([]);
  const [segmentLYYtdData, setSegmentLYYtdData ] = useState<LyYtdPtdItem[]>([]);
  const [geographyLYYtdData, setGeographyLYYtdData ] = useState<LyYtdPtdItem[]>([]);
  const [regionLYYtdData, setRegionLYYtdData ] = useState<LyYtdPtdItem[]>([]);
  const [regionDistrictLYYtdData, setRegionDistrictLYYtdData ] = useState<LyYtdPtdItem[]>([]);



  const fetchReport = async (
    getReport: () => Promise<ReportResponse>,
    setReport: React.Dispatch<React.SetStateAction<ReportState<ReportItem>>>
  ) => {
    try {
      const result = await getReport();
      const data = result.result;
      setReport({ data: Array.isArray(data) ? data : [], loading: false, error: null });
    } catch (err) {
      console.error("Error fetching report:", err);
      setReport({ data: [], loading: false, error: "Failed to fetch report. Please try again later." });
    }
  };

  const fetchReportData = async () => {
    const cohortResponse = await Promise.all([
      fetchReport(getCohortReport, setCohortReport),
      fetchTYPtdYtdReport("cohortSales", "YTD", TyReportEndPoint.cohort, getTYPtdYtdReport, setCohortTYYtdData),
      fetchTYPtdYtdReport("cohortSales", "PTD", TyReportEndPoint.cohort,  getTYPtdYtdReport, setCohortTYPtdData),
      fetchLYWtdReport("cohortSales", "WTD", LyReportEndPoint.cohort,  getLYWtdReport, setCohortLYWtdData),
      fetchLYPtdReport("cohortSales", "PTD", LyPtdReportEndPoint.cohort,  getLYPtdReport, setCohortLYPtdData),
      fetchLYYtdReport("cohortSales", "YTD", LyYtdReportEndPoint.cohort,  getLYYtdReport, setCohortLYYtdData),
    ])
    
    const segmentResponse = await Promise.all([
      fetchReport(getSegmentReport, setSegmentReport), 
      fetchTYPtdYtdReport("segmentSales", "YTD", TyReportEndPoint.segment,  getTYPtdYtdReport, setSegmentTYYtdData),
      fetchTYPtdYtdReport("segmentSales", "PTD", TyReportEndPoint.segment,  getTYPtdYtdReport, setSegmentTYPtdData),
      fetchLYWtdReport("segmentSales", "WTD", LyReportEndPoint.segment,  getLYWtdReport, setSegmentLYWtdData),
      fetchLYPtdReport("segmentSales", "PTD", LyPtdReportEndPoint.segment,  getLYPtdReport, setSegmentLYPtdData),
      fetchLYYtdReport("segmentSales", "YTD", LyYtdReportEndPoint.segment,  getLYYtdReport, setSegmentLYYtdData),
    ])
    
    const geographyResponse = await Promise.all([
      fetchReport(getGeographyReport, setGeographyReport), 
      fetchTYPtdYtdReport("geographySales", "YTD", TyReportEndPoint.geography,  getTYPtdYtdReport, setGeographyTYYtdData),
      fetchTYPtdYtdReport("geographySales", "PTD", TyReportEndPoint.geography,  getTYPtdYtdReport, setGeographyTYPtdData),
      fetchLYWtdReport("geographySales", "WTD", LyReportEndPoint.geography,  getLYWtdReport, setGeographyLYWtdData),
      fetchLYPtdReport("geographySales", "PTD", LyPtdReportEndPoint.geography,  getLYPtdReport, setGeographyLYPtdData),
      fetchLYYtdReport("geographySales", "YTD", LyYtdReportEndPoint.geography,  getLYYtdReport, setGeographyLYYtdData),
    ])

    const regionResponse = await Promise.all([
      fetchReport(getRegionReport, setRegionReport),
      fetchTYPtdYtdReport("regionSales", "YTD", TyReportEndPoint.region,  getTYPtdYtdReport, setRegionTYYtdData),
      fetchTYPtdYtdReport("regionSales", "PTD", TyReportEndPoint.region,  getTYPtdYtdReport, setRegionTYPtdData),
      fetchLYWtdReport("regionSales", "WTD", LyReportEndPoint.region,  getLYWtdReport, setRegionLYWtdData),
      fetchLYPtdReport("regionSales", "PTD", LyPtdReportEndPoint.region,  getLYPtdReport, setRegionLYPtdData),
      fetchLYYtdReport("regionSales", "YTD", LyYtdReportEndPoint.region,  getLYYtdReport, setRegionLYYtdData),
    ])

    const regionDistrictResponse = await Promise.all([
      fetchReport(getDistrictReport, setDistrictReport),
      fetchTYPtdYtdReport("regionDistrictSales", "YTD", TyReportEndPoint.regionDistrict,  getTYPtdYtdReport, setRegionDistrictTYYtdData),
      fetchTYPtdYtdReport("regionDistrictSales", "PTD", TyReportEndPoint.regionDistrict,  getTYPtdYtdReport, setRegionDistrictTYPtdData),
      fetchLYWtdReport("regionDistrictSales", "WTD", LyReportEndPoint.regionDistrict,  getLYWtdReport, setRegionDistrictLYWtdData),
      fetchLYPtdReport("regionDistrictSales", "PTD", LyPtdReportEndPoint.regionDistrict,  getLYPtdReport, setRegionDistrictLYPtdData),
      fetchLYYtdReport("regionDistrictSales", "YTD", LyYtdReportEndPoint.regionDistrict,  getLYYtdReport, setRegionDistrictLYYtdData),
    ])
  }
  useEffect(() => {
    fetchReportData()
  }, []);

  const fetchTYPtdYtdReport = async (
    reportKey: string,
    type: string,
    endPoint: TyReportEndPoint,
    getReport: (type: string, endPoint: TyReportEndPoint) => Promise<TyPtdYtdReport>,
    setReport: React.Dispatch<React.SetStateAction<TyPtdYtdItem[]>>
  ) => {
    try {
      const result = await getReport(type, endPoint);
      const data = result[reportKey as keyof TyPtdYtdReport]
      // const data = result.result;
      setReport(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error("Error fetching report:", err);
      setReport([]);
    }
  };

  const fetchLYWtdReport = async (
    reportKey: string,
    type: string,
    endPoint: LyReportEndPoint,
    getReport: (type: string, endPoint: LyReportEndPoint) => Promise<LyWtdReport>,
    setReport: React.Dispatch<React.SetStateAction<LyWtdItem[]>>
  ) => {
    try {
      const result = await getReport(type, endPoint);
      //const data = result[reportKey as keyof LyWtdReport]
      // const data = result.result;
      setReport(Array.isArray(result) ? result : []);
    } catch (err) {
      console.error("Error fetching report:", err);
      setReport([]);
    }
  };

  const fetchLYPtdReport = async (
    reportKey: string,
    type: string,
    endPoint: LyPtdReportEndPoint,
    getReport: (type: string, endPoint: LyPtdReportEndPoint) => Promise<LyYtdPtdReport>,
    setReport: React.Dispatch<React.SetStateAction<LyYtdPtdItem[]>>
  ) => {
    try {
      const result = await getReport(type, endPoint);
      //const data = result[reportKey as keyof LyWtdReport]
      // const data = result.result;
      setReport(Array.isArray(result) ? result : []);
    } catch (err) {
      console.error("Error fetching report:", err);
      setReport([]);
    }
  };

  const fetchLYYtdReport = async (
    reportKey: string,
    type: string,
    endPoint: LyYtdReportEndPoint,
    getReport: (type: string, endPoint: LyYtdReportEndPoint) => Promise<LyYtdPtdReport>,
    setReport: React.Dispatch<React.SetStateAction<LyYtdPtdItem[]>>
  ) => {
    try {
      const result = await getReport(type, endPoint);
      //const data = result[reportKey as keyof LyWtdReport]
      // const data = result.result;
      setReport(Array.isArray(result) ? result : []);
    } catch (err) {
      console.error("Error fetching report:", err);
      setReport([]);
    }
  };


  const fetchTyLtdPtdReportData = async () => {
    const _ = await Promise.all([
      fetchTYPtdYtdReport("cohortSales", "YTD", TyReportEndPoint.cohort, getTYPtdYtdReport, setCohortTYYtdData),
      fetchTYPtdYtdReport("cohortSales", "PTD", TyReportEndPoint.cohort,  getTYPtdYtdReport, setCohortTYPtdData),
      fetchTYPtdYtdReport("segmentSales", "YTD", TyReportEndPoint.segment,  getTYPtdYtdReport, setSegmentTYYtdData),
      fetchTYPtdYtdReport("segmentSales", "PTD", TyReportEndPoint.segment,  getTYPtdYtdReport, setSegmentTYPtdData),
      fetchTYPtdYtdReport("geographySales", "YTD", TyReportEndPoint.geography,  getTYPtdYtdReport, setGeographyTYYtdData),
      fetchTYPtdYtdReport("geographySales", "PTD", TyReportEndPoint.geography,  getTYPtdYtdReport, setGeographyTYPtdData),
      fetchTYPtdYtdReport("regionSales", "YTD", TyReportEndPoint.region,  getTYPtdYtdReport, setRegionTYYtdData),
      fetchTYPtdYtdReport("regionSales", "PTD", TyReportEndPoint.region,  getTYPtdYtdReport, setRegionTYPtdData),
      fetchTYPtdYtdReport("regionDistrictSales", "YTD", TyReportEndPoint.regionDistrict,  getTYPtdYtdReport, setRegionDistrictTYYtdData),
      fetchTYPtdYtdReport("regionDistrictSales", "PTD", TyReportEndPoint.regionDistrict,  getTYPtdYtdReport, setRegionDistrictTYPtdData),
    ])
  }

  // useEffect(() => {
  //   fetchTyLtdPtdReportData()
  // }, []);
  console.log("getting response: ",cohortLYWtdData);
  const percentageReportConfigs = [
    // Comp Store Transaction/Ticket Percentage::  Current Week vs Previous Year 
    { 
      title: "Comp Store Tickets Cohort Report: Current Fiscal Week vs Last Year", 
      data: cohortReport, 
      keyName: "storeConceptName", 
      header1: "Cohort", 
      type: ReportDataType.TransactionWeekVsLastYear, 
      tyYtdData: cohortTYYtdData,
      tyPtdData: cohortTYPtdData,
      lyWtdData: cohortLYWtdData,
      lyPtdData: cohortLYPtdData,
      lyYtdData: cohortLYYtdData,
    },
    { 
      title: "Comp Store Tickets Segment Report: Current Fiscal Week vs Last Year", 
      data: segmentReport, 
      keyName: "segment", 
      header1: "Segment", 
      type: ReportDataType.TransactionWeekVsLastYear, 
      tyYtdData: segmentTYYtdData, 
      tyPtdData: segmentTYPtdData,
      lyWtdData: segmentLYWtdData,
      lyPtdData: segmentLYPtdData,
      lyYtdData: segmentLYYtdData
    },
    { 
      title: "Comp Store Tickets Geography Report: Current Fiscal Week vs Last Year", 
      data: geographyReport, 
      keyName: "geographyName", 
      header1: "Geography", 
      type: ReportDataType.TransactionWeekVsLastYear, 
      tyYtdData: geographyTYYtdData,
      tyPtdData: geographyTYPtdData,
      lyWtdData: geographyLYWtdData,
      lyPtdData: geographyLYPtdData,
      lyYtdData: geographyLYYtdData
    },
    { 
      title: "Comp Store Tickets Region Report: Current Fiscal Week vs Last Year", 
      data: regionReport, 
      keyName: "Region", 
      header1: "Regional Manager", 
      type: ReportDataType.TransactionWeekVsLastYear, 
      tyYtdData: regionTYYtdData, 
      tyPtdData: regionTYPtdData, 
      lyWtdData: regionLYWtdData,
      lyPtdData: regionLYPtdData,
      lyYtdData: regionLYYtdData
    },
    {
      title: "Comp Store Tickets District Report: Current Fiscal Week vs Last Year",
      data: districtReport,
      keyName: "District",
      keyName2: "Region",
      header1: "District Manager",
      header2: "Regional Manager",
      type: ReportDataType.TransactionWeekVsLastYear,
      tyYtdData: regionDistrictTYYtdData,
      tyPtdData: regionDistrictTYPtdData,
      lyWtdData: regionDistrictLYWtdData,
      lyPtdData: regionDistrictLYPtdData,
      lyYtdData: regionDistrictLYYtdData
    },

    // Store Sales Percentage ::  Current Week vs Previous Year 
    { 
      title: "Comp Store Sales Cohort Report: Current Fiscal Week vs Last Year", 
      data: cohortReport, 
      keyName: "storeConceptName", 
      header1: "Cohort", 
      type: ReportDataType.SaleWeekVsLastYear, 
      tyYtdData: cohortTYYtdData,
      tyPtdData: cohortTYPtdData,
      lyWtdData: cohortLYWtdData,
      lyPtdData: cohortLYPtdData,
      lyYtdData: cohortLYYtdData
    },
    { 
      title: "Comp Store Sales Segment Report: Current Fiscal Week vs Last Year", 
      data: segmentReport, 
      keyName: "segment", 
      header1: "Segment", 
      type: ReportDataType.SaleWeekVsLastYear, 
      tyYtdData: segmentTYYtdData, 
      tyPtdData: segmentTYPtdData,
      lyWtdData: segmentLYWtdData,
      lyPtdData: segmentLYPtdData,
      lyYtdData: segmentLYYtdData
    },
    { 
      title: "Store Sales Geography Report: Current Fiscal Week vs Last Year", 
      data: geographyReport, 
      keyName: "geographyName", 
      header1: "Geography", 
      type: ReportDataType.SaleWeekVsLastYear, 
      tyYtdData: geographyTYYtdData,
      tyPtdData: geographyTYPtdData,
      lyWtdData: geographyLYWtdData,
      lyPtdData: geographyLYPtdData,
      lyYtdData: geographyLYYtdData
    },
    { 
      title: "Store Sales Region Report: Current Fiscal Week vs Last Year", 
      data: regionReport, 
      keyName: "Region", 
      header1: "Regional Manager", 
      type: ReportDataType.SaleWeekVsLastYear, 
      tyYtdData: regionTYYtdData, 
      tyPtdData: regionTYPtdData,
      lyWtdData: regionLYWtdData,
      lyPtdData: regionLYPtdData,
      lyYtdData: regionLYYtdData
    },
    {
      title: "Store Sales District Report: Current Fiscal Week vs Last Year",
      data: districtReport,
      keyName: "District",
      keyName2: "Region",
      header1: "District Manager",
      header2: "Regional Manager",
      type: ReportDataType.SaleWeekVsLastYear,
      tyYtdData: regionDistrictTYYtdData,
      tyPtdData: regionDistrictTYPtdData,
      lyWtdData: regionDistrictLYWtdData,
      lyPtdData: regionDistrictLYPtdData,
      lyYtdData: regionDistrictLYYtdData
    },
  ];

  const valueReportConfigs = [
    // Store Transaction/Ticket Count: Current Fiscal Week
    { 
      title: "Store Tickets Cohort Report: Current Fiscal Week", 
      data: cohortReport, 
      keyName: "storeConceptName", 
      header1: "Cohort", 
      type: ReportDataType.TransactionFiscalWeek, 
      tyYtdData: combinedTYYtdData?.cohortSales ?? [], 
      tyPtdData: combinedTYPtdData?.cohortSales ?? [], 
    },
    { 
      title: "Store Tickets Segment Report: Current Fiscal Week", 
      data: segmentReport, 
      keyName: "segment", 
      header1: "Segment", 
      type: ReportDataType.TransactionFiscalWeek, 
      tyYtdData: combinedTYYtdData?.segmentSales ?? [], 
      tyPtdData: combinedTYPtdData?.segmentSales ?? [], 
    },
    { 
      title: "Store Tickets Geography Report: Current Fiscal Week", 
      data: geographyReport, 
      keyName: "geographyName", 
      header1: "Geography", 
      type: ReportDataType.TransactionFiscalWeek, 
      tyYtdData: combinedTYYtdData?.geographySales ?? [],
      tyPtdData: combinedTYPtdData?.geographySales ?? [],
    },
    { 
      title: "Store Tickets Region Report: Current Fiscal Week", 
      data: regionReport, 
      keyName: "Region", 
      header1: "Regional Manager", 
      type: ReportDataType.TransactionFiscalWeek, 
      tyYtdData: combinedTYYtdData?.regionSales ?? [],
      tyPtdData: combinedTYPtdData?.regionSales ?? [],
     },
    {
      title: "Store Tickets District Report: Current Fiscal Week",
      data: districtReport,
      keyName: "District",
      keyName2: "Region",
      header1: "District Manager",
      header2: "Regional Manager",
      type: ReportDataType.TransactionFiscalWeek,
      tyYtdData: combinedTYYtdData?.regionDistrictSales ?? [],
      tyPtdData: combinedTYPtdData?.regionDistrictSales ?? [],
    },

    // Store Sales: Current Fiscal Week
    { 
      title: "Store Sales Cohort Report: Current Fiscal Week", 
      data: cohortReport, keyName: "storeConceptName", 
      header1: "Cohort", 
      type: ReportDataType.SalesFiscalWeek, 
      tyYtdData: combinedTYYtdData?.cohortSales ?? [], 
      tyPtdData: combinedTYPtdData?.cohortSales ?? [], 
    },
    { 
      title: "Store Sales Segment Report: Current Fiscal Week", 
      data: segmentReport, 
      keyName: "segment", 
      header1: "Segment", 
      type: ReportDataType.SalesFiscalWeek, 
      tyYtdData: combinedTYYtdData?.segmentSales ?? [], 
      tyPtdData: combinedTYPtdData?.segmentSales ?? [], 
    },
    { 
      title: "Store Sales Geography Report: Current Fiscal Week", 
      data: geographyReport, 
      keyName: "geographyName", 
      header1: "Geography", 
      type: ReportDataType.SalesFiscalWeek, 
      tyYtdData: combinedTYYtdData?.geographySales ?? [],
      tyPtdData: combinedTYPtdData?.geographySales ?? [],
    },
    { 
      title: "Store Sales Region Report: Current Fiscal Week", 
      data: regionReport, 
      keyName: "Region", 
      header1: "Regional Manager", 
      type: ReportDataType.SalesFiscalWeek, 
      tyYtdData: combinedTYYtdData?.regionSales ?? [], 
      tyPtdData: combinedTYPtdData?.regionSales ?? [], 
    },
    {
      title: "Store Sales District Report: Current Fiscal Week",
      data: districtReport,
      keyName: "District",
      keyName2: "Region",
      header1: "District Manager",
      header2: "Regional Manager",
      type: ReportDataType.SalesFiscalWeek,
      tyYtdData: combinedTYYtdData?.regionDistrictSales ?? [],
      tyPtdData: combinedTYPtdData?.regionDistrictSales ?? [],
    },
  ]

  const promotionHeaderItem = (title: string, value: string) => {
    const comp = (
      <div className="m-3 d-flex" style={{ gap: 10 }}>
        <Typography variant="body1" gutterBottom sx={{ color: 'primary.text' }}>
          {title}:
        </Typography>
        <Typography variant="body1" gutterBottom sx={{ fontWeight: 'bold', color: 'grey' }}>
          {value}
        </Typography>
      </div>
    )

    return comp
    
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4, padding: 0 }}>
      <Typography variant="h4" sx={{ mb: 4, fontWeight: "bold", color: "primary.main", }}>
        Reports Dashboard
      </Typography>
      <StyledPaper elevation={0}>
        <Box position="relative" minHeight="200px">
        
          <Grid container spacing={2} style={{ display: 'flex', alignItems: 'stretch' }}>
            {/* Start::  Promotions (This Year vs Last Year) */}
            <Grid item xs={12} md={6} style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography variant="h6" gutterBottom className="ms-4 mt-3" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                Promotions (This Year vs Last Year)
              </Typography>
              <Card className="ms-4" variant="outlined" style={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs>
                    <Typography variant="body1" gutterBottom className="m-3" sx={{ fontWeight: 'bold', color: 'primary.text' }}>
                      This Year
                    </Typography>
                    {promotionHeaderItem("Marketing Campaigns", "Marketing Campaigns Value")}
                  </Grid>
                  <Grid item>
                    <Typography variant="body1" gutterBottom className="m-3" sx={{ fontWeight: 'bold', color: 'primary.text', textAlign: 'end' }}>
                      Last Year
                    </Typography>
                    {promotionHeaderItem("Marketing Campaigns", "Marketing Campaigns Value")}
                  </Grid>
                </Grid>
              </Card>
            </Grid>
            {/* End::  Promotions (This Year vs Last Year) */}

            {/* Other::  Other (This Year vs Last Year) */}
            <Grid item xs={12} md={6} style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography variant="h6" gutterBottom className="ms-4 mt-3" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                Others (This Year vs Last Year)
              </Typography>
              <Card className="me-4" variant="outlined" style={{ flexGrow: 1 }}>
                <Grid container spacing={2} >
                  <Grid item xs>
                    <Typography variant="body1" gutterBottom className="m-3" sx={{ fontWeight: 'bold', color: 'primary.text' }}>
                      This Year
                    </Typography>
                    {promotionHeaderItem("Holiday / Event", "Holiday Event Value")}
                    {promotionHeaderItem("Other", "Other Value")}
                  </Grid>
                  <Grid item>
                    <Typography variant="body1" gutterBottom className="m-3" sx={{ fontWeight: 'bold', color: 'primary.text', textAlign: 'end' }}>
                      Last Year
                    </Typography>
                    {promotionHeaderItem("Holiday / Event", "Holiday Event Value")}
                    {promotionHeaderItem("Other", "Other Value")}
                  </Grid>
                </Grid>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </StyledPaper>
      {percentageReportConfigs.map((config, index) => (
        <StyledPaper key={index} elevation={0}>
          <Box position="relative" minHeight="200px">
            <LoadingOverlay loading={config.data.loading} />
            {!config.data.loading && !config.data.error && (
              <ReportPercentageTable
                title={config.title}
                data={config.data.data}
                keyName={config.keyName}
                keyName2={config.keyName2}
                header1={config.header1}
                header2={config.header2}
                dataType={config.type}
                tyYtdData={config.tyYtdData}
                tyPtdData={config.tyPtdData}
                lyWtdData={config.lyWtdData}
                lyPtdData={config.lyPtdData}
                lyYtdData={config.lyYtdData}
              />
            )}
            {config.data.error && 
            <div className="d-flex justify-content-center align-items-center" style={{minHeight: 200}} >
              <Typography color="error">
                {config.data.error}
              </Typography>
            </div>
            }
          </Box>
        </StyledPaper>
      ))}

      {/* Value Report without comparison */}
      {valueReportConfigs.map((config, index) => (
        <StyledPaper key={index} elevation={0}>
          <Box position="relative" minHeight="200px">
            <LoadingOverlay loading={config.data.loading} />
            {!config.data.loading && !config.data.error && (
              <ReportValueTable
                title={config.title}
                data={config.data.data}
                keyName={config.keyName}
                keyName2={config.keyName2}
                header1={config.header1}
                header2={config.header2}
                dataType={config.type}
              />
            )}
            {config.data.error && 
            <div className="d-flex justify-content-center align-items-center" style={{minHeight: 200}} >
              <Typography color="error">
                {config.data.error}
              </Typography>
            </div>
            }
          </Box>
        </StyledPaper>
      ))}

    </Container>
  );
};

export default ReportPage;
