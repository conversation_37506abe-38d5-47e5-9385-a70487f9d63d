import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { createTheme, styled, ThemeProvider } from "@mui/material/styles";
import React from "react";
import { LyWtdItem, LyYtdPtdItem, ReportItem, TyPtdYtdItem } from "../../../../__redux/reportSlice";
import { ReportDataType } from "../../../constants/constants";
import { formatNumberToCurrency } from "../../ai/components/utils";

interface Data {
  name: string;
  name2: string;
  count: number;
  days: { [key: string]: string };
}

interface ReportProps {
  data: ReportItem[];
  keyName: string;
  keyName2?: string;
  title: string;
  header1: string;
  header2?: string;
  dataType: ReportDataType;
  tyYtdData: TyPtdYtdItem[];
  tyPtdData: TyPtdYtdItem[];
  lyWtdData: LyWtdItem[];
  lyPtdData: LyYtdPtdItem[];
  lyYtdData: LyYtdPtdItem[];
}

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  background: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[4],
  overflow: "hidden",
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: "none",
  padding: theme.spacing(2),
  "&.header": {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    fontWeight: "bold",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const calculatePercentageChange = (thisYear: number, previousYear: number): string => {
  if (previousYear === 1 || previousYear === 0 || previousYear === null || previousYear === undefined) return "N/A";
  if (thisYear === 1 || thisYear === 0 || thisYear === null || thisYear === undefined) return "0.0%";
  if (typeof previousYear === "string" || typeof thisYear === "string") {
    const numPreviousYear = Number(previousYear);
    const numThisYear = Number(thisYear);
    if (numPreviousYear === 1 || numPreviousYear === 0 || numPreviousYear === null || numPreviousYear === undefined) return "N/A";
    if (numThisYear === 1 || numThisYear === 0 || numThisYear === null || numThisYear === undefined) return "0.0%";
    const diff = numThisYear / numPreviousYear;
    const change = (diff - 1) * 100;
    return change.toFixed(1) + "%";
  }
  const diff = thisYear / previousYear;
  const change = (diff - 1) * 100;
  return change.toFixed(1) + "%";
};

const calculateWeeklyTotalPercentage = (field: Data): string => {
  const values = ["Thursday", "Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday"].map((day) => parseFloat(field.days[day] || "0"));
  const validValues = values.filter((value) => !isNaN(value));
  if (validValues.length === 0) return "N/A";
  const total = validValues.reduce((sum, value) => sum + value, 0) / validValues.length;
  return total.toFixed(1) + "%";
};

const calculateWeeklyTotal = (field: Data): string => {
  const values = ["Thursday", "Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday"].map((day) => parseFloat(field.days[day] || "0"));
  const validValues = values.filter((value) => !isNaN(value));
  if (validValues.length === 0) return "N/A";
  const total = validValues.reduce((sum, value) => sum + value, 0);
  return formatNumberToCurrency(total);
};

const ReportPercentageTable: React.FC<ReportProps> = ({
  data,
  keyName,
  keyName2,
  title,
  header1,
  header2,
  dataType,
  tyYtdData,
  tyPtdData,
  lyWtdData,
  lyPtdData,
  lyYtdData,
}) => {
  const days = ["Thursday", "Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday"];

  const groupedData: { [key: string]: Data } = data.reduce((acc, item) => {
    const key = item?.[keyName as keyof ReportItem];
    const key2 = item?.[keyName2 as keyof ReportItem];
    if (!acc[key]) {
      acc[key] = {
        name: key as string,
        name2: key2 as string,
        count: 0,
        days: {},
      };
    }
    acc[key].count = item.storeCnt;
    switch (dataType) {
      case ReportDataType.TransactionWeekVsLastYear:
        acc[key].days[item.dayname] = calculatePercentageChange(item.thisYearTrans, item.previousYear_Transactions);
        break;
      case ReportDataType.SaleWeekVsLastYear:
        acc[key].days[item.dayname] = calculatePercentageChange(item.thisYearNetSales, item.previousYear_NetSales);
        break;
    }

    return acc;
  }, {} as { [key: string]: Data });

  const grouped = Object.values(groupedData);

  const calculateTotal = (day: string): string => {
    if (day === "weekly") {
      const weeklyTotals = grouped.map((field) => parseFloat(calculateWeeklyTotalPercentage(field)));
      const validTotals = weeklyTotals.filter((value) => !isNaN(value));
      if (validTotals.length === 0) return "N/A";
      const total = validTotals.reduce((sum, value) => sum + value, 0) / validTotals.length;
      return total.toFixed(1) + "%";
    }

    const values = grouped.map((field) => parseFloat(field.days[day] || "0"));
    const validValues = values.filter((value) => !isNaN(value));
    if (validValues.length === 0) return "N/A";
    const total = validValues.reduce((sum, value) => sum + value, 0) / validValues.length;
    return total.toFixed(1) + "%";
  };

  const getTyPtdRowData = (data: TyPtdYtdItem[], type: ReportDataType, header1: string, key: string, key2?: string) => {
    if (data.length === 0) return null;
    if (header1 === "Cohort") {
      const rowItem = data.find((item) => item.storeConceptName === key);
      if (rowItem) {
        switch (type) {
          case ReportDataType.TransactionWeekVsLastYear:
            const tyCohortTransValue = calculatePercentageChange(rowItem.thisYearTrans ?? 1, rowItem.previousYear_Transactions ?? 1);
            return tyCohortTransValue;
          case ReportDataType.SaleWeekVsLastYear:
            const tyCohortSalesValue = calculatePercentageChange(rowItem.thisYearNetSales ?? 1, rowItem.previousYear_NetSales ?? 1);
            return tyCohortSalesValue;
        }
      } else {
        return null;
      }
    } else {
      let rowItems: TyPtdYtdItem[] = [];
      // District - Region Graph
      if (header1 === "District Manager") {
        rowItems = data.filter((item) => item.District === key && item.Region === (key2 ?? ""));
      }
      // Segment Graph
      else if (header1 === "Segment") {
        rowItems = data.filter((item) => item.Segment === key);
      }
      //  Geography Graph
      else if (header1 === "Geography") {
        rowItems = data.filter((item) => item.geographyName === key);
      }
      // RegionGraph
      else if (header1 === "Regional Manager") {
        rowItems = data.filter((item) => item.Region === key);
      }

      if (rowItems.length === 0) return null;

      switch (type) {
        case ReportDataType.TransactionWeekVsLastYear:
          const thisYearTrans = rowItems
            .map((item) => item.thisYearTrans)
            .filter((item) => item !== null && item !== undefined)
            .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
          let previousYearTrans = rowItems
            .map((item) => item.previousYear_Transactions)
            .filter((item) => item !== null && item !== undefined)
            .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
          previousYearTrans = previousYearTrans == 0 ? 1 : previousYearTrans;
          const tyTransValue = calculatePercentageChange(thisYearTrans ?? 0, previousYearTrans ?? 0);
          return tyTransValue;
        case ReportDataType.SaleWeekVsLastYear:
          const thisYearSales = rowItems
            .map((item) => item.thisYearNetSales)
            .filter((item) => item !== null && item !== undefined)
            .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
          let previousYearSales = rowItems
            .map((item) => item.previousYear_NetSales)
            .filter((item) => item !== null && item !== undefined)
            .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
          previousYearSales = previousYearSales == 0 ? 1 : previousYearSales;
          const tySalesValue = calculatePercentageChange(thisYearSales ?? 0, previousYearSales ?? 0);
          return tySalesValue;
      }
    }
  };

  const calculateTYTotal = (data: TyPtdYtdItem[], type: ReportDataType): string => {
    const weeklyTYPercentage = grouped.map((field) => parseFloat(getTyPtdRowData(data, type, header1, field.name, field.name2) ?? ""));
    const validWeeklyTYTotals = weeklyTYPercentage.filter((value) => !isNaN(value));
    const total = validWeeklyTYTotals.reduce((sum, value) => sum + value, 0) / validWeeklyTYTotals.length;
    return !isNaN(total) ? total.toFixed(1) + "%" : "0.0%";
  };

  const calculateLYTotal = (data: LyWtdItem[], type: ReportDataType): string => {
    const weeklyTYPercentage = grouped.map((field) => parseFloat(getLyWtdRowData(data, type, header1, field.name, field.name2) ?? ""));
    const validWeeklyTYTotals = weeklyTYPercentage.filter((value) => !isNaN(value));
    const total = validWeeklyTYTotals.reduce((sum, value) => sum + value, 0) / validWeeklyTYTotals.length;
    return !isNaN(total) ? total.toFixed(1) + "%" : "0.0%";
  };

  const calculateLYYtdPtdTotal = (data: LyYtdPtdItem[], type: ReportDataType): string => {
    const weeklyTYPercentage = grouped.map((field) => parseFloat(getLyYtdPtdRowData(data, type, header1, field.name, field.name2) ?? ""));
    const validWeeklyTYTotals = weeklyTYPercentage.filter((value) => !isNaN(value));
    const total = validWeeklyTYTotals.reduce((sum, value) => sum + value, 0) / validWeeklyTYTotals.length;
    return !isNaN(total) ? total.toFixed(1) + "%" : "0.0%";
  };

  const calculateLYYtdPtdCohortTotal = (data: LyYtdPtdItem[], type: ReportDataType): string => {
    const weeklyTYPercentage = grouped.map((field) => parseFloat(getLyYtdPtdCohortRowData(data, type, header1, field.name, field.name2) ?? ""));
    const validWeeklyTYTotals = weeklyTYPercentage.filter((value) => !isNaN(value));
    const total = validWeeklyTYTotals.reduce((sum, value) => sum + value, 0) / validWeeklyTYTotals.length;
    return !isNaN(total) ? total.toFixed(1) + "%" : "0.0%";
  };

  const getLyWtdRowData = (data: LyWtdItem[], type: ReportDataType, header1: string, key: string, key2?: string) => {
    if (data.length === 0) return null;
    let rowItems: LyWtdItem[] = [];
    if (header1 === "Cohort") {
      rowItems = data.filter((item) => item.storeConceptName === key && item.Comp === "Comp");
    }
    // District - Region Graph
    if (header1 === "District Manager") {
      rowItems = data.filter((item) => item.District === key && item.Region === (key2 ?? ""));
    }
    // Segment Graph
    else if (header1 === "Segment") {
      rowItems = data.filter((item) => item.Segment === key);
    }
    //  Geography Graph
    else if (header1 === "Geography") {
      rowItems = data.filter((item) => item.geographyName === key);
    }
    // RegionGraph
    else if (header1 === "Regional Manager") {
      rowItems = data.filter((item) => item.Region === key);
    }

    if (rowItems.length === 0) return null;

    switch (type) {
      case ReportDataType.TransactionWeekVsLastYear:
        const prevYearTrans = rowItems
          .map((item) => item.previousYearTrans)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        let prevToPrevYearTrans = rowItems
          .map((item) => item.previousToPreviousYear_Transactions)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        prevToPrevYearTrans = prevToPrevYearTrans == 0 ? 1 : prevToPrevYearTrans;
        const lyTransValue = calculatePercentageChange(prevYearTrans ?? 0, prevToPrevYearTrans ?? 0);
        return lyTransValue;
      case ReportDataType.SaleWeekVsLastYear:
        const prevYearSales = rowItems
          .map((item) => item.previousYearNetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        let prevToPrevYearSales = rowItems
          .map((item) => item.previousToPreviousYear_NetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        prevToPrevYearSales = prevToPrevYearSales == 0 ? 1 : prevToPrevYearSales;
        const lySalesValue = calculatePercentageChange(prevYearSales ?? 0, prevToPrevYearSales ?? 0);
        return lySalesValue;
    }
  };

  const getLyYtdPtdCohortRowData = (data: LyYtdPtdItem[], type: ReportDataType, header1: string, key: string, key2?: string) => {
    if (data.length === 0) return null;
    let rowItems: LyYtdPtdItem[] = [];
    if (header1 === "Cohort") {
      rowItems = data.filter((item) => item.storeConceptName === key && item.Comp === "Comp");
    }

    if (rowItems.length === 0) return null;

    switch (type) {
      case ReportDataType.TransactionWeekVsLastYear:
        const prevYearTrans = rowItems
          .map((item) => item.lastYearTrans)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        let prevToPrevYearTrans = rowItems
          .map((item) => item.twoYearsAgoTransactions)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        prevToPrevYearTrans = prevToPrevYearTrans == 0 ? 1 : prevToPrevYearTrans;
        // console.log("test 1", prevYearTrans, prevToPrevYearTrans);
        const lyTransValue = calculatePercentageChange(prevYearTrans ?? 0, prevToPrevYearTrans ?? 0);
        return lyTransValue;
      case ReportDataType.SaleWeekVsLastYear:
        const prevYearSales = rowItems
          .map((item) => item.lastYearNetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        let prevToPrevYearSales = rowItems
          .map((item) => item.twoYearsAgoNetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        prevToPrevYearSales = prevToPrevYearSales == 0 ? 1 : prevToPrevYearSales;
        // console.log("test 2", prevYearSales, prevToPrevYearSales);
        const lySalesValue = calculatePercentageChange(prevYearSales ?? 0, prevToPrevYearSales ?? 0);
        return lySalesValue;
    }
  };

  const getLyYtdPtdRowData = (data: LyYtdPtdItem[], type: ReportDataType, header1: string, key: string, key2?: string) => {
    if (data.length === 0) return null;
    let rowItems: LyYtdPtdItem[] = [];
    // if (header1 === "Cohort")  {
    //   rowItems = data.filter(item => item.storeConceptName === key && item.Comp === "Comp" );
    // }
    // District - Region Graph
    if (header1 === "District Manager") {
      rowItems = data.filter((item) => item.District === key && item.Region === (key2 ?? ""));
    }
    // Segment Graph
    else if (header1 === "Segment") {
      rowItems = data.filter((item) => item.Segment === key);
    }
    //  Geography Graph
    else if (header1 === "Geography") {
      rowItems = data.filter((item) => item.geographyName === key);
    }
    // RegionGraph
    else if (header1 === "Regional Manager") {
      rowItems = data.filter((item) => item.Region === key);
    }

    if (rowItems.length === 0) return null;

    switch (type) {
      case ReportDataType.TransactionWeekVsLastYear:
        const prevYearTrans = rowItems
          .map((item) => item.previousYear_Transactions)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => Number(sum ?? 0) + Number(value ?? 0), 0);
        let prevToPrevYearTrans = rowItems
          .map((item) => item.previousToPreviousYear_Transactions)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => Number(sum ?? 0) + Number(value ?? 0), 0);
        prevToPrevYearTrans = prevToPrevYearTrans == 0 ? 1 : prevToPrevYearTrans;
        // console.log("test 3", prevYearTrans, prevToPrevYearTrans);
        const lyTransValue = calculatePercentageChange(prevYearTrans ?? 0, prevToPrevYearTrans ?? 0);
        return lyTransValue;
      case ReportDataType.SaleWeekVsLastYear:
        const prevYearSales = rowItems
          .map((item) => item.previousYear_NetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        let prevToPrevYearSales = rowItems
          .map((item) => item.previousToPreviousYear_NetSales)
          .filter((item) => item !== null && item !== undefined)
          .reduce((sum, value) => (sum ?? 0) + (value ?? 0), 0);
        prevToPrevYearSales = prevToPrevYearSales == 0 ? 1 : prevToPrevYearSales;
        // console.log("test 4", prevYearSales, prevToPrevYearSales);
        const lySalesValue = calculatePercentageChange(prevYearSales ?? 0, prevToPrevYearSales ?? 0);
        return lySalesValue;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ padding: theme.spacing(3) }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: "bold", color: theme.palette.primary.main }}>
          {title}
        </Typography>
        <StyledTableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <StyledTableCell className="header">{header1}</StyledTableCell>
                {header2 && <StyledTableCell className="header">{header2}</StyledTableCell>}
                <StyledTableCell className="header" align="right">
                  Count
                </StyledTableCell>
                {days.map((day) => (
                  <StyledTableCell key={day} className="header" align="right">
                    {day}
                  </StyledTableCell>
                ))}
                <StyledTableCell className="header" align="right">
                  TY WTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  LY WTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  TY YTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  LY YTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  TY PTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  LY PTD
                </StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {grouped.map((field, index) => (
                <StyledTableRow key={index}>
                  <StyledTableCell>{field.name}</StyledTableCell>
                  {header2 && <StyledTableCell align="left">{field.name2}</StyledTableCell>}
                  <StyledTableCell align="right">{field.count}</StyledTableCell>
                  {days.map((day) => (
                    <StyledTableCell key={day} align="right">
                      {field.days[day] ? (
                        <Box
                          sx={{
                            color: parseFloat(field.days[day]) >= 0 ? "text-dark" : "error.main",
                            // fontWeight: "bold",
                          }}
                        >
                          {field.days[day]}
                        </Box>
                      ) : (
                        ""
                      )}
                    </StyledTableCell>
                  ))}
                  {/* TY WTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: parseFloat(calculateWeeklyTotalPercentage(field)) >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {calculateWeeklyTotalPercentage(field)}
                    </Box>
                  </StyledTableCell>
                  {/* LY WTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: getLyWtdRowData(lyWtdData, dataType, header1, field.name, field.name2) ?? 0 >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {getLyWtdRowData(lyWtdData, dataType, header1, field.name, field.name2)}
                    </Box>
                  </StyledTableCell>
                  {/* TY YTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: getTyPtdRowData(tyYtdData, dataType, header1, field.name, field.name2) ?? 0 >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {getTyPtdRowData(tyYtdData, dataType, header1, field.name, field.name2)}
                    </Box>
                  </StyledTableCell>
                  {/* LY YTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: getLyYtdPtdRowData(lyYtdData, dataType, header1, field.name, field.name2) ?? 0 >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {header1 === "Cohort"
                        ? getLyYtdPtdCohortRowData(lyYtdData, dataType, header1, field.name, field.name2)
                        : getLyYtdPtdRowData(lyYtdData, dataType, header1, field.name, field.name2)}
                    </Box>
                  </StyledTableCell>
                  {/* TY PTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: getTyPtdRowData(tyPtdData, dataType, header1, field.name, field.name2) ?? 0 >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {getTyPtdRowData(tyPtdData, dataType, header1, field.name, field.name2)}
                    </Box>
                  </StyledTableCell>
                  {/* LY PTD Cell */}
                  <StyledTableCell align="right">
                    <Box
                      sx={{
                        color: getLyYtdPtdRowData(lyPtdData, dataType, header1, field.name, field.name2) ?? 0 >= 0 ? "text-dark" : "error.main",
                        // fontWeight: "bold",
                      }}
                    >
                      {header1 === "Cohort"
                        ? getLyYtdPtdCohortRowData(lyPtdData, dataType, header1, field.name, field.name2)
                        : getLyYtdPtdRowData(lyPtdData, dataType, header1, field.name, field.name2)}
                    </Box>
                  </StyledTableCell>
                </StyledTableRow>
              ))}
              <StyledTableRow>
                <StyledTableCell sx={{ fontWeight: "bold" }}>Total</StyledTableCell>
                {header2 && <StyledTableCell align="left" sx={{ fontWeight: "bold" }}></StyledTableCell>}
                {/* Count Field */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  {grouped.reduce((sum, field) => sum + field.count, 0)}
                </StyledTableCell>
                {days.map((day) => (
                  <StyledTableCell
                    key={day}
                    align="right"
                    sx={{
                      // color: parseFloat(calculateTotal(day)) >= 0 ? "success.main" : "error.main",
                      color: parseFloat(calculateTotal(day)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {calculateTotal(day)}
                  </StyledTableCell>
                ))}
                {/* TY WTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateTotal("weekly")) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {calculateTotal("weekly")}
                  </Box>
                </StyledTableCell>
                {/* LY WTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateLYTotal(lyWtdData, dataType)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {calculateLYTotal(lyWtdData, dataType)}
                  </Box>
                </StyledTableCell>
                {/* TY YTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateTYTotal(tyYtdData, dataType)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {calculateTYTotal(tyYtdData, dataType)}
                  </Box>
                </StyledTableCell>
                {/* LY YTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateLYYtdPtdTotal(lyYtdData, dataType)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {header1 === "Cohort" ? calculateLYYtdPtdCohortTotal(lyPtdData, dataType) : calculateLYYtdPtdTotal(lyYtdData, dataType)}
                  </Box>
                </StyledTableCell>
                {/* TY PTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateTYTotal(tyPtdData, dataType)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {calculateTYTotal(tyPtdData, dataType)}
                  </Box>
                </StyledTableCell>
                {/* LY PTD */}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box
                    sx={{
                      color: parseFloat(calculateLYYtdPtdTotal(lyPtdData, dataType)) >= 0 ? "text-dark" : "error.main",
                      fontWeight: "bold",
                    }}
                  >
                    {header1 === "Cohort" ? calculateLYYtdPtdCohortTotal(lyPtdData, dataType) : calculateLYYtdPtdTotal(lyPtdData, dataType)}
                  </Box>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          </Table>
        </StyledTableContainer>
      </Box>
    </ThemeProvider>
  );
};

export default ReportPercentageTable;
