import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { createTheme, styled, ThemeProvider } from "@mui/material/styles";
import React from "react";
import { ReportItem } from "../../../../__redux/reportSlice";
import { ReportDataType,  } from "../../../constants/constants";
import { formatNumberToCurrency, convertFormattedCurrencyToNumber } from "../../ai/components/utils";

type DayValue = {currentValue: string, prevValue: string}
interface Data {
  name: string;
  name2: string;
  count: number;
  days: { [key: string]: DayValue };
}

interface ReportProps {
  data: ReportItem[];
  keyName: string;
  keyName2?: string;
  title: string;
  header1: string;
  header2?: string;
  dataType: ReportDataType;
}

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  background: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[4],
  overflow: "hidden",
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: "none",
  padding: theme.spacing(2),
  "&.header": {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    fontWeight: "bold",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

// const calculateWeeklyTotal = (field: Data): number => {
//   const values = ["Thursday", "Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday"].map((day) => parseFloat(field.days[day] || "0"));
//   const validValues = values.filter((value) => !isNaN(value));
//   if (validValues.length === 0) return 0;
//   const total = validValues.reduce((sum, value) => sum + value, 0);
//   return total;
// };

const ReportValueTable: React.FC<ReportProps> = ({ data, keyName, keyName2, title, header1, header2, dataType }) => {
  const days = ["Thursday", "Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday"];

  const groupedData: { [key: string]: Data } = data.reduce((acc, item) => {
    const key = item?.[keyName as keyof ReportItem];
    const key2 = item?.[keyName2 as keyof ReportItem];
    if (!acc[key]) {
      acc[key] = {
        name: key as string,
        name2: key2 as string,
        count: 0,
        days: {},
      };
    }
    acc[key].count = item.storeCnt;
    switch (dataType) {
      case ReportDataType.TransactionFiscalWeek:
        acc[key].days[item.dayname] = {currentValue: formatNumberToCurrency(item.thisYearTrans), prevValue: formatNumberToCurrency(item.previousYear_Transactions)} ;
        break;
      case ReportDataType.SalesFiscalWeek:
        acc[key].days[item.dayname] = {currentValue: formatNumberToCurrency(item.thisYearNetSales), prevValue: formatNumberToCurrency(item.previousYear_NetSales)};
        break;
    }
    
    return acc;
  }, {} as { [key: string]: Data });

  const grouped = Object.values(groupedData);

  // Calculate total values for a particular day (column wise)
  const calculateDayTotal = (day: string): string => {
    const values = grouped.map((field) => {
        const val = field.days[day]?.currentValue || "0"
        const num  = convertFormattedCurrencyToNumber(val);
        return num
    });
    const validValues = values.filter((value) => !isNaN(value));
    if (validValues.length === 0) return "N/A";
    const total = validValues.reduce((sum, value) => sum + value, 0);
    return formatNumberToCurrency(total);
  };

  // Calculate Current Weekly data
  const calculateWeeklyCWWTD = (field: Data,): string => {
    const cwDaysData = Object.keys(field.days).map((val) => convertFormattedCurrencyToNumber(field.days[val]?.currentValue || "0"));
    const total = cwDaysData.reduce((sum, val) => sum + val);
    return formatNumberToCurrency(total);
  }

  // Calculate Total Current Week CW WTD data
  const calculateTotalCWWTD = () => {
    const weeklyTotals = grouped.map((field) => convertFormattedCurrencyToNumber(calculateWeeklyCWWTD(field)));
    const validTotals = weeklyTotals.filter((value) => !isNaN(value));
    if (validTotals.length === 0) return "N/A";
    const total = validTotals.reduce((sum, value) => sum + value, 0);
    return formatNumberToCurrency(total);
  }

  // Calculate Current Weekly data
  const calculateWeeklyLWWTD = (field: Data,): string => {
    const cwDaysData = Object.keys(field.days).map((val) => convertFormattedCurrencyToNumber(field.days[val]?.prevValue || "0"));
    const total = cwDaysData.reduce((sum, val) => sum + val);
    return formatNumberToCurrency(total);
  }

  // Calculate Total Current Week CW WTD data
  const calculateTotalLWWTD = () => {
    const weeklyTotals = grouped.map((field) => convertFormattedCurrencyToNumber(calculateWeeklyLWWTD(field)));
    const validTotals = weeklyTotals.filter((value) => !isNaN(value));
    if (validTotals.length === 0) return "N/A";
    const total = validTotals.reduce((sum, value) => sum + value, 0);
    return formatNumberToCurrency(total);
  }

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ padding: theme.spacing(3) }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: "bold", color: theme.palette.primary.main }}>
          {title}
        </Typography>
        <StyledTableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <StyledTableCell className="header">{header1}</StyledTableCell>
                {header2 && <StyledTableCell className="header">{header2}</StyledTableCell>}
                <StyledTableCell className="header" align="right">
                  Count
                </StyledTableCell>
                {days.map((day) => (
                  <StyledTableCell key={day} className="header" align="right">
                    {day}
                  </StyledTableCell>
                ))}
                <StyledTableCell className="header" align="right">
                  CW WTD
                </StyledTableCell>
                <StyledTableCell className="header" align="right">
                  LW WTD
                </StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {grouped.map((field, index) => (
                <StyledTableRow key={index}>
                  <StyledTableCell>{field.name}</StyledTableCell>
                  {header2 && <StyledTableCell align="left">{field.name2}</StyledTableCell>}
                  <StyledTableCell align="right">{field.count}</StyledTableCell>
                  {days.map((day) => (
                    <StyledTableCell key={day} align="right">
                      {field.days[day] ? (
                        <Box>
                          {field.days[day].currentValue}
                        </Box>
                      ) : (
                        ""
                      )}
                    </StyledTableCell>
                  ))}
                  {/* CW WTD Data */}
                  <StyledTableCell align="right">
                    <Box>
                      {calculateWeeklyCWWTD(field)}
                    </Box>
                  </StyledTableCell>
                  <StyledTableCell align="right">
                    <Box>
                      {calculateWeeklyLWWTD(field)}
                    </Box>
                  </StyledTableCell>
                </StyledTableRow>
              ))}
              <StyledTableRow>
                <StyledTableCell sx={{ fontWeight: "bold" }}>Total</StyledTableCell>
                {header2 && <StyledTableCell align="left" sx={{ fontWeight: "bold" }}></StyledTableCell>}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  {grouped.reduce((sum, field) => sum + field.count, 0)}
                </StyledTableCell>
                {days.map((day) => (
                  <StyledTableCell key={day} align="right" sx={{ fontWeight: "bold" }}>
                    {calculateDayTotal(day)}
                  </StyledTableCell>
                ))}
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box>
                    {calculateTotalCWWTD()}
                  </Box>
                </StyledTableCell>
                <StyledTableCell align="right" sx={{ fontWeight: "bold" }}>
                  <Box>
                    {calculateTotalLWWTD()}
                  </Box>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          </Table>
        </StyledTableContainer>
      </Box>
    </ThemeProvider>
  );
};

export default ReportValueTable;
