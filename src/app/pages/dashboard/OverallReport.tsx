import { Box, Chip, Grid, Paper, Skeleton, Typography } from "@mui/material";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { styled } from "@mui/system";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Cell, Pie, <PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON>ltip, <PERSON>Axis, <PERSON>Axis } from "recharts";

interface OverallStats {
  totalRuns: number;
  successRate: string;
  totalDuration: string;
  successfulRuns: number;
  failedRuns: number;
}

interface PipelineStats {
  pipelineName: string;
  successfulRuns: number;
  failedRuns: number;
}

interface AllPipelineStatuses {
  overallStats: OverallStats;
  pipelineSpecificStats: PipelineStats[];
  isLoadingOverallReport: boolean;
}

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: "12px",
  transition: "transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out",
  "&:hover": {
    transform: "translateY(-5px)",
  },
}));

const renderSkeletonLoading = () => {
  return (
    <Box sx={{ flexGrow: 1, p: 3, minHeight: "100vh" }}>
      <Skeleton variant="text" width={300} height={48} sx={{ mb: 4 }} />
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Skeleton variant="rectangular" height={300} sx={{ borderRadius: 2 }} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Skeleton variant="rectangular" height={300} sx={{ borderRadius: 2 }} />
        </Grid>
        <Grid item xs={12}>
          <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 2 }} />
        </Grid>
      </Grid>
    </Box>
  );
};

const OverallReport: React.FC<AllPipelineStatuses> = ({ overallStats, pipelineSpecificStats, isLoadingOverallReport }) => {
  const pieChartData = [
    { name: "Successful", value: overallStats.successfulRuns },
    { name: "Failed", value: overallStats.failedRuns },
  ];

  if (isLoadingOverallReport) {
    return <ThemeProvider theme={theme}>{renderSkeletonLoading()}</ThemeProvider>;
  }

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: "bold", color: theme.palette.primary.main, mb: 4 }}>
          Pipeline Performance: Last 100 Runs (Past 7 Days)
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <StyledPaper elevation={3} sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold", color: theme.palette.primary.main }}>
                Overall Statistics
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2, flexGrow: 1, justifyContent: "space-between" }}>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                    Total Runs:
                  </Typography>
                  <Chip label={overallStats.totalRuns} color="primary" variant="outlined" />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                    Success Rate:
                  </Typography>
                  <Chip
                    label={`${overallStats.successRate}`}
                    color={parseFloat(overallStats.successRate) > 80 ? "success" : "warning"}
                    variant="outlined"
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                    Success Runs:
                  </Typography>
                  <Chip label={overallStats.successfulRuns} color="success" variant="outlined" />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                    Failed Runs:
                  </Typography>
                  <Chip label={overallStats.failedRuns} color="error" variant="outlined" />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography variant="body1" sx={{ fontWeight: "medium" }}>
                    Total Duration:
                  </Typography>
                  <Chip
                    label={
                      parseInt(overallStats.totalDuration) > 60
                        ? `${(parseInt(overallStats.totalDuration) / 60).toFixed(2)} hours`
                        : `${overallStats.totalDuration} mins`
                    }
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              </Box>
            </StyledPaper>
          </Grid>

          <Grid item xs={12} md={6}>
            <StyledPaper sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
              <Typography variant="h6" gutterBottom>
                Run Distribution
              </Typography>
              <Box sx={{ flexGrow: 1, display: "flex", alignItems: "center", justifyContent: "center" }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius="60%"
                      outerRadius="80%"
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={theme.palette.primary.main} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </StyledPaper>
          </Grid>

          <Grid item xs={12}>
            <StyledPaper>
              <Typography variant="h6" gutterBottom>
                Pipeline-Specific Statistics
              </Typography>
              <Box sx={{ height: 400 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={pipelineSpecificStats} margin={{ top: 20, right: 30, left: 20, bottom: 100 }}>
                    <XAxis
                      dataKey="pipelineName"
                      stroke={theme.palette.text.secondary}
                      interval={0}
                      angle={-45}
                      textAnchor="end"
                      height={60}
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis stroke={theme.palette.text.secondary} />
                    <Tooltip
                      contentStyle={{ backgroundColor: theme.palette.background.paper, borderRadius: "8px" }}
                      itemStyle={{ color: theme.palette.text.primary }}
                    />
                    <Bar dataKey="successfulRuns" name="Successful Runs" fill={theme.palette.primary.main} />
                    <Bar dataKey="failedRuns" name="Failed Runs" fill={theme.palette.secondary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </StyledPaper>
          </Grid>
        </Grid>
      </Box>
    </ThemeProvider>
  );
};

export default OverallReport;
