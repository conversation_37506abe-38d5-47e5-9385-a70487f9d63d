import { Check, Close, PlayArrow, Schedule, Search } from "@mui/icons-material";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  InputAdornment,
  Pagination,
  Paper,
  Skeleton,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { styled, Theme } from "@mui/system";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import debounce from "lodash/debounce";
import React, { FC, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ianGrid, Cell, Legend, Tooltip as Recharts<PERSON>ool<PERSON>, ResponsiveContainer, XAxis, YAxis } from "recharts";
import {
  setEndDate,
  setError,
  setLoading,
  setPage,
  setPipelineData,
  setPipelineRuns,
  setSearchQuery,
  setSelectedPipeline,
  setStartDate,
} from "../../../__redux/dashboardSlice";
import { pushNewAlert } from "../../../__redux/generalSlice";
import { getAllPipelineStatuses, getPipelineList, getPipelineRuns } from "../../api/dashboardApi";
import { RootState } from "../../store";
import OverallReport from "./OverallReport";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StyledCard = styled(Card)(() => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  transition: "all 0.3s ease-in-out",
  "&:hover": {
    transform: "translateY(-5px)",
    boxShadow: `0 10px 20px rgba(0,0,0,0.2)`,
  },
  borderRadius: 16,
  overflow: "hidden",
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  borderRadius: theme.shape.borderRadius,
  fontWeight: "bold",
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: "24px",
  textTransform: "none",
  fontWeight: "bold",
  padding: theme.spacing(1, 3),
}));

const StatusChip = styled(Chip)(({ theme, status }: { theme: Theme; status: string }) => ({
  fontWeight: "bold",
  borderRadius: theme.shape.borderRadius,
  ...(status === "Succeeded" && {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
  }),
  ...(status === "Failed" && {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
  }),
  ...(status === "InProgress" && {
    backgroundColor: theme.palette.info.main,
    color: theme.palette.info.contrastText,
  }),
}));

interface PipelineRun {
  runId: string;
  runGroupId: string;
  isLatest: boolean;
  pipelineName: string;
  parameters: Record<string, unknown>;
  invokedBy: {
    name: string;
    id: string;
    invokedByType: string;
  };
  lastUpdated: string;
  runStart: string;
  runEnd: string;
  durationInMs: number;
  status: string;
  message: string;
  id: string;
  debugRunId: string | null;
  pipelineReturnValue: Record<string, unknown>;
  annotations: unknown[];
  runDimension: Record<string, unknown>;
}

interface PipelineRunsResponse {
  value: PipelineRun[];
}

interface PipelineStats {
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: number;
  averageDuration: number;
  lastRunStatus: string;
  lastRunTime: string;
}

const PipelineStatsTab: FC<{ stats: PipelineStats; runs: PipelineRun[] }> = ({ stats, runs }) => {
  const chartData = runs.map(run => ({
    date: new Date(run.runStart).toLocaleDateString("en-US", { month: "short", day: "numeric", hour: "numeric", minute: "numeric" }),
    duration: run.durationInMs / 1000,
    status: run.status,
    successfulRuns: stats.successfulRuns,
    failedRuns: stats.failedRuns,
  }));

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <StyledCard>
          <CardContent>
            <Grid container spacing={2} justifyContent="space-between" px={2} sx={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
              <Box>
                <Typography variant="subtitle2">Total Runs</Typography>
                <Typography variant="h5" sx={{ fontWeight: "bold", color: "primary.main" }}>
                  {stats.totalRuns}
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Success Rate</Typography>
                <Typography variant="h5" sx={{ fontWeight: "bold", color: "primary.main" }}>
                  {stats.successRate.toFixed(2)}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Successful Runs</Typography>
                <Typography variant="h5" sx={{ fontWeight: "bold", color: "primary.main" }}>
                  {stats.successfulRuns}
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Failed Runs</Typography>
                <Typography variant="h5" sx={{ fontWeight: "bold", color: "primary.main" }}>
                  {stats.failedRuns}
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Average Duration</Typography>
                <Typography variant="h5" sx={{ fontWeight: "bold", color: "primary.main" }}>
                  {stats.averageDuration > 60
                    ? `${Math.floor(stats.averageDuration / 60)}m ${Math.round(stats.averageDuration % 60)}s`
                    : `${stats.averageDuration.toFixed(2)}s`}
                </Typography>
              </Box>
            </Grid>
          </CardContent>
        </StyledCard>
      </Grid>
      <Grid item xs={12}>
        <StyledCard>
          <CardContent>
            <Typography variant="h6">Run Duration Over Time</Typography>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={value => value.split(",")[0]} angle={-45} textAnchor="end" height={60} />
                <YAxis label={{ value: "Duration (minutes)", angle: -90, position: "insideLeft" }} tickFormatter={value => `${Math.round(value / 60)}`} />
                <RechartsTooltip
                  labelFormatter={label => label}
                  formatter={(value, name) => [`${typeof value === "number" ? `${Math.floor(value / 60)}m ${Math.round(value % 60)}s` : value}`, name]}
                />
                <Bar dataKey="duration">
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={
                        entry.status === "Succeeded" ? "#4caf50" : entry.status === "Failed" ? "#f44336" : entry.status === "Cancelled" ? "#9e9e9e" : "#2196f3"
                      }
                    />
                  ))}
                </Bar>
                <Legend />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </StyledCard>
      </Grid>
    </Grid>
  );
};

export interface AllPipelineStatuses {
  overallStats: {
    totalRuns: number;
    successfulRuns: number;
    failedRuns: number;
    successRate: string;
    totalDuration: string;
    startTime: string;
    endTime: string;
  };
  pipelineSpecificStats: {
    pipelineName: string;
    totalRuns: number;
    successfulRuns: number;
    failedRuns: number;
    totalDuration: string;
    successRate: string;
  }[];
}

export const DashboardPage: FC = () => {
  const dispatch = useDispatch();
  const { pipelineData, loading, error, page, limit, selectedPipeline, pipelineRuns, startDate, endDate, searchQuery } = useSelector(
    (state: RootState) => state.dashboard
  );

  const [openDialog, setOpenDialog] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [activeTab, setActiveTab] = useState<"overall" | "dashboard">("dashboard");
  const [isLoadingOverallReport, setIsLoadingOverallReport] = useState(false);
  const [allPipelineStatuses, setAllPipelineStatuses] = useState<AllPipelineStatuses>({
    overallStats: {
      totalRuns: 0,
      successfulRuns: 0,
      failedRuns: 0,
      successRate: "0%",
      totalDuration: "0s",
      startTime: "",
      endTime: "",
    },
    pipelineSpecificStats: [],
  });

  useEffect(() => {
    const fetchAllPipelineStatuses = async () => {
      try {
        setIsLoadingOverallReport(true);
        const data = await getAllPipelineStatuses();
        setAllPipelineStatuses(data);
      } catch (err) {
        console.error("Error fetching all pipeline statuses:", err);
      } finally {
        setIsLoadingOverallReport(false);
      }
    };
    fetchAllPipelineStatuses();
  }, []);

  const [pipelineStats, setPipelineStats] = useState<PipelineStats>({
    totalRuns: 0,
    successRate: 0,
    averageDuration: 0,
    lastRunStatus: "",
    lastRunTime: "",
    successfulRuns: 0,
    failedRuns: 0,
  });

  const debouncedSearch = useCallback(
    debounce((query: string) => {
      dispatch(setPage(1));
      dispatch(setSearchQuery(query));
    }, 700),
    [dispatch]
  );

  useEffect(() => {
    const fetchPipelineData = async () => {
      try {
        dispatch(setLoading(true));
        const data = await getPipelineList(page, limit, searchQuery);
        dispatch(setPipelineData(data));
      } catch (err) {
        dispatch(setError("Failed to fetch pipeline data. Please try again later."));
        dispatch(
          pushNewAlert({
            type: "error",
            message: "Failed to fetch pipeline data. Please try again later.",
            show: true,
            heading: "Error",
            errMessage: "",
            errDescription: "",
          })
        );
      } finally {
        dispatch(setLoading(false));
      }
    };

    fetchPipelineData();
  }, [dispatch, page, limit, searchQuery]);

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    dispatch(setPage(value));
  };

  const handlePipelineClick = async (pipelineName: string) => {
    dispatch(setSelectedPipeline(pipelineName));
    setOpenDialog(true);
    await fetchPipelineRuns(pipelineName);
  };

  const calculatePipelineStats = (runs: PipelineRun[]): PipelineStats => {
    const totalRuns = runs.length;
    const successfulRuns = runs.filter(run => run.status === "Succeeded").length;
    const failedRuns = runs.filter(run => run.status === "Failed").length;
    const successRate = (successfulRuns / totalRuns) * 100;
    const totalDuration = runs.reduce((sum, run) => sum + run.durationInMs, 0);
    const averageDuration = totalDuration / totalRuns / 1000;
    const lastRun = runs[0];

    return {
      totalRuns,
      successRate,
      successfulRuns,
      failedRuns,
      averageDuration,
      lastRunStatus: lastRun ? lastRun.status : "",
      lastRunTime: lastRun ? lastRun.runStart : "",
    };
  };

  const fetchPipelineRuns = async (pipelineName: string) => {
    try {
      dispatch(setLoading(true));
      const runs: PipelineRunsResponse = await getPipelineRuns(pipelineName, startDate, endDate);
      const sortedRuns = runs.value.sort((a, b) => new Date(b.runStart).getTime() - new Date(a.runStart).getTime());
      dispatch(setPipelineRuns(sortedRuns));
      setPipelineStats(calculatePipelineStats(sortedRuns));
    } catch (err) {
      dispatch(setError("Failed to fetch pipeline runs. Please try again later."));
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to fetch pipeline runs. Please try again later.",
          show: true,
          heading: "Error",
          errMessage: "",
          errDescription: "",
        })
      );
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    dispatch(setSelectedPipeline(null));
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      dispatch(setStartDate(date));
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      dispatch(setEndDate(date));
    }
  };

  const handleRefresh = () => {
    if (selectedPipeline) {
      fetchPipelineRuns(selectedPipeline);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setLocalSearchQuery(query);
    debouncedSearch(query);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: "overall" | "dashboard") => {
    setActiveTab(newValue);
  };

  const renderSkeletonLoading = () => (
    <Box sx={{ flexGrow: 1, p: 3, minHeight: "100vh" }}>
      <Skeleton variant="text" width={300} height={48} sx={{ mb: 4 }} />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 3 }} />
        </Grid>
        <Grid item xs={12} md={6} lg={3}>
          <Skeleton />
        </Grid>
        <Grid item xs={12} md={6} lg={3}>
          <Skeleton />
        </Grid>
        <Grid item xs={12}>
          <Card sx={{ borderRadius: 2 }}>
            <CardContent>
              <Skeleton variant="text" width={200} height={32} sx={{ mb: 3 }} />
              <Grid container spacing={2}>
                {[...Array(9)].map((_, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Skeleton />
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          <Skeleton variant="rectangular" width={300} height={36} sx={{ borderRadius: 4 }} />
        </Grid>
      </Grid>
    </Box>
  );

  if (loading) {
    return <ThemeProvider theme={theme}>{renderSkeletonLoading()}</ThemeProvider>;
  }

  if (error) {
    return (
      <Box sx={{ width: "100%", display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ flexGrow: 1, p: 3, minHeight: "100vh" }}>
        <Typography
          variant="h4"
          sx={{
            flexShrink: 0,
            whiteSpace: "nowrap",
            fontWeight: 500,
            letterSpacing: "0.05em",
            color: "primary.main",
            mb: 4,
          }}
        >
          Pipeline Dashboard
        </Typography>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="dashboard tabs" sx={{ mb: 4 }}>
          <Tab label="Pipeline Dashboard" value="dashboard" />
          <Tab label="Overall Report" value="overall" />
        </Tabs>
        {activeTab === "overall" && (
          <OverallReport
            isLoadingOverallReport={isLoadingOverallReport}
            overallStats={allPipelineStatuses.overallStats}
            pipelineSpecificStats={allPipelineStatuses.pipelineSpecificStats}
          />
        )}
        {activeTab === "dashboard" && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search pipelines..."
                value={localSearchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search color="primary" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  mb: 3,
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "24px",
                    backgroundColor: "background.paper",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <StyledCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Total Pipelines
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: "bold", color: "primary.main" }}>
                    {pipelineData?.totalPipelines || 0}
                  </Typography>
                </CardContent>
              </StyledCard>
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <StyledCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Current Page
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: "bold", color: "primary.main" }}>
                    {pipelineData?.page || 1} / {pipelineData?.totalPages || 1}
                  </Typography>
                </CardContent>
              </StyledCard>
            </Grid>
            <Grid item xs={12}>
              <StyledCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold", mb: 3 }}>
                    Pipelines
                  </Typography>
                  <Grid container spacing={2}>
                    {pipelineData?.pipelines.map(pipeline => (
                      <Grid item xs={12} sm={6} md={4} key={pipeline.id}>
                        <StyledCard onClick={() => handlePipelineClick(pipeline.name)} sx={{ cursor: "pointer" }}>
                          <CardContent>
                            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: "400" }}>
                              {pipeline.name}
                            </Typography>
                            <Typography variant="body2" color="text.primary" gutterBottom>
                              {pipeline.folder.name && <StyledChip label={pipeline.folder.name} color="primary" size="small" />}
                            </Typography>
                          </CardContent>
                        </StyledCard>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </StyledCard>
            </Grid>
            <Grid item xs={12} sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
              <Pagination
                count={pipelineData?.totalPages || 1}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </Grid>
          </Grid>
        )}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle sx={{ fontWeight: "bold", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Typography variant="h6">Pipeline Runs: {selectedPipeline}</Typography>
            <IconButton onClick={handleCloseDialog} size="small">
              <Close />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 3, display: "flex", gap: 2, flexWrap: "wrap" }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker label="Start Date" value={startDate} onChange={handleStartDateChange} renderInput={params => <TextField {...params} />} />
                <DateTimePicker label="End Date" value={endDate} onChange={handleEndDateChange} renderInput={params => <TextField {...params} />} />
              </LocalizationProvider>
              <StyledButton variant="contained" color="primary" onClick={handleRefresh} startIcon={<PlayArrow />}>
                Refresh
              </StyledButton>
            </Box>
            <PipelineStatsTab stats={pipelineStats} runs={pipelineRuns} />
            <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: "auto", mt: 2 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Run ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Start Time</TableCell>
                    <TableCell>End Time</TableCell>
                    <TableCell>Duration</TableCell>
                    <TableCell>Triggered By</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {pipelineRuns.map(run => (
                    <TableRow key={run.runId}>
                      <TableCell>{run.runId}</TableCell>
                      <TableCell>
                        <StatusChip
                          theme={theme}
                          label={run.status}
                          status={run.status}
                          icon={
                            run.status === "Succeeded" ? (
                              <Check color="inherit" sx={{ borderRadius: "50%" }} />
                            ) : run.status === "Failed" ? (
                              <Close color="inherit" sx={{ borderRadius: "50%" }} />
                            ) : (
                              <Schedule color="inherit" sx={{ borderRadius: "50%" }} />
                            )
                          }
                        />
                      </TableCell>
                      <TableCell>{new Date(run.runStart).toLocaleString()}</TableCell>
                      <TableCell>{new Date(run.runEnd).toLocaleString()}</TableCell>
                      <TableCell>
                        {run.durationInMs >= 60000
                          ? `${Math.floor(run.durationInMs / 60000)}m ${Math.floor((run.durationInMs % 60000) / 1000)}s`
                          : `${Math.floor(run.durationInMs / 1000)}s`}
                      </TableCell>
                      <TableCell>
                        <Tooltip title={`Type: ${run.invokedBy.invokedByType}`}>
                          <Typography variant="body2">{run.invokedBy.name}</Typography>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </DialogContent>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};
