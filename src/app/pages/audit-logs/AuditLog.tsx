import SearchIcon from "@mui/icons-material/Search";
import { Box, Grid, InputAdornment, TextField, Typography } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import debounce from "lodash/debounce";
import { FC, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  LogsFilterParam,
  setAuditLogs,
  setCurrentPage,
  setFilters,
  setLoading,
  setPage,
  setTotalCount,
  setTotalPages,
} from "../../../__redux/auditLogSlice";
import { fetchAuditLogs } from "../../api/auditLogApi";
import Footer from "../../CustomComponents/Footer";
import { StyledPaper } from "../../CustomComponents/StyledComponents";
import { RootState } from "../../store";
import AuditLogTable from "./components/AuditLogTable";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const AuditLog: FC<{ tablename: string }> = ({ tablename }) => {
  const dispatch = useDispatch();
  const { auditLogs, loading, pageSize, totalPages, currentPage, filters } = useSelector((state: RootState) => state.auditLog);

  const fetchAuditLogData = useCallback(
    async (pageSize: number, pageNumber: number, filters: LogsFilterParam) => {
      dispatch(setLoading(true));
      try {
        const data = await fetchAuditLogs(pageSize, pageNumber, filters);
        dispatch(setAuditLogs(data.result));
        dispatch(setTotalPages(data.totalPages));
        dispatch(setCurrentPage(data.currentPage));
        dispatch(setTotalCount(data.totalCount));
      } catch (error) {
        console.error("Error fetching Audit Logs:", error);
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  useEffect(() => {
    fetchAuditLogData(pageSize, currentPage, filters);
  }, [pageSize, currentPage, filters, fetchAuditLogData]);

  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    dispatch(setPage(value));
    dispatch(setCurrentPage(1));
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    dispatch(setCurrentPage(value));
  };

  const debouncedSearch = debounce((searchValue: string) => {
    dispatch(
      setFilters({
        searchTerm: searchValue || null,
      })
    );
    dispatch(setCurrentPage(1));
  }, 500);

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                {tablename}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search across all fields..."
                onChange={(e) => debouncedSearch(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>
        </StyledPaper>

        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <AuditLogTable
            auditLogs={auditLogs}
            loading={loading}
            limit={pageSize}
            // handleClickOpen={handleEditBtnClick}
          />
        </Box>
        <Footer
          limit={pageSize}
          totalPages={totalPages}
          page={currentPage}
          count={auditLogs.length}
          handleLimitChange={handleLimitChange}
          handlePageChange={handlePageChange}
        />
      </Box>
    </ThemeProvider>
  );
};

export default AuditLog;
