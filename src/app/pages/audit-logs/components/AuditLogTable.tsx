import { FC, useState } from "react";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Skeleton, Box, Typography, Tooltip, IconButton, Grid, Chip } from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { styled } from "@mui/material/styles";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import moment, { MomentInput } from "moment-timezone";
import { AuditLogObject } from "../../../../__redux/auditLogSlice";
// import { objectStringify } from "../../ai/components/utils";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import SyntaxHighlighter from "react-syntax-highlighter";
import { vs2015 } from "react-syntax-highlighter/dist/esm/styles/hljs";

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: "calc(100vh - 200px)",
  "&::-webkit-scrollbar": {
    width: "0.4em",
    height: "0.4em",
  },
  "&::-webkit-scrollbar-track": {
    boxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
    webkitBoxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
  },
  "&::-webkit-scrollbar-thumb": {
    backgroundColor: theme.palette.primary.main,
    opacity: 0.2,
    borderRadius: "10px",
    "&:hover": {
      backgroundColor: theme.palette.primary.dark,
    },
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  whiteSpace: "nowrap",
  padding: theme.spacing(2),
  fontSize: "1.1rem",
  borderBottom: `1px solid ${theme.palette.divider}`,
  "&.sql-cell": {
    maxWidth: "300px",
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
  },
  "& .MuiTableCell-root": {
    padding: theme.spacing(1.5),
    fontSize: "1rem",
  },
}));

interface AuditLogTableProps {
  auditLogs: AuditLogObject[];
  loading: boolean;
  limit: number;
  //   handleClickOpen: (store: PromotionObject) => void;
}

interface DetailDialogProps {
  open: boolean;
  onClose: () => void;
  log: AuditLogObject;
}

const DetailDialog: FC<DetailDialogProps> = ({ open, onClose, log }) => {
  console.log("log", log);
  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>Audit Log Details</DialogTitle>
      <DialogContent>
        {/* User Information Section */}
        <Box mb={4}>
          <Typography variant="h6" gutterBottom color="primary">
            User Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                Name
              </Typography>
              <Typography>{log.name || "-"}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                Email
              </Typography>
              <Typography>{log.email || "-"}</Typography>
            </Grid>
            {/* <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                User ID
              </Typography>
              <Typography>{log.userId || "-"}</Typography>
            </Grid> */}
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                Roles
              </Typography>
              <Typography>
                {Array.isArray(log.roles)
                  ? log.roles
                      .map(role =>
                        role === "Developer" ? (
                          <span
                            key={role}
                            style={{
                              color: "red",
                              backgroundColor: "rgba(255,0,0,0.1)",
                              padding: "2px 8px",
                              borderRadius: "12px",
                              border: "1px solid red",
                            }}
                          >
                            {role}
                          </span>
                        ) : role === "Admin" ? (
                          <span
                            key={role}
                            style={{
                              color: "blue",
                              backgroundColor: "rgba(0,0,255,0.1)",
                              padding: "2px 8px",
                              borderRadius: "12px",
                              border: "1px solid blue",
                            }}
                          >
                            {role}
                          </span>
                        ) : (
                          <span key={role}>{role}</span>
                        )
                      )
                      .reduce((prev, curr, i) => (i === 0 ? [curr] : [...prev, " ", curr]), [] as React.ReactNode[])
                  : "-"}
              </Typography>
            </Grid>
          </Grid>
        </Box>

        {/* Request Information Section */}
        <Box mb={4}>
          <Typography variant="h6" gutterBottom color="primary">
            Request Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                IP Address
              </Typography>
              <Typography>{log.ipAddress || "-"}</Typography>
            </Grid>
            {/* <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                Tenant ID
              </Typography>
              <Typography>{log.tenantId || "-"}</Typography>
            </Grid> */}
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                API Endpoint
              </Typography>
              <Typography sx={{ wordBreak: "break-all" }}>{log.apiEndpoint || "-"}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold">
                Timestamp
              </Typography>
              <Typography>{moment(log.timestamp).tz("America/Chicago").format("MM/DD/YYYY HH:mm:ss")}</Typography>
            </Grid>
          </Grid>
        </Box>

        {/* SQL Query Section */}
        <Box>
          <Typography variant="h6" gutterBottom color="primary">
            SQL Queries
          </Typography>
          {typeof log.sqlQuery === "string" ? (
            <Box mb={3}>
              <Typography variant="subtitle1" fontWeight="bold">
                Query
              </Typography>
              <SyntaxHighlighter language="sql" style={vs2015} customStyle={{ borderRadius: "4px" }}>
                {log.sqlQuery}
              </SyntaxHighlighter>
            </Box>
          ) : typeof log.sqlQuery === "object" && !Array.isArray(log.sqlQuery) && log.sqlQuery?.query ? (
            <Box mb={3}>
              <Typography variant="subtitle1" fontWeight="bold">
                Query
              </Typography>
              <SyntaxHighlighter language="sql" style={vs2015} customStyle={{ borderRadius: "4px" }}>
                {log.sqlQuery.query}
              </SyntaxHighlighter>

              {log.sqlQuery.action && (
                <Box mt={2} mb={1}>
                  <Chip
                    label={log.sqlQuery.action}
                    color={
                      log.sqlQuery.action === "UPDATE"
                        ? "primary"
                        : log.sqlQuery.action === "INSERT"
                        ? "success"
                        : log.sqlQuery.action === "DELETE"
                        ? "error"
                        : "default"
                    }
                  />
                  {log.sqlQuery.tableName && (
                    <Typography variant="body2" color="textSecondary" mt={1}>
                      Table: <strong>{log.sqlQuery.tableName}</strong>
                    </Typography>
                  )}
                </Box>
              )}

              {log.sqlQuery.params && (
                <>
                  <Typography variant="subtitle1" fontWeight="bold" mt={2}>
                    Parameters
                  </Typography>
                  <SyntaxHighlighter language="json" style={vs2015} customStyle={{ borderRadius: "4px" }}>
                    {JSON.stringify(log.sqlQuery.params, null, 2)}
                  </SyntaxHighlighter>
                </>
              )}

              {log.sqlQuery.primaryKey && (
                <Box mt={2}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Primary Key
                  </Typography>
                  <Typography>
                    {log.sqlQuery.primaryKey.column}: <strong>{log.sqlQuery.primaryKey.value}</strong>
                  </Typography>
                </Box>
              )}
            </Box>
          ) : (
            Array.isArray(log.sqlQuery) &&
            log.sqlQuery?.map((query, index) => (
              <Box key={index} mb={3}>
                <Typography variant="subtitle1" fontWeight="bold">
                  Query {index + 1}
                </Typography>
                <SyntaxHighlighter language="sql" style={vs2015} customStyle={{ borderRadius: "4px" }}>
                  {query.query}
                </SyntaxHighlighter>
                <Typography variant="subtitle1" fontWeight="bold">
                  Parameters
                </Typography>
                <SyntaxHighlighter language="json" style={vs2015} customStyle={{ borderRadius: "4px" }}>
                  {JSON.stringify(query.params, null, 2)}
                </SyntaxHighlighter>
              </Box>
            ))
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const AuditLogTable: FC<AuditLogTableProps> = ({ auditLogs, loading, limit }) => {
  const [selectedLog, setSelectedLog] = useState<AuditLogObject | null>(null);

  const formatValue = (value: unknown, field: string): React.ReactNode => {
    if (value === null || value === undefined) return "-";

    if (field === "timestamp") {
      const date = moment(value as MomentInput);
      return date.isValid() ? date.tz("America/Chicago").format("MM/DD/YYYY HH:mm:ss") : "-";
    }

    if (field === "roles" && Array.isArray(value)) {
      return value.map(role => (
        <Chip
          key={String(role)}
          label={String(role)}
          size="small"
          color={String(role) === "Developer" ? "error" : String(role) === "Admin" ? "primary" : "default"}
          sx={{ mr: 0.5 }}
        />
      ));
    }

    if (field === "apiEndpoint") {
      return (
        <Tooltip title={String(value)}>
          <Typography
            sx={{
              maxWidth: "200px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {String(value)}
          </Typography>
        </Tooltip>
      );
    }

    return String(value);
  };

  const headers = ["Actions", "Name", "Roles", "Email", "API Endpoint", "Timestamp"];

  const headersAPIKey = ["name", "roles", "email", "apiEndpoint", "timestamp"];

  const renderTableContent = () => {
    if (loading) {
      return (
        <TableBody>
          {[...Array(limit)].map((_, index) => (
            <StyledTableRow key={index}>
              {headers.map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton animation="wave" />
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }

    if (auditLogs.length === 0) {
      return (
        <TableBody>
          <StyledTableRow>
            <TableCell colSpan={headers.length} align="center">
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <ErrorOutlineIcon color="action" style={{ fontSize: 60, marginBottom: 16 }} />
                <Typography variant="h6" color="textSecondary">
                  No Audit Logs data found
                </Typography>
              </Box>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {auditLogs.map(log => (
          <StyledTableRow key={log.Id}>
            <TableCell>
              <Tooltip title="View Details">
                <IconButton color="primary" size="small" onClick={() => handleClickOpen(log)}>
                  <VisibilityIcon />
                </IconButton>
              </Tooltip>
            </TableCell>
            {headersAPIKey.map(field => (
              <TableCell
                key={field}
                sx={{
                  maxWidth: field === "apiEndpoint" ? "200px" : "auto",
                  minWidth: field === "roles" ? "150px" : "auto",
                }}
              >
                {formatValue(log[field as keyof AuditLogObject], field)}
              </TableCell>
            ))}
          </StyledTableRow>
        ))}
      </TableBody>
    );
  };

  const handleClickOpen = (log: AuditLogObject) => {
    setSelectedLog(log);
  };

  const handleClose = () => {
    setSelectedLog(null);
  };

  return (
    <>
      <StyledTableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {headers.map(header => (
                <StyledTableCell key={header}>
                  <span>{header}</span>
                </StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          {renderTableContent()}
        </Table>
      </StyledTableContainer>
      {selectedLog && <DetailDialog open={Boolean(selectedLog)} onClose={handleClose} log={selectedLog} />}
    </>
  );
};

export default AuditLogTable;
