import {
  Box,
  Grid,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  List,
  ListItem,
  IconButton,
  CircularProgress,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import AddIcon from "@mui/icons-material/Add";
import EditCalendarIcon from "@mui/icons-material/EditCalendar";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { FC, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setFiscalCalendars,
  setLimit,
  setLoading,
  setPage,
  setShowFilters,
  setTotalPages,
  setConfig,
  setFilters,
  setIsAddMode,
  setOpen,
  setFormData,
  setTabData,
} from "../../../__redux/fiscalCalendarSlice";
import {
  fetchFiscalCalendar,
  fetchFiscalCalendarConfigData,
  UpdateCalendarRecordParams,
  updateCalendarRecord,
  addCalendarRecord,
  bulkUploadCalendarRecord,
} from "../../api/fiscalCalendarApi";
import Footer from "../../CustomComponents/Footer";
import { StyledPaper, StyledButton } from "../../CustomComponents/StyledComponents";
import { RootState } from "../../store";
import FiscalCalendarTable from "./components/FiscalCalendarTable";
import { FiscalCalendar, FiscalCalendarConfig, FiscalCalendarFilterParam } from "../../Types/fiscal-calendar-types";
import FiscalCalendarFilter from "./components/FiscalCalendarFilter";
import EditCalendarTab from "./components/edit-calendar/EditCalendarTab";
import { addSpaceToCamelCase } from "../ai/components/utils";
import { pushNewAlert, setFullScreenLoader } from "../../../__redux/generalSlice";
import { setNewCalendar, initialAddCalendarData } from "../../../__redux/addCalendarSlice";
import AddCalendarTab from "./components/add-calendar/AddCalendarTab";
import { convertToDate, formatDateToString } from "../ai/components/utils";
import BulkUploadCalendarTab from "./components/add-calendar/BulkUploadCalanderTab";
import axios from "axios";
import { ROLES } from "../../constants/constants";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const FiscalCalendarPage: FC<{ tablename: string }> = ({ tablename }) => {
  const dispatch = useDispatch();
  const { fiscalCalendars, loading, page, totalPages, limit, config, filters, showFilters, formData, tabData } = useSelector(
    (state: RootState) => state.fiscalCalendar
  );
  const { newCalendar } = useSelector((state: RootState) => state.addCalendar);
  const { userRole } = useSelector((state: RootState) => state.auth);

  const [openEditCalendar, setOpenEditCalendar] = useState<boolean>(false);
  const [openAddCalendar, setOpenAddCalendar] = useState<boolean>(false);
  const [openAddManualCalendar, setOpenAddManualCalendar] = useState<boolean>(false);
  const [openBulkUploadCalendar, setOpenBulkUploadCalendar] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveUpdateConfirmOpen, setSaveUpdateConfirmOpen] = useState<boolean>(false);
  const [saveAddConfirmOpen, setSaveAddConfirmOpen] = useState<boolean>(false);

  const fetchFiscalCalendarData = useCallback(
    async (page: number, limit: number, filter: FiscalCalendarFilterParam) => {
      dispatch(setLoading(true));
      try {
        const data = await fetchFiscalCalendar(page, limit, filter);
        const formattedData = data.result.map((item: FiscalCalendar) => ({
          ...item,
          isPayPeriodEnd: item.isPayPeriodEnd !== null ? item.isPayPeriodEnd : false,
          isPayPeriodStart: item.isPayPeriodStart !== null ? item.isPayPeriodStart : false,
        }));
        dispatch(setFiscalCalendars(formattedData));
        dispatch(setTotalPages(data.totalPages));
        dispatch(setLoading(false));
      } catch (error) {
        console.error("Error fetching fiscal calendar:", error);
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  useEffect(() => {
    fetchFiscalCalendarData(page, limit, filters);
  }, [page, limit, fetchFiscalCalendarData, filters]);

  // Fetch Filter Config Data
  const fetchConfigData = async () => {
    try {
      const config = await fetchFiscalCalendarConfigData();
      dispatch(setConfig(config));
    } catch (error) {
      console.error("Error fetching config:", error);
    }
  };
  useEffect(() => {
    fetchConfigData();
  }, []);

  const updateExistingRecord = async () => {
    if (Object.keys(tabData).length === 0) {
      dispatch(
        pushNewAlert({
          type: "success",
          message: "No value is updated",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
      return;
    }
    const changedFields = Object.entries(tabData).map(([key, value]) => {
      return { field: key, value: value };
    });

    const objToSave = {
      fiscalCalendarId: formData?.fiscalCalendarId ?? 1,
      filters: changedFields,
    };

    // Call Update API
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      const response = await updateCalendarRecord(objToSave);
      setSaveUpdateConfirmOpen(false);
      setOpenEditCalendar(false);
      dispatch(setTabData({}));

      // Call Fetch Calendar List API
      await fetchFiscalCalendarData(page, limit, filters);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "Calendar updated successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving  changes:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to update store",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while saving changes",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  const addManualNewRecord = async () => {
    let currentDate = convertToDate(newCalendar["calendarDate"])!;
    const params = { ...newCalendar };
    const dayName = currentDate.toLocaleDateString("en-US", { weekday: "long" });
    const dayOfWeek = currentDate.getDay() + 1; // 1 is added as the day index is started from 0
    params["dayName"] = dayName;
    params["dayOfWeek"] = dayOfWeek;
    params["createDate"] = formatDateToString(new Date(), "YYYY-MM-DD");
    params["calendarDate"] = formatDateToString(params["calendarDate"], "YYYY-MM-DD");
    params["previousYearDate"] = formatDateToString(params["previousYearDate"], "YYYY-MM-DD");

    // Call Manual Add API
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      const response = await addCalendarRecord(params);
      setSaveAddConfirmOpen(false);
      setOpenAddManualCalendar(false);
      dispatch(setNewCalendar(initialAddCalendarData));

      // Call Fetch Calendar List API
      await fetchConfigData();
      await fetchFiscalCalendarData(page, limit, filters);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "New record added successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving  changes:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to add new record",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while saving changes",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  const addBulkUploadFile = async (file: File) => {
    // Call Upload file  API
    dispatch(setFullScreenLoader(true));

    try {
      const response = await bulkUploadCalendarRecord(file);
      setOpenBulkUploadCalendar(false);

      // Call Fetch Calendar List API
      await fetchConfigData();
      await fetchFiscalCalendarData(page, limit, filters);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "New record added successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving  changes:", error);
      let errMsg: string | null = null;
      if (axios.isAxiosError(error)) {
        errMsg = error.response?.data?.error;
      }
      window.alert(`An error occurred while uploading the file\n${errMsg || "Unknown error"}`);
      // dispatch(
      //   pushNewAlert({
      //     type: "error",
      //     message: "Failed to add new record",
      //     show: true,
      //     heading: "Error",
      //     errMessage: "An error occurred while uploading the file",
      //     errDescription: errMsg || "Unknown error",
      //   })
      // );
    } finally {
      dispatch(setFullScreenLoader(false));
    }
  };

  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    dispatch(setLimit(value === 100 ? 1000 : value));
    dispatch(setPage(1));
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    dispatch(setPage(value));
  };

  const handleToggleFilters = () => {
    dispatch(setShowFilters(!showFilters));
  };

  const handleResetFilter = () => {
    const emptyFilter: FiscalCalendarFilterParam = {
      fiscalPeriodNumber: null,
      fiscalWeekNumber: null,
      fiscalQuarterNumber: null,
      fiscalYear: null,
    };
    dispatch(setFilters(emptyFilter));
    dispatch(setShowFilters(false));
  };

  const handleApplyFilter = (params: FiscalCalendarFilterParam) => {
    dispatch(setFilters(params));
    dispatch(setShowFilters(false));
  };

  // View Available Date section
  const handleViewDates = () => {
    const newTab = window.open("/available-date", "_blank");
  };

  //  Edit Record section
  const handleEditBtnClick = (calendar: FiscalCalendar) => {
    dispatch(setFormData(calendar));
    setOpenEditCalendar(true);
  };

  const handleCloseEditCalendarDialog = () => {
    dispatch(setTabData({}));
    setOpenEditCalendar(false);
  };

  const handleEditSaveChanges = () => {
    setSaveUpdateConfirmOpen(true);
  };

  const handleConfirmUpdateSave = async () => {
    await updateExistingRecord();
  };

  // Add Record Section
  const handleAddNewCalendar = () => {
    setOpenAddCalendar(true);
  };

  const handleCloseAddCalendarDialog = () => {
    setOpenAddCalendar(false);
  };

  const handleManualRecordEntry = () => {
    dispatch(setNewCalendar({ ...initialAddCalendarData }));
    setOpenAddManualCalendar(true);
    setOpenAddCalendar(false);
  };

  const handleCloseManualAddCalendarDialog = () => {
    setOpenAddManualCalendar(false);
  };

  const handleManualAddSaveChanges = () => {
    setSaveAddConfirmOpen(true);
  };

  const handleBulkUploadRecord = () => {
    setOpenBulkUploadCalendar(true);
    setOpenAddCalendar(false);
  };

  const handleCloseBulUploadCalendarDialog = () => {
    setOpenBulkUploadCalendar(false);
  };

  const handleSaveBulkUploadRecord = async (file: File) => {
    await addBulkUploadFile(file);
  };

  const handleConfirmManualAddSave = async () => {
    await addManualNewRecord();
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                {tablename}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                <StyledButton variant="outlined" color="primary" onClick={handleToggleFilters} startIcon={<FilterListIcon />}>
                  {showFilters ? "Hide Filters" : "Show Filters"}
                </StyledButton>
                <StyledButton variant="contained" color="warning" onClick={handleViewDates} startIcon={<CalendarMonthIcon />}>
                  View Available Dates
                </StyledButton>
                {userRole?.includes(ROLES.ADMIN) ||
                  (userRole?.includes(ROLES.DEVELOPER) && (
                    <StyledButton variant="contained" color="primary" onClick={handleAddNewCalendar} startIcon={<AddIcon />}>
                      Add New Record
                    </StyledButton>
                  ))}
              </Box>
            </Grid>
          </Grid>

          <FiscalCalendarFilter
            showFilters={showFilters}
            config={config}
            filter={filters}
            resetFilterCallBack={handleResetFilter}
            applyFilterCallback={handleApplyFilter}
          />
        </StyledPaper>
        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <FiscalCalendarTable fiscalCalendars={fiscalCalendars} loading={loading} limit={limit} handleClickOpen={handleEditBtnClick} />
        </Box>
        <Footer
          limit={limit}
          totalPages={totalPages}
          page={page}
          count={fiscalCalendars.length}
          handleLimitChange={handleLimitChange}
          handlePageChange={handlePageChange}
        />
      </Box>

      {/* BEGIN:: Update Calendar Form Dialog Box */}
      <Dialog open={openEditCalendar} onClose={handleCloseEditCalendarDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Update Calendar</DialogTitle>
        <DialogContent dividers>
          <EditCalendarTab calendarData={formData!} onSaveBtnCallback={handleEditSaveChanges} onCancelBtnCallback={handleCloseEditCalendarDialog} />
        </DialogContent>
      </Dialog>
      {/* END:: Update Calendar Form Dialog Box */}

      {/* BEGIN:: Add Calendar Form Dialog Box */}
      <Dialog open={openAddManualCalendar} onClose={handleCloseManualAddCalendarDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Add Calendar</DialogTitle>
        <DialogContent dividers>
          <EditCalendarTab
            calendarData={formData!}
            onSaveBtnCallback={handleEditSaveChanges}
            onCancelBtnCallback={handleCloseManualAddCalendarDialog}
          />
        </DialogContent>
      </Dialog>
      {/* END:: Add Calendar Form Dialog Box */}
      <Dialog open={openAddCalendar} onClose={handleCloseAddCalendarDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Calendar Record</DialogTitle>
        <DialogContent dividers>
          <Box display="flex" justifyContent="center" alignItems="center" height="100px">
            <Button
              onClick={handleManualRecordEntry}
              variant="contained"
              color="warning"
              size="large"
              startIcon={<EditCalendarIcon />}
              sx={{ borderRadius: 10, mr: 2 }}
            >
              Manual Entry
            </Button>
            <Button
              onClick={handleBulkUploadRecord}
              variant="contained"
              color="success"
              size="large"
              startIcon={<UploadFileIcon />}
              sx={{ borderRadius: 10, mr: 2 }}
            >
              Bulk Upload
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
      {/* BEGIN:: Manually Add Calendar Form Dialog Box */}
      <Dialog open={openAddManualCalendar} onClose={handleCloseManualAddCalendarDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Add New Calendar Record</DialogTitle>
        <DialogContent dividers>
          <AddCalendarTab onSaveBtnCallback={handleManualAddSaveChanges} onCancelBtnCallback={handleCloseManualAddCalendarDialog} />
        </DialogContent>
      </Dialog>
      {/* END:: Manually Add Calendar Form Dialog Box */}

      {/* BEGIN:: Bulk Upload Calendar Form Dialog Box */}
      <Dialog open={openBulkUploadCalendar} onClose={handleCloseBulUploadCalendarDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Bulk Upload Calendar Record</DialogTitle>
        <DialogContent dividers>
          <BulkUploadCalendarTab onSaveBtnCallback={handleSaveBulkUploadRecord} onCancelBtnCallback={handleCloseBulUploadCalendarDialog} />
        </DialogContent>
      </Dialog>
      {/* END:: Bulk Upload Calendar Form Dialog Box */}

      {/* BEGIN:: Save Edit Calendar Dialog Box */}
      <Dialog open={saveUpdateConfirmOpen} onClose={() => setSaveUpdateConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Confirm Changes</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to make changes to the following fields
          </Typography>
          {tabData !== undefined && (
            <Box
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-start",
                // gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
                gap: 10,
              }}
            >
              {Object.entries(tabData).map(([columnName]) => (
                <Box
                  key={columnName}
                  sx={{
                    padding: 1,
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    border: 1,
                    borderColor: "grey",
                    borderRadius: "8px",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      transform: "translateY(-2px)",
                      boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: "0.9rem",
                      letterSpacing: "0.5px",
                      color: "primary.light",
                    }}
                  >
                    {addSpaceToCamelCase(columnName)}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveUpdateConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpdateSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* End:: Save Edit Calendar Dialog Box */}

      {/* BEGIN:: Save Add Manual Calendar Dialog Box */}
      <Dialog open={saveAddConfirmOpen} onClose={() => setSaveAddConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Record</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to add new calendar record
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveAddConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmManualAddSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* End:: Save Add Manual Calendar Dialog Box */}
    </ThemeProvider>
  );
};

export default FiscalCalendarPage;
