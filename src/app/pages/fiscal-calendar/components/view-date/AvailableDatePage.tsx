import React, { useState, useEffect, useCallback, ChangeEvent } from "react";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Box, Typography, Grid, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { setFullScreenLoader } from "../../../../../__redux/generalSlice";
import { fetchMissingDates } from "../../../../api/fiscalCalendarApi";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { StyledButton } from "../../../../CustomComponents/StyledComponents";
import SearchIcon from "@mui/icons-material/Search";
import { formatDateToString } from "../../../ai/components/utils";

type DateResponse = { Date: string };

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const AvailableDatePage = () => {
  const dispatch = useDispatch();

  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  //   const [loading, setLoading] = useState<boolean>(false);
  const [shouldCallAPI, setShouldCallAPI] = useState<boolean>(false);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [noDataMsg, setNoDataMsg] = useState<string>("Please select a date range");

  // Fetch Table Data
  const fetchTableData = useCallback(
    async (startDate: Date, endDate: Date) => {
      dispatch(setFullScreenLoader(true));
      try {
        const sd = formatDateToString(startDate, "YYYY-MM-DD");
        const ed = formatDateToString(endDate, "YYYY-MM-DD");
        const response: DateResponse[] = await fetchMissingDates(sd, ed);
        const data = response.map((item) => item.Date);
        if (data.length == 0) setNoDataMsg("No data found for the given date range");
        setAvailableDates(data);
      } catch (error) {
        console.error("Error fetching stores:", error);
      } finally {
        setShouldCallAPI(false);
        dispatch(setFullScreenLoader(false));
      }
    },
    [shouldCallAPI]
  );
  useEffect(() => {
    if (!shouldCallAPI) {
      return;
    }
    fetchTableData(startDate!, endDate!);
  }, [fetchTableData]);

  const handleSearch = async () => {
    if (startDate === null || endDate === null) {
      window.alert("Please select start date and end date");
      return;
    }
    if (startDate >= endDate) {
      window.alert("Start date should be less than end date");
      return;
    }
    setShouldCallAPI(true);
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "visible" }}>
        {/* Header Section Start */}
        <div
          className="d-flex justify-content-start align-items-center flex-wrap"
          style={{
            width: "100%",
            marginTop: 10,
            marginBottom: 10,
            marginLeft: 20,
            marginRight: 20,
          }}
        >
          <Typography variant="h5" gutterBottom>
            Start date
          </Typography>
          <div style={{ paddingLeft: 10, paddingRight: 20 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                // renderInput={(params) => <TextField {...params} fullWidth />}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          </div>
          <Typography variant="h5" gutterBottom>
            End date
          </Typography>
          <div style={{ paddingLeft: 10, paddingRight: 20 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                // renderInput={(params) => <TextField {...params} fullWidth />}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          </div>
          <StyledButton
            variant="contained"
            color="primary"
            onClick={handleSearch}
            startIcon={<SearchIcon />}
            sx={{
              padding: "8px 3%",
              marginLeft: "8px",
              // width: 200
            }}
          >
            Search
          </StyledButton>
        </div>
        <Grid container spacing={2} alignItems="center" style={{ margin: 10 }}>
          {/* Top Border */}
          <div className="bg-secondary" style={{ width: "100%", height: 3, marginTop: 10, marginBottom: 10 }}></div>

          {/* Header Section End */}

          {/* Search Bar */}
          {availableDates.length > 0 && (
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                Available Dates
              </Typography>
            </Grid>
          )}
        </Grid>
        {availableDates.length > 0 && (
          <>
            <Box
              sx={{
                m: 2,
                display: "flex",
                justifyContent: "flex-start",
                flexWrap: "wrap",
                gap: 2,
              }}
            >
              {availableDates.map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    padding: 1,
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    border: 1,
                    borderColor: "grey",
                    borderRadius: "8px",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      transform: "translateY(-2px)",
                      boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: "0.9rem",
                      letterSpacing: "0.5px",
                      color: "primary.light",
                    }}
                  >
                    {formatDateToString(item, "MM/DD/YYYY", "GMT")}
                  </Typography>
                </Box>
              ))}
            </Box>
          </>
        )}
        {availableDates.length == 0 && (
          <Box display="flex" flexDirection="column" alignItems="center" py={4}>
            <Typography variant="h6" color="textSecondary">
              {noDataMsg}
            </Typography>
          </Box>
        )}
      </Box>
    </ThemeProvider>
  );
};

export default AvailableDatePage;
