import React, { useState } from 'react';
import axios from 'axios';
import { styled } from '@mui/material/styles';
import {Button, Typography, Box} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

interface AddCalendarProps {
  onSaveBtnCallback: ((file: File) => void);
  onCancelBtnCallback: (() => void);
}

const BulkUploadCalendarTab: React.FC<AddCalendarProps> = ({onSaveBtnCallback, onCancelBtnCallback,} ) => {
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    setFile(selectedFile || null);
  };

  const handleUpload = async () => {
    if (file) {
      onSaveBtnCallback(file);
    }
  };

  return (
    <div>
        <Typography variant="h6" color="info.primary">
          Upload an excel file(supported extensions xls,.xlsx)
        </Typography>
        <Typography variant="h6" color="info.primary">
          Please only use the following column names. No duplicate values are allowed in "fiscalCalendarId"	"calendarDate"	"previousYearDate"
        </Typography>
        <Typography>
          fiscalCalendarId,	calendarDate,	previousYearDate,	dayName,	dayOfWeek,	fiscalPeriodNumber,	fiscalWeekNumber,	fiscalQuarterNumber,	fiscalYear,	isPayPeriodEnd,	isPayPeriodStartHoliday
        </Typography>
        <Box
        sx={{
          marginTop: 2,
          marginBottom: 2
        }}
        >
          <input type="file" accept=".xls,.xlsx" onChange={handleFileChange} />
          <Button 
            onClick={handleUpload} 
            color="success" 
            variant="contained" 
            startIcon={<CloudUploadIcon />} 
            disabled={!file} 
            sx={{ borderRadius: 20, ml: 2 }}>
              Upload File
          </Button>
        </Box>
        
        <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          p: ``,
          borderTop: 2,
          borderColor: "divider",
      }}
        >
          <Button onClick={onCancelBtnCallback} sx={{ mr: 1 }}>
            Cancel
          </Button>
        </Box>
    </div>
  );
};

export default BulkUploadCalendarTab;


