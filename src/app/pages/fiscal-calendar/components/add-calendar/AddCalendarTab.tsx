import {
  Box,
  Button,
  CircularProgress,
  Fade,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Step,
  StepButton,
  Stepper,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import SaveIcon from "@mui/icons-material/Save";
import InfoIcon from "@mui/icons-material/Info";
import { setNewCalendar } from "../../../../../__redux/addCalendarSlice";
import { addSpaceToCamelCase, convertToDate, formatDateToString } from "../../../ai/components/utils";
import { AddStoreFormFieldType, FormObjectValidation } from "../../../store-logistic-manager/Components/AddStoreHelper";
import { checkExistingValueInStore, StoreExistingValueResponse } from "../../../../api/storeLogisticManagerApi";
import debounce from "lodash/debounce";
import moment, { MomentInput } from "moment-timezone";

type FieldRule = { rule: Record<string, FormObjectValidation> };

interface AddCalendarProps {
  onSaveBtnCallback: () => void;
  onCancelBtnCallback: () => void;
}

interface TabData {
  [key: string]:
    | string
    | number
    | boolean
    | null
    | Date
    | {
        latitude: number;
        longitude: number;
      };
}

export const formatTableName = (tableName: string): string => {
  return tableName
    .split("_")
    .map(word => {
      if (word.toLowerCase() === "str") {
        return "STR";
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

const PRIMARY_KEYS = ["fiscalCalendarId", "calendarDate", "previousYearDate", "dayName", "dayOfWeek"];

const FormObjectRule: Record<string, FormObjectValidation> = {
  // TABLE:: str_store
  fiscalCalendarId: { helperText: "", maxInputLimit: 11, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  calendarDate: { helperText: "", maxInputLimit: 10, isPrimaryKey: true, fieldType: AddStoreFormFieldType.DATE },
  previousYearDate: { helperText: "", maxInputLimit: 10, isPrimaryKey: true, fieldType: AddStoreFormFieldType.DATE },
  // "dayName": {helperText: "", maxInputLimit: 15, isPrimaryKey: true, fieldType: AddStoreFormFieldType.TEXT},
  // "dayOfWeek": {helperText: "", maxInputLimit: 1, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC},
  fiscalPeriodNumber: { helperText: "", maxInputLimit: 11, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  fiscalWeekNumber: { helperText: "", maxInputLimit: 11, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  fiscalQuarterNumber: { helperText: "", maxInputLimit: 11, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  fiscalYear: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  isPayPeriodStart: { helperText: "", maxInputLimit: 10, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  isPayPeriodEnd: { helperText: "", maxInputLimit: 10, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  Holiday: { helperText: "", maxInputLimit: 10, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
};

const AddCalendarTab: React.FC<AddCalendarProps> = ({ onSaveBtnCallback, onCancelBtnCallback }) => {
  const dispatch = useDispatch();

  const { newCalendar } = useSelector((state: RootState) => state.addCalendar);
  const theme = useTheme();
  const [editedData, setEditedData] = useState<TabData>({});
  const [fieldRules, setFieldRules] = useState<FieldRule>({ rule: JSON.parse(JSON.stringify(FormObjectRule)) });
  const [isLoading, setIsLoading] = useState(false);
  const [showSaveMessage, setShowSaveMessage] = useState(false);
  const [isDebouncing, setDebouncing] = useState<boolean>(false);

  useEffect(() => {
    setFieldRules({ rule: JSON.parse(JSON.stringify(FormObjectRule)) });
    setEditedData(newCalendar);
  }, [newCalendar]);

  const callExistingValueCheckAPI = async (key: string, value: any, tableName: string) => {
    if (value === null || isDebouncing) return;
    try {
      setDebouncing(true);
      const val = String(value);
      const response = await checkExistingValueInStore(tableName, key, val, "dbo");
      return response;
    } catch (error) {
      console.error("Error in calling existing value check api", error);
    } finally {
      setDebouncing(false);
    }
  };

  const validateInputOnSave = () => {
    const columnKeys = Object.keys(editedData);
    for (const key of columnKeys) {
      if (fieldRules.rule[key].helperText !== "") {
        window.alert("Please check all the fields, there are some wrong inputs");
        return;
      }
    }
    for (const key of columnKeys) {
      if (editedData[key] === null || editedData[key] === undefined || editedData[key] === "") {
        return "Please fill up all the fields";
      }
    }
    const endDate = convertToDate(editedData["calendarDate"]);
    const startDate = convertToDate(editedData["previousYearDate"]);
    if (endDate && startDate) {
      if (endDate < startDate) return "Previous Year Date should be always smaller than Calendar Date";
    }
    return null;
  };

  const handleSave = () => {
    console.log("saved editedData", editedData);
    const errorStr = validateInputOnSave();
    if (errorStr) {
      window.alert(errorStr);
      return;
    }
    const tempData = { ...editedData };
    tempData["calendarDate"] = formatDateToString(tempData["calendarDate"]);
    tempData["previousYearDate"] = formatDateToString(tempData["previousYearDate"]);
    dispatch(setNewCalendar(tempData));
    onSaveBtnCallback();
  };

  const handleInputChange = (key: string, value: string | number | boolean | Date | null) => {
    if (validateInputMaxLimit(key, value)) {
      setEditedData(prev => ({ ...prev, [key]: value }));
    }
    // Check existing value on the server only for primary key
    handleDebounceValueChecking(key, value);
  };

  const validateInputMaxLimit = (key: string, value: string | number | boolean | Date | null): boolean => {
    const rule = { ...fieldRules.rule[key] };

    switch (rule.fieldType) {
      case AddStoreFormFieldType.NUMERIC: {
        const isValid = Number(value).toString().length <= rule.maxInputLimit;
        return isValid;
      }
      case AddStoreFormFieldType.TEXT: {
        const isValid = String(value).length <= rule.maxInputLimit;
        return isValid;
      }
      default:
        break;
    }
    return true;
  };

  const validateExistingValue = async (key: string, val: string | number | boolean | Date | null) => {
    const rule = { ...fieldRules.rule[key] };
    if (!rule.isPrimaryKey) return;
    const currentTableName = "act_fiscalCalendar1";
    let value = val;
    if (val instanceof Date) {
      const date = moment(value as MomentInput);
      if (date.isValid()) {
        value = date.tz("America/Chicago").format("YYYY-MM-DD");
      }
    }
    const result = await callExistingValueCheckAPI(key, value, currentTableName);
    if (result) {
      const tempRules = { ...fieldRules };
      rule.helperText = result.exists ? "Value already exits" : "";
      tempRules.rule[key] = rule;
      setFieldRules(tempRules);
    } else if (value === null) {
      const tempRules = { ...fieldRules };
      rule.helperText = "";
      tempRules.rule[key] = rule;
      setFieldRules(tempRules);
    }
  };

  const handleDebounceValueChecking = useCallback(
    debounce(async (key: string, value: string | number | boolean | Date | null) => {
      await validateExistingValue(key, value);
    }, 400),
    []
  );

  const renderFormInputField = (key: string, value: any) => {
    const rule = fieldRules.rule[key];
    // Common props for all inputs
    const commonProps = {
      label: addSpaceToCamelCase(key),
      error: rule.helperText !== "",
      helperText: rule.helperText,
      fullWidth: true,
    };

    // Create UI
    switch (rule.fieldType) {
      case AddStoreFormFieldType.YES_NO:
        return (
          <FormControl fullWidth>
            <InputLabel id={`${key}-label`}>{addSpaceToCamelCase(key)}</InputLabel>
            <Select
              labelId={`${key}-label`}
              id={key}
              value={value === null ? null : value}
              onChange={e => handleInputChange(key, e.target.value ? e.target.value === "true" : null)}
              label={addSpaceToCamelCase(key)}
            >
              <MenuItem value="true">True</MenuItem>
              <MenuItem value="false">False</MenuItem>
            </Select>
          </FormControl>
        );

      case AddStoreFormFieldType.DATE:
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={addSpaceToCamelCase(key)}
              value={value ? new Date(value) : null}
              onChange={v => {
                handleInputChange(key, v);
              }}
              // renderInput={(params) => <TextField {...commonProps} {...params} fullWidth />}
              renderInput={params => (
                <TextField
                  {...commonProps}
                  {...params}
                  fullWidth
                  inputProps={{
                    ...params.inputProps,
                    readOnly: true,
                  }}
                />
              )}
            />
          </LocalizationProvider>
        );

      case AddStoreFormFieldType.NUMERIC:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={e => handleInputChange(key, parseInt(e.target.value) || null)}
            type="number"
            sx={{
              "& .MuiInputLabel-root.Mui-error": {
                color: "#ff8804",
              },
              "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
                border: "3px solid #ff8804",
              },
              "& .MuiFormHelperText-root.Mui-error": {
                color: "#ff8804",
              },
            }}
          />
        );

      case AddStoreFormFieldType.TEXT:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={e => handleInputChange(key, e.target.value)}
            sx={{
              "& .MuiInputLabel-root.Mui-error": {
                color: "#ff8804",
              },
              "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
                border: "3px solid #ff8804",
              },
              "& .MuiFormHelperText-root.Mui-error": {
                color: "#ff8804",
              },
            }}
          />
        );

      case AddStoreFormFieldType.EMAIL:
        return <TextField {...commonProps} value={value || undefined} onChange={e => handleInputChange(key, e.target.value)} type="email" />;
    }
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <CircularProgress color="primary" />
        </Box>
      );
    }

    if (!editedData || Object.keys(editedData).length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <Typography variant="body1">No data available</Typography>
        </Box>
      );
    }

    return (
      <Fade in={true} timeout={500}>
        <Box>
          <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
            <InfoIcon sx={{ mr: 1, color: "info.main" }} />
            <Typography variant="body2" color="info.main">
              Make changes and click Save to update the data, if you make a change and cancel or close the window, the changes will be lost.
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {Object.entries(editedData).map(([key, value]) => {
              return (
                <Grid item xs={12} sm={6} md={4} key={key}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                      transition: "all 0.3s",
                      borderRadius: 2,
                      border: "1px solid",
                      borderColor: "divider",
                      "&:hover": {
                        boxShadow: theme.shadows[4],
                        borderColor: "primary.main",
                      },
                    }}
                  >
                    {renderFormInputField(key, value)}
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      </Fade>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        // bgcolor: "background.paper",
        borderRadius: 2,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        height: "100%",
        // boxShadow: theme.shadows[4],
      }}
    >
      <Box sx={{ p: 3, flexGrow: 1, overflowY: "auto", maxHeight: "50vh" }}>{renderTabContent()}</Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
        }}
      >
        {/* <Button onClick={handleBack} disabled={currentTab === 0} variant="outlined" sx={{ borderRadius: 20 }}>
                Back
            </Button> */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button onClick={onCancelBtnCallback} sx={{ mr: 1 }}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            color="success"
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={Object.keys(editedData).length === 0}
            sx={{ borderRadius: 20, mr: 2 }}
          >
            Save
          </Button>
          {/* <Button onClick={handleNext} disabled={currentTab === allowedTables.length - 1} variant="contained" sx={{ borderRadius: 20 }}>
                {currentTab === allowedTables.length - 1 ? "Finish" : "Next"}
                </Button> */}
        </Box>
      </Box>
    </Box>
  );
};

export default AddCalendarTab;
