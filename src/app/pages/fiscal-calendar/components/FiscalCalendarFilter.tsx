import React, { useEffect, useState } from "react";
import { Autocomplete, Box, Button, Chip, Collapse, Grid, TextField, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import { FiscalCalendarConfig, FiscalCalendarFilterParam } from "../../../Types/fiscal-calendar-types";
import { StyledButton, StyledTextField } from "../../../CustomComponents/StyledComponents";




interface FiscalCalendarFilterProps {
  showFilters: boolean;
  config: FiscalCalendarConfig;
  filter: FiscalCalendarFilterParam;
  resetFilterCallBack: () => void;
  applyFilterCallback: (params: FiscalCalendarFilterParam) => void;
}
const FiscalCalendarFilter: React.FC<FiscalCalendarFilterProps> = ({
  showFilters,
  config,
  filter,
  resetFilterCallBack,
  applyFilterCallback,
}) => {

const [selectedYear, setSelectedYear] = useState<string | null>(filter.fiscalYear);
const [selectedPeriodNumber, setSelectedPeriodNumber] = useState<string | null>(filter.fiscalPeriodNumber);
const [selectedWeekNumber, setSelectedWeekNumber] = useState<string | null>(filter.fiscalWeekNumber);
const [selectedQuarterNumber, setSelectedQuarterNumber] = useState<string | null>(filter.fiscalQuarterNumber);

  const renderFilterChips = () => {
    let activeFilters: string[] = []
    if (filter.fiscalYear !== null) activeFilters.push(`Fiscal Year: ${filter.fiscalYear!}`);
    if (filter.fiscalPeriodNumber !== null) activeFilters.push(`Fiscal Period: ${filter.fiscalPeriodNumber!}`);
    if (filter.fiscalWeekNumber !== null) activeFilters.push(`Fiscal Week: ${filter.fiscalWeekNumber!}`);
    if (filter.fiscalQuarterNumber !== null) activeFilters.push(`Fiscal Quarter: ${filter.fiscalQuarterNumber!}`);
    

    return activeFilters.map((filter) => (
      <Chip key={filter} label={filter} color="primary" variant="outlined" size="medium" sx={{ m: 0.5, fontSize: "1rem" }} />
    ));
  };
  
  useEffect(() => {
    setSelectedYear(filter.fiscalYear);
    setSelectedPeriodNumber(filter.fiscalPeriodNumber);
    setSelectedWeekNumber(filter.fiscalWeekNumber);
    setSelectedQuarterNumber(filter.fiscalQuarterNumber);
  }, [showFilters])

  // Handle Apply Filter Action
  const handleApplyFilter = () => {
    const filter: FiscalCalendarFilterParam = {
      fiscalYear: selectedYear,
      fiscalPeriodNumber: selectedPeriodNumber,
      fiscalWeekNumber: selectedWeekNumber,
      fiscalQuarterNumber: selectedQuarterNumber
    }
    applyFilterCallback(filter)
  }

  // Handle Reset Filter Action
  const handleResetFilter = () => {
    resetFilterCallBack()
  }

  return (
    <>
      <Collapse in={showFilters}>
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                // multiple
                options={config.fiscalYear.map((i) => String(i)) || []}
                value={selectedYear}
                onChange={(_, newValue) => setSelectedYear(newValue)}
                renderInput={(params) => <StyledTextField {...params} label="Fiscal Year" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                options={config.fiscalPeriodNumber.map((i) => String(i)) || []}
                value={selectedPeriodNumber}
                onChange={(_, newValue) => setSelectedPeriodNumber(newValue)}
                renderInput={(params) => <StyledTextField {...params} label="Fiscal Period Number" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                options={config.fiscalWeekNumber.map((i) => String(i)) || []}
                value={selectedWeekNumber}
                onChange={(_, newValue) => setSelectedWeekNumber(newValue)}
                renderInput={(params) => <StyledTextField {...params} label="Fiscal Week Number" variant="outlined" />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                options={config.fiscalQuarterNumber.map((i) => String(i)) || []}
                value={selectedQuarterNumber}
                onChange={(_, newValue) => setSelectedQuarterNumber(newValue)}
                renderInput={(params) => <StyledTextField {...params} label="Fiscal Quarter Number" variant="outlined" />}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <StyledButton variant="outlined" color="secondary" onClick={handleResetFilter}>
              Reset Filters
            </StyledButton>
            <StyledButton variant="contained" color="primary" onClick={handleApplyFilter}>
              Apply Filters
            </StyledButton>
          </Box>
        </Box>
      </Collapse>

      {(filter.fiscalPeriodNumber !== null ||
        filter.fiscalQuarterNumber !== null ||
        filter.fiscalWeekNumber !== null ||
        filter.fiscalYear !== null) && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Active Filters:
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap" }}>{renderFilterChips()}</Box>
        </Box>
      )}
    </>
  );
};

export default FiscalCalendarFilter
