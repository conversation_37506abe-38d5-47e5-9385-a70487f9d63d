import {
  Box,
  Button,
  CircularProgress,
  Fade,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
//   import { setTabData } from "../../../../../__redux/fiscalCalendarSlice";
import { RootState } from "../../../store";
import SaveIcon from "@mui/icons-material/Save";
import InfoIcon from "@mui/icons-material/Info";
import { RevelDiscount } from "../../../../__redux/revelDiscountSlice";
import { AddStoreFormFieldType, FormObjectValidation } from "../../store-logistic-manager/Components/AddStoreHelper";
import { addSpaceToCamelCase, formatDateToString } from "../../ai/components/utils";
import { ROLES } from "../../../constants/constants";
import { UpdateGlRecordRequest } from "../../../api/glDiscountAPI";
import { isDiscountPageAdmin, isDiscountPageReadOnly } from "../../../utils/roleUtils";

interface UpdateGLCodeProps {
  discountData: RevelDiscount;
  onSaveBtnCallback: (data: UpdateGlRecordRequest) => void;
  onCancelBtnCallback: () => void;
}

interface TabData {
  [key: string]: string | number | boolean | null | Date;
}

const FormObjectRule: Record<string, FormObjectValidation> = {
  // TABLE:: str_store
  discount_name: { helperText: "", maxInputLimit: 100, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT, isDisabled: true },
  gl_account_no: { helperText: "", maxInputLimit: 10, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  Updated_date: { helperText: "", maxInputLimit: 10, isPrimaryKey: true, fieldType: AddStoreFormFieldType.DATE },
  is_active: { helperText: "", maxInputLimit: 15, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO, isDisabled: true },
  source: { helperText: "", maxInputLimit: 50, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DROP_DOWN, isDisabled: true },
  id: { helperText: "", maxInputLimit: 10, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
};

const EditGLDiscountTab: React.FC<UpdateGLCodeProps> = ({ discountData, onSaveBtnCallback, onCancelBtnCallback }) => {
  const dispatch = useDispatch();
  const { userRole } = useSelector((state: RootState) => state.auth);

  const theme = useTheme();
  const [editedData, setEditedData] = useState<Record<string, any>>({});
  const [dataSource, setDataSource] = useState<TabData>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const dict: TabData = { ...discountData };
    setDataSource(dict || {});
  }, [discountData]);

  const handleSave = () => {
    const updatedRecord: UpdateGlRecordRequest = {
      // glAccountNo: discountData.gl_account_no,
      id: discountData.id,
      // newDiscountName: String(editedData["discount_name"]),
      // newGlAccountNo: String(editedData["gl_account_no"])
    };
    if (editedData["discount_name"] !== undefined && editedData["discount_name"] !== "") {
      updatedRecord.newDiscountName = String(editedData["discount_name"]);
    }
    if (editedData["gl_account_no"] !== undefined && editedData["gl_account_no"] !== "") {
      updatedRecord.newGlAccountNo = String(editedData["gl_account_no"]);
    }
    if (editedData["is_active"] !== undefined && editedData["is_active"] !== "") {
      updatedRecord.is_active = editedData["is_active"];
    }
    if (editedData["source"] !== undefined && editedData["source"] !== "") {
      updatedRecord.source = String(editedData["source"]);
    }
    if (editedData["id"] !== undefined && editedData["id"] !== "") {
      updatedRecord.id = Number(editedData["id"]);
    }

    onSaveBtnCallback(updatedRecord);
  };

  const handleInputChange = (key: string, value: string | number | boolean | Date | null) => {
    if (validateInputMaxLimit(key, value)) {
      setDataSource((prev) => ({ ...prev, [key]: value }));
      checkForUpdatedData(key, value);
    }
  };

  const validateInputMaxLimit = (key: string, value: string | number | boolean | Date | null): boolean => {
    const rule = FormObjectRule[key];

    switch (rule.fieldType) {
      case AddStoreFormFieldType.NUMERIC: {
        const isValid = Number(value).toString().length <= rule.maxInputLimit;
        return isValid;
      }
      case AddStoreFormFieldType.TEXT: {
        const isValid = String(value).trim().length <= rule.maxInputLimit;
        return isValid;
      }
      default:
        break;
    }
    return true;
  };

  const checkForUpdatedData = (key: string, value: string | number | boolean | Date | null) => {
    const dict: TabData = { ...discountData };
    if (dict[key] != value) {
      setEditedData((prev) => ({ ...prev, [key]: value }));
    } else if (key in editedData) {
      // delete the existing key
      const temp = { ...editedData };
      delete temp[key];
      setEditedData(temp);
    }
  };

  const renderFormInputField = (key: string, value: any) => {
    const rule = FormObjectRule[key];
    // Common props for all inputs

    const commonProps = {
      label: addSpaceToCamelCase(key),
      error: rule.helperText !== "",
      helperText: rule.helperText,
      fullWidth: true,
      disabled: rule.isPrimaryKey,
    };

    // Create UI
    switch (rule.fieldType) {
      case AddStoreFormFieldType.YES_NO:
        return (
          <FormControl fullWidth>
            <InputLabel id={`${key}-label`}>{addSpaceToCamelCase(key)}</InputLabel>
            <Select
              disabled={rule.isDisabled}
              labelId={`${key}-label`}
              id={key}
              value={value === null ? null : value}
              onChange={(e) => handleInputChange(key, e.target.value ? e.target.value === "true" : null)}
              label={addSpaceToCamelCase(key)}
              readOnly={isDiscountPageReadOnly(userRole)}
            >
              <MenuItem value="true">True</MenuItem>
              <MenuItem value="false">False</MenuItem>
            </Select>
          </FormControl>
        );

      case AddStoreFormFieldType.DATE:
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={addSpaceToCamelCase(key)}
              // disabled={PRIMARY_KEYS["str_store" as keyof typeof PRIMARY_KEYS]?.includes(key)}
              value={value ? formatDateToString(value, "MM/DD/YYYY", "GMT") : null}
              onChange={(v) => {
                handleInputChange(key, v);
              }}
              // renderInput={(params) => <TextField {...commonProps} {...params} fullWidth />}
              renderInput={(params) => (
                <TextField
                  {...commonProps}
                  {...params}
                  fullWidth
                  inputProps={{
                    ...params.inputProps,
                    readOnly: true,
                  }}
                />
              )}
              disabled={rule.isPrimaryKey}
              readOnly={isDiscountPageReadOnly(userRole)}
            />
          </LocalizationProvider>
        );

      case AddStoreFormFieldType.NUMERIC:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
            type="number"
            InputProps={{
              readOnly: isDiscountPageReadOnly(userRole),
            }}
          />
        );

      case AddStoreFormFieldType.TEXT:
        return (
          <TextField
            {...commonProps}
            disabled={rule.isDisabled}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, e.target.value)}
            InputProps={{
              readOnly: isDiscountPageReadOnly(userRole),
            }}
          />
        );

      case AddStoreFormFieldType.EMAIL:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, e.target.value)}
            type="email"
            InputProps={{
              readOnly: isDiscountPageReadOnly(userRole),
            }}
          />
        );
      case AddStoreFormFieldType.DROP_DOWN:
        return (
          <FormControl fullWidth>
            <InputLabel id={`${key}-label`}>{addSpaceToCamelCase(key)}</InputLabel>
            <Select
              disabled={rule.isDisabled}
              labelId={`${key}-label`}
              id={key}
              value={value || ""}
              onChange={(e) => handleInputChange(key, e.target.value)}
              label={addSpaceToCamelCase(key)}
            >
              <MenuItem value="Revel">Revel</MenuItem>
              <MenuItem value="Toast">Toast</MenuItem>
            </Select>
          </FormControl>
        );
    }
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <CircularProgress color="primary" />
        </Box>
      );
    }

    if (!dataSource || Object.keys(dataSource).length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <Typography variant="body1">No data available</Typography>
        </Box>
      );
    }

    return (
      <Fade in={true} timeout={500}>
        <Box>
          <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
            <InfoIcon sx={{ mr: 1, color: "info.main" }} />
            <Typography variant="body2" color="info.main">
              Make changes and click Save to update the data, if you make a change and come back to a tab from another tab, the changes will be lost.
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {Object.entries(dataSource).map(([key, value]) => {
              return (
                <Grid item xs={12} sm={6} md={4} key={key}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                      transition: "all 0.3s",
                      borderRadius: 2,
                      border: "1px solid",
                      borderColor: "divider",
                      "&:hover": {
                        boxShadow: theme.shadows[4],
                        borderColor: "primary.main",
                      },
                    }}
                  >
                    {renderFormInputField(key, value)}
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      </Fade>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        // bgcolor: "background.paper",
        borderRadius: 2,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        height: "100%",
        // boxShadow: theme.shadows[4],
      }}
    >
      <Box sx={{ p: 3, flexGrow: 1, overflowY: "auto", maxHeight: "50vh" }}>{renderTabContent()}</Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
        }}
      >
        {/* <Button onClick={handleBack} disabled={currentTab === 0} variant="outlined" sx={{ borderRadius: 20 }}>
            Back
        </Button> */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button onClick={onCancelBtnCallback} sx={{ mr: 1 }}>
            Cancel
          </Button>
          {isDiscountPageAdmin(userRole) && (
            <Button
              onClick={handleSave}
              color="success"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={Object.keys(editedData).length === 0}
              sx={{ borderRadius: 20, mr: 2 }}
            >
              Save
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default EditGLDiscountTab;
