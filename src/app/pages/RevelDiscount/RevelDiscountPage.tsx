import { FC, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Box, Container, Typography, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Button, CircularProgress, Grid } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { styled } from "@mui/system";
import { getGLDiscountData, updateGLDiscountData, UpdateGlRecordRequest, addGLDiscountData } from "../../api/glDiscountAPI";
import { StyledPaper, StyledButton } from "../../CustomComponents/StyledComponents";
import { RevelDiscount } from "../../../__redux/revelDiscountSlice";
import RevelDiscountTable from "./components/RevelDiscountTable";
import EditGLDiscountTab from "./components/RevelDiscountEditTab";
import { setFullScreenLoader, pushNewAlert } from "../../../__redux/generalSlice";
import { RootState } from "../../store";
import { ROLES } from "../../constants/constants";
import AddIcon from "@mui/icons-material/Add";
import BackupTableIcon from "@mui/icons-material/BackupTable";
import AddRevelDiscountTab from "./components/AddRevelDiscountTab";
import SearchBar from "../../CustomComponents/SearchBar";
import { setNewGlDiscount } from "../../../__redux/glCodeDiscountSlice";
import moment from "moment";
import { isDiscountPageAdmin } from "../../utils/roleUtils";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const RevelDiscountPage: FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<RevelDiscount[]>([]);
  const [filteredDataSource, setFilteredDataSource] = useState<RevelDiscount[]>([]);
  const [openEditGlDiscount, setOpenEditGlDiscount] = useState<boolean>(false);
  const [openNewGlDiscount, setOpenNewGlDiscount] = useState<boolean>(false);
  const [selectedRecord, setSelectedRecord] = useState<RevelDiscount | null>(null);
  const [saveUpdateConfirmOpen, setSaveUpdateConfirmOpen] = useState<boolean>(false);
  const [saveNewConfirmOpen, setSaveNewConfirmOpen] = useState<boolean>(false);
  const [updatedFields, setUpdatedFields] = useState<string[]>([]);
  const [localUpdatedRecord, setLocalUpdatedRecord] = useState<UpdateGlRecordRequest>();
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");

  const { userRole } = useSelector((state: RootState) => state.auth);
  const { newGLDiscount } = useSelector((state: RootState) => state.glDiscount);

  const dispatch = useDispatch();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const result = await getGLDiscountData();
      setDataSource(result);
      setFilteredDataSource(result);
    } catch (error) {
      console.error("Error fetching validation data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const updateExistingRecord = async () => {
    if (localUpdatedRecord == undefined) return;
    // Call Update API
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      await updateGLDiscountData(localUpdatedRecord);
      setSaveUpdateConfirmOpen(false);
      setOpenEditGlDiscount(false);
      setLocalUpdatedRecord(undefined);
      setUpdatedFields([]);

      // Call Fetch Calendar List API
      await fetchData();
      dispatch(
        pushNewAlert({
          type: "success",
          message: "GL Discount record updated successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving  changes:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to update store",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while saving changes",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  const addNewGlDiscountRecord = async () => {
    // Call Update API
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      await addGLDiscountData([newGLDiscount]);
      dispatch(
        setNewGlDiscount({
          discount_name: "",
          gl_account_no: "",
          is_active: true,
          source: "Revel",
          id: null,
          update_date: moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss"),
        })
      );
      setSaveNewConfirmOpen(false);
      setOpenNewGlDiscount(false);
      setUpdatedFields([]);

      // Call Fetch Calendar List API
      await fetchData();
      dispatch(
        pushNewAlert({
          type: "success",
          message: "New GL Discount record added successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving  changes:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to update store",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while saving changes",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  const handleEditBtnClick = (store: RevelDiscount) => {
    setSelectedRecord(store);
    setOpenEditGlDiscount(true);
  };

  const handleEditSaveChanges = (data: UpdateGlRecordRequest) => {
    const arr: string[] = [];
    if (data.newDiscountName !== null && data.newDiscountName !== undefined) {
      arr.push("Discount Name");
    }
    if (data.newGlAccountNo !== null && data.newGlAccountNo !== undefined) {
      arr.push("GL Account No");
    }
    if (data.source !== null && data.source !== undefined) {
      arr.push("Source");
    }
    if (data.id !== null && data.id !== undefined) {
      arr.push("Id");
    }
    if (data.is_active !== null && data.is_active !== undefined) {
      arr.push("Is Active");
    }

    setUpdatedFields(arr);
    setLocalUpdatedRecord(data);
    setSaveUpdateConfirmOpen(true);
  };

  const handCloseUpdateTab = () => {
    setOpenEditGlDiscount(false);
  };

  const handleConfirmUpdateSave = async () => {
    await updateExistingRecord();
  };

  const handleAddNewGLCode = () => {
    setOpenNewGlDiscount(true);
  };

  const handleCloseAddGLCodeTab = () => {
    setOpenNewGlDiscount(false);
  };

  const handleAddSaveChanges = () => {
    setSaveNewConfirmOpen(true);
  };

  const handleConfirmNewGLDiscountSave = async () => {
    await addNewGlDiscountRecord();
  };

  const handleSearch = () => {
    const filteredData = searchRevelDiscounts(dataSource, searchText);
    setFilteredDataSource(filteredData);
  };

  const searchRevelDiscounts = (discounts: RevelDiscount[], searchString: string): RevelDiscount[] => {
    // Convert the search string to lowercase to make the search case-insensitive
    const lowerSearchString = searchString.toLowerCase();

    // Filter the array of discounts
    return discounts.filter(discount => {
      return Object.values(discount).some(value => {
        if (typeof value === "string") {
          return value.toLowerCase().includes(lowerSearchString);
        }
        if (typeof value === "boolean") {
          return value.toString().toLowerCase().includes(lowerSearchString);
        }
        return value.toString().includes(lowerSearchString);
      });
    });
  };

  const handleClearSearch = () => {
    setSearchText("");
    setFilteredDataSource(dataSource);
  };

  const handleViewDiscountTable = () => {
    window.open("/revel-discount-table", "_blank");
  };

  const headers = isDiscountPageAdmin(userRole)
    ? ["Action", "Discount Name", "Gl Account No", "Update Date", "Is Active", "Source", "Id"]
    : ["Discount Name", "Gl Account No", "Update Date", "Is Active", "Source", "Id"];

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                Discount & GL Code -
              </Typography>
              <SearchBar
                searchText={searchText}
                setSearchText={text => setSearchText(text)}
                handleSearch={handleSearch}
                handleClearSearch={handleClearSearch}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                <StyledButton variant="outlined" color="primary" onClick={handleViewDiscountTable} startIcon={<BackupTableIcon />}>
                  View Newly Added Discount
                </StyledButton>
                {isDiscountPageAdmin(userRole) && (
                  <StyledButton variant="contained" color="primary" onClick={handleAddNewGLCode} startIcon={<AddIcon />}>
                    Add New Record
                  </StyledButton>
                )}
              </Box>
            </Grid>
          </Grid>
        </StyledPaper>

        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <RevelDiscountTable tableData={filteredDataSource} headers={headers} loading={isLoading} handleClickOpen={handleEditBtnClick} userRole={userRole} />
        </Box>
      </Box>

      {/* BEGIN:: Update GL Record Form Dialog Box */}
      <Dialog open={openEditGlDiscount} onClose={handCloseUpdateTab} maxWidth="md">
        <DialogTitle>Update GL Discount</DialogTitle>
        <DialogContent dividers>
          <EditGLDiscountTab discountData={selectedRecord!} onSaveBtnCallback={handleEditSaveChanges} onCancelBtnCallback={handCloseUpdateTab} />
        </DialogContent>
      </Dialog>
      {/* END:: Update GL Record Form Dialog Box */}

      {/* BEGIN:: Save Edit Calendar Dialog Box */}
      <Dialog open={saveUpdateConfirmOpen} onClose={() => setSaveUpdateConfirmOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Confirm Changes</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to make changes to the following fields
          </Typography>
          {updatedFields.length > 0 && (
            <Box
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-start",
                // gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
                gap: 10,
              }}
            >
              {updatedFields.map((columnName, index) => {
                if (columnName.toLowerCase() === "id") return null;
                return (
                  <Box
                    key={index}
                    sx={{
                      padding: 1,
                      backgroundColor: "rgba(255, 255, 255, 0.05)",
                      border: 1,
                      borderColor: "grey",
                      borderRadius: "8px",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        transform: "translateY(-2px)",
                        boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: "0.9rem",
                        letterSpacing: "0.5px",
                        color: "primary.light",
                      }}
                    >
                      {columnName}
                    </Typography>
                  </Box>
                );
              })}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveUpdateConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpdateSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* End:: Save Edit Calendar Dialog Box */}

      {/* BEGIN:: Manually Add Calendar Form Dialog Box */}
      <Dialog open={openNewGlDiscount} onClose={handleCloseAddGLCodeTab} maxWidth="md" fullWidth>
        <DialogTitle>Add New GL Discount</DialogTitle>
        <DialogContent dividers>
          <AddRevelDiscountTab onSaveBtnCallback={handleAddSaveChanges} onCancelBtnCallback={handleCloseAddGLCodeTab} />
        </DialogContent>
      </Dialog>
      {/* END:: Manually Add Calendar Form Dialog Box */}

      {/* BEGIN:: Save Add New GL Code Dialog Box */}
      <Dialog open={saveNewConfirmOpen} onClose={() => setSaveNewConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Record</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to add new GL Discount record
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveNewConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmNewGLDiscountSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>
      {/* End:: Save Add Manual Calendar Dialog Box */}
    </ThemeProvider>
  );
};

export default RevelDiscountPage;
