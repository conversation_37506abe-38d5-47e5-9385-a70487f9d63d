import React, { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent } from "react";
import { createTheme, ThemeProvider, alpha, styled } from "@mui/material/styles";
import { Box, Typography, Grid, CircularProgress, Backdrop, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { StyledPaper } from "../../../CustomComponents/StyledComponents";
import SearchBar from "../../../CustomComponents/SearchBar";
import { getAllGLDiscountData } from "../../../api/glDiscountAPI";
import { RevelDiscount } from "../../../../__redux/revelDiscountSlice";
import Footer from "../../../CustomComponents/Footer";
import { useDispatch } from "react-redux";
import { setFullScreenLoader } from "../../../../__redux/generalSlice";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import moment, { MomentInput } from "moment-timezone";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: "calc(100vh - 300px)",
  "&::-webkit-scrollbar": {
    width: "0.4em",
    height: "0.4em",
  },
  "&::-webkit-scrollbar-track": {
    boxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
    webkitBoxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
  },
  "&::-webkit-scrollbar-thumb": {
    backgroundColor: theme.palette.primary.main,
    opacity: 0.2,
    borderRadius: "10px",
    "&:hover": {
      backgroundColor: theme.palette.primary.dark,
    },
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  whiteSpace: "nowrap",
  padding: theme.spacing(2),
  fontSize: "1.2rem",
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
  },
}));

const RevelDiscountTableView = () => {
  const dispatch = useDispatch();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [discounts, setDiscounts] = useState<RevelDiscount[]>([]);
  const [searchText, setSearchText] = useState<string>("");
  const [searchColumns, setSearchColumns] = useState<string[]>([]);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(15);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [availableColumns, setAvailableColumns] = useState<string[]>(["discount_name", "gl_account_no", "Updated_date", "is_active", "source", "id"]);
  const [error, setError] = useState<string | null>(null);
  const [sortBy] = useState<string>("Updated_date");
  const [sortOrder] = useState<"asc" | "desc">("desc");
  const [shouldFetchData, setShouldFetchData] = useState<boolean>(true);

  const fetchDiscountData = useCallback(async () => {
    if (!shouldFetchData) return;

    setIsLoading(true);
    setError(null);

    if (discounts.length === 0) {
      dispatch(setFullScreenLoader(true));
    }

    try {
      const params = {
        page,
        limit,
        searchText: searchText || undefined,
        searchColumns: searchColumns.length > 0 ? searchColumns : undefined,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder || undefined,
      };

      const response = await getAllGLDiscountData(params);

      if (response && response.result) {
        setDiscounts(response.result);
        setTotalPages(response.totalPages || 1);

        // If this is the first fetch, set available columns based on the first record
        if (response.result.length > 0 && availableColumns.length === 6) {
          setAvailableColumns(Object.keys(response.result[0]));
        }
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      console.error("Error fetching discount data:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
      setDiscounts([]);
    } finally {
      setIsLoading(false);
      setShouldFetchData(false);
      dispatch(setFullScreenLoader(false));
    }
  }, [dispatch, page, limit, searchText, searchColumns, sortBy, sortOrder, shouldFetchData, discounts.length, availableColumns.length]);

  useEffect(() => {
    fetchDiscountData();
  }, [fetchDiscountData]);

  const handleSearch = () => {
    setPage(1);
    setShouldFetchData(true);
  };

  const handleClearSearch = () => {
    setSearchText("");
    setSearchColumns([]);
    setPage(1);
    setShouldFetchData(true);
  };

  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    setLimit(value === 100 ? 200 : value);
    setPage(1);
    setShouldFetchData(true);
  };

  const handlePageChange = (_event: ChangeEvent<unknown>, value: number) => {
    setPage(value);
    setShouldFetchData(true);
  };

  // Helper function to format display value
  const formatValue = (value: string | number | boolean | null | undefined, field: string): string => {
    if (value === null || value === undefined) return "-";

    if (["Updated_date", "update_date", "order_date"].includes(field)) {
      const date = moment(value as MomentInput);
      if (date.isValid()) {
        return date.tz("America/Chicago").format("MM/DD/YYYY");
      }
    }

    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    return String(value);
  };

  // Render table content
  const renderTableContent = () => {
    if (isLoading) {
      return (
        <TableBody>
          {[...Array(15)].map((_, index) => (
            <StyledTableRow key={index}>
              {availableColumns.map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Box sx={{ height: 24, width: "100%", bgcolor: "rgba(0, 0, 0, 0.08)", borderRadius: 1 }} />
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }

    if (discounts.length === 0) {
      return (
        <TableBody>
          <StyledTableRow>
            <TableCell colSpan={availableColumns.length} align="center">
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <ErrorOutlineIcon color="action" style={{ fontSize: 60, marginBottom: 16 }} />
                <Typography variant="h6" color="textSecondary">
                  No discount data found
                </Typography>
              </Box>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {discounts.map((item, index) => (
          <StyledTableRow key={index}>
            {availableColumns.map(field => (
              <TableCell key={field} sx={{ fontSize: "1.1rem" }}>
                {formatValue(item[field as keyof RevelDiscount], field)}
              </TableCell>
            ))}
          </StyledTableRow>
        ))}
      </TableBody>
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <Backdrop
        sx={{
          color: "#fff",
          zIndex: theme.zIndex.drawer + 1,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backdropFilter: "blur(3px)",
        }}
        open={isLoading && discounts.length === 0}
      >
        <CircularProgress color="inherit" size={70} thickness={5} />
        <Typography variant="h6" sx={{ mt: 2, color: "white", fontWeight: 500 }}>
          Loading discount data...
        </Typography>
      </Backdrop>

      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                Discount Listing -
              </Typography>
              <SearchBar
                searchText={searchText}
                setSearchText={setSearchText}
                handleSearch={handleSearch}
                handleClearSearch={handleClearSearch}
                showFieldSelector={true}
                searchColumns={searchColumns}
                availableColumns={availableColumns}
                setSearchColumns={setSearchColumns}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                <Box
                  sx={{
                    p: 1.5,
                    backgroundColor: alpha(theme.palette.info.light, 0.1),
                    borderRadius: 1,
                    border: "1px solid",
                    borderColor: alpha(theme.palette.info.main, 0.2),
                    display: "flex",
                    alignItems: "flex-start",
                    gap: 1.5,
                  }}
                >
                  <InfoOutlinedIcon color="info" fontSize="small" sx={{ mt: 0.3 }} />
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500, color: "text.secondary" }}>
                      This view shows all discount records in the system
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </StyledPaper>

        {error && (
          <Box sx={{ p: 2, bgcolor: "error.light", color: "error.contrastText", mx: 2, my: 1, borderRadius: 1 }}>
            <Typography variant="body1">Error: {error}</Typography>
          </Box>
        )}

        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <StyledTableContainer>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  {availableColumns.map(column => (
                    <StyledTableCell key={column}>
                      <span>{column.charAt(0).toUpperCase() + column.slice(1).replace(/_/g, " ")}</span>
                    </StyledTableCell>
                  ))}
                </TableRow>
              </TableHead>
              {renderTableContent()}
            </Table>
          </StyledTableContainer>
        </Box>

        <Footer
          limit={limit}
          totalPages={totalPages}
          page={page}
          count={discounts.length}
          handleLimitChange={handleLimitChange}
          handlePageChange={handlePageChange}
          disabled={isLoading}
        />
      </Box>
    </ThemeProvider>
  );
};

export default RevelDiscountTableView;
