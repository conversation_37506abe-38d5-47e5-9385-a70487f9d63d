import {FC, useEffect, useState} from 'react'
import { useSelector, useDispatch } from 'react-redux';
import { Box, Container, Typography, Paper, Grid } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { styled } from "@mui/system";
import { fetchValidationData, VDFilterParamRequest, fetchVDConfig } from '../../api/validationDataAPi';
import { ValidRecordItem } from '../../../__redux/validationDataSlice';
import { StyledPaper, StyledButton } from "../../CustomComponents/StyledComponents";
import ValidationDataTable from './components/ValidationDataTable';
import { RootState } from "../../store";
import FilterListIcon from "@mui/icons-material/FilterList";
import { setShowFilters, setLimit, setPage, setTotalPages, setFilters, setConfig, ValidationDataFilterParam } from '../../../__redux/validationDataSlice';
import Footer from "../../CustomComponents/Footer";
import ValidationDataFilter from './components/ValidationDataFilter';
import { formatDateToString } from '../ai/components/utils';


const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});


const ValidationDataPage: FC = () => {

  const dispatch = useDispatch();
  const {
    page, 
    totalPages, 
    limit, 
    filters,
    showFilters,
    config,
  } = useSelector((state: RootState) => state.validationData);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<ValidRecordItem[]>([]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      let filterRequest: VDFilterParamRequest = {storeName: filters.storeName, businessDate: null}
      if (filters.businessDate !== null) {
        const businessDate = formatDateToString(filters.businessDate, "YYYY-MM-DD");
        filterRequest.businessDate = businessDate
      }
      
      const response = await fetchValidationData(page, limit, filterRequest);
      setDataSource(response.result);
      dispatch(setTotalPages(response.totalPages));
    } catch (error) {
      console.error("Error fetching validation data:", error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    fetchData();
  }, [page, limit, filters]);

   // Fetch Filter Config Data
   const fetchConfigData = async () => {
    try {
      const config = await fetchVDConfig();
      dispatch(setConfig(config));
    } catch (error) {
      console.error("Error fetching config:", error);
    }
  }
  useEffect(() => {
    
    fetchConfigData()
  }, []);


  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    dispatch(setLimit(value === 100 ? 1000 : value));
    dispatch(setPage(1));
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    dispatch(setPage(value));
  };

  const handleToggleFilters = () => {
    dispatch(setShowFilters(!showFilters));
  };

  const handleResetFilter = () => {
    const emptyFilter: ValidationDataFilterParam = {
      businessDate: null,
      storeName: null,
    }
    dispatch(setFilters(emptyFilter));
    dispatch(setShowFilters(false));
  }

  const handleApplyFilter = (params: ValidationDataFilterParam) => {
    dispatch(setFilters(params));
    dispatch(setShowFilters(false));
  }

  const headers = [
    "Site Number",
    "Store",
    "Business Date",
    "Store Name",
    "GP Net Sales",
    "GP Gross Sales",
    "GP Transaction",
    "Fact Net Sales",
    "Fact Gross Sales",
    "Fact Transaction",
    "OT Hrs Net Sales",
    "OT Gross Sales",
    "PB Total hrs",
    "Portal Total Hrs",
    "PB Total Labour cost",
    "Portal Total Labour Cost",
    "Ukg Raw Total Hours",
    "UKG Raw Exception Pay",
    "Ukg Raw Sales",
    "HS Raw Total Hours",
    "HS Raw Exception Pay",
    "HS Raw Sales",
  ]

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
      <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid
            item
            xs={12}
            md={6}
            sx={{
              display: "flex",
              justifyContent: "flex-start",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Typography
              variant="h4"
              sx={{
                flexShrink: 0,
                whiteSpace: "nowrap",
                fontWeight: 500,
                letterSpacing: "0.05em",
                color: "primary.main",
              }}
            >
              Validation Data
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
              <StyledButton variant="outlined" color="primary" onClick={handleToggleFilters} startIcon={<FilterListIcon />}>
                {showFilters ? "Hide Filters" : "Show Filters"}
              </StyledButton>
            </Box>
          </Grid>
        </Grid>
        <ValidationDataFilter
            showFilters={showFilters}
            config={config}
            filter={filters}
            resetFilterCallBack={handleResetFilter}
            applyFilterCallback={handleApplyFilter}
          />
      </StyledPaper>
      
        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <ValidationDataTable 
            tableData={dataSource} 
            headers={headers}
            loading={isLoading} 
            limit={limit}
          />
        </Box>
        <Footer
          limit={limit}
          totalPages={totalPages}
          page={page}
          count={dataSource.length}
          handleLimitChange={handleLimitChange}
          handlePageChange={handlePageChange}
        />
      </Box>
    </ThemeProvider>

  )
}

export default ValidationDataPage
