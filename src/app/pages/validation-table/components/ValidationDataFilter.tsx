import React, { useEffect, useState } from "react";
import { Autocomplete, Box, Button, Chip, Collapse, Grid, TextField, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import { StyledButton, StyledTextField } from "../../../CustomComponents/StyledComponents";
import { ValidationDataFilterParam } from "../../../../__redux/validationDataSlice";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { formatDateToString } from "../../ai/components/utils";
import { VDConfig } from "../../../api/validationDataAPi";

interface ValidationDataFilterProps {
  showFilters: boolean;
  config: VDConfig;
  filter: ValidationDataFilterParam;
  resetFilterCallBack: () => void;
  applyFilterCallback: (params: ValidationDataFilterParam) => void;
}
const ValidationDataFilter: React.FC<ValidationDataFilterProps> = ({ showFilters, config, filter, resetFilterCallBack, applyFilterCallback }) => {
  const [selectedBusinessDate, setSelectedBusinessDate] = useState<Date | null>(filter.businessDate);
  const [selectedStoreName, setSelectedStoreName] = useState<string | null>(filter.storeName);

  const renderFilterChips = () => {
    const activeFilters: string[] = [];
    if (filter.businessDate !== null) activeFilters.push(`Business Date: ${formatDateToString(filter.businessDate, "MM-DD-YYYY")}`);
    if (filter.storeName !== null) activeFilters.push(`Store Name: ${filter.storeName!}`);

    return activeFilters.map((filter) => (
      <Chip key={filter} label={filter} color="primary" variant="outlined" size="medium" sx={{ m: 0.5, fontSize: "1rem" }} />
    ));
  };

  useEffect(() => {
    setSelectedBusinessDate(filter.businessDate);
    setSelectedStoreName(filter.storeName);
  }, [showFilters]);

  // Handle Apply Filter Action
  const handleApplyFilter = () => {
    const filter: ValidationDataFilterParam = {
      businessDate: selectedBusinessDate,
      storeName: selectedStoreName,
    };
    applyFilterCallback(filter);
  };

  // Handle Reset Filter Action
  const handleResetFilter = () => {
    resetFilterCallBack();
  };

  return (
    <>
      <Collapse in={showFilters}>
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2}>
            {/*  Store Name Container */}
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Autocomplete
                // multiple
                options={config.stores.map((i) => String(i)) || []}
                value={selectedStoreName}
                onChange={(_, newValue) => setSelectedStoreName(newValue)}
                renderInput={(params) => <StyledTextField {...params} label="Store Name" variant="outlined" />}
              />
            </Grid>
            {/*  Business Date Container */}
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Business Date"
                  value={selectedBusinessDate}
                  onChange={(newValue) => setSelectedBusinessDate(newValue)}
                  // renderInput={(params) => <TextField {...params} fullWidth />}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      inputProps={{
                        ...params.inputProps,
                        readOnly: true,
                      }}
                    />
                  )}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <StyledButton variant="outlined" color="secondary" onClick={handleResetFilter}>
              Reset Filters
            </StyledButton>
            <StyledButton variant="contained" color="primary" onClick={handleApplyFilter}>
              Apply Filters
            </StyledButton>
          </Box>
        </Box>
      </Collapse>

      {(filter.businessDate !== null || filter.storeName !== null) && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Active Filters:
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap" }}>{renderFilterChips()}</Box>
        </Box>
      )}
    </>
  );
};

export default ValidationDataFilter;
