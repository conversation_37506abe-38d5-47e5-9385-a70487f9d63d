import { FC } from "react";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Skeleton, Box, Typography, Tooltip, IconButton } from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { styled } from "@mui/material/styles";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import moment, { MomentInput } from "moment-timezone";
import { ValidRecordItem } from "../../../../__redux/validationDataSlice";
import { convertToMMDDYYYY } from "../../ai/components/utils";

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: "calc(100vh - 300px)",
  "&::-webkit-scrollbar": {
    width: "0.4em",
    height: "0.4em",
  },
  "&::-webkit-scrollbar-track": {
    boxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
    webkitBoxShadow: "inset 0 0 6px rgba(0,0,0,0.00)",
  },
  "&::-webkit-scrollbar-thumb": {
    backgroundColor: theme.palette.primary.main,
    opacity: 0.2,
    borderRadius: "10px",
    "&:hover": {
      backgroundColor: theme.palette.primary.dark,
    },
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  whiteSpace: "nowrap",
  padding: theme.spacing(2),
  fontSize: "1.2rem",
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
  },
}));

interface ValidationDataTableProps {
  tableData: ValidRecordItem[];
  headers: string[];
  loading: boolean;
  limit: number;
}

const ValidationDataTable: FC<ValidationDataTableProps> = ({ tableData, headers, loading, limit }) => {
  type Value = string | number | null | boolean | Date;

  const formatValue = (value: Value, field: string): string => {
    if (value === null) return "-";

    if (["business_date"].includes(field)) {
      const date = moment(value as MomentInput);
      if (date.isValid()) {
        // return date.tz("America/Chicago").format("MM/DD/YYYY");
        return date.tz("America/Chicago").format("MM/DD/YYYY");
      }
    }
    // Check the value is numeric or not
    if (!isNaN(Number(value)) && String(value).trim() !== "") {
      const num = Number(value);
      return String(Math.round(num));
    }
    return String(value);
  };

  const renderTableContent = () => {
    if (loading) {
      return (
        <TableBody>
          {[...Array(limit)].map((_, index) => (
            <StyledTableRow key={index}>
              {headers.map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton animation="wave" />
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }

    if (tableData.length === 0) {
      return (
        <TableBody>
          <StyledTableRow>
            <TableCell colSpan={headers.length} align="center">
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <ErrorOutlineIcon color="action" style={{ fontSize: 60, marginBottom: 16 }} />
                <Typography variant="h6" color="textSecondary">
                  No validation data found
                </Typography>
              </Box>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      );
    }

    if (!loading && tableData.length !== 0) {
      return (
        <TableBody>
          {tableData.map((item, index) => (
            <StyledTableRow key={index}>
              {Object.entries(item).map(([field, value], index) => (
                <TableCell key={index} sx={{ fontSize: "1.1rem" }}>
                  {formatValue(value, field)}
                </TableCell>
              ))}
            </StyledTableRow>
          ))}
        </TableBody>
      );
    }
  };

  return (
    <StyledTableContainer>
      <Table stickyHeader>
        <TableHead>
          <TableRow>
            {headers.map(header => (
              <StyledTableCell key={header}>
                <span>{header}</span>
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        {renderTableContent()}
      </Table>
    </StyledTableContainer>
  );
};

export default ValidationDataTable;
