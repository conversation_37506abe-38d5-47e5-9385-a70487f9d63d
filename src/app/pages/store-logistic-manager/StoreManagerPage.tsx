import { useDispatch, useSelector } from "react-redux";

import BackupTableIcon from "@mui/icons-material/BackupTable";
import FilterListIcon from "@mui/icons-material/FilterList";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import SortIcon from "@mui/icons-material/Sort";
import { Box, Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Grid, List, ListItem, Popover, Typography } from "@mui/material";
import { alpha, createTheme, styled, ThemeProvider } from "@mui/material/styles";
import { ChangeEvent, FC, useCallback, useEffect, useState } from "react";
import { initialAddStoreData, setNewStore } from "../../../__redux/addStoreSlice";
import { pushNewAlert, setFullScreenLoader } from "../../../__redux/generalSlice";
import {
  resetTabData,
  setConfig,
  setConfirmDialogError,
  setConfirmDialogOpen,
  setConfirmDialogSuccess,
  setFilters,
  setIsAddMode,
  setLimit,
  setLoading,
  setOpen,
  setPage,
  setSearchText,
  setSelectedDistricts,
  setSelectedGeographies,
  setSelectedIsClosed,
  setSelectedMarkets,
  setSelectedRegions,
  setSelectedStoreConcepts,
  setSelectedStoreNames,
  setShowFilters,
  setSortBy,
  setSortOrder,
  setStores,
  setTotalPages,
} from "../../../__redux/storeManagerSlice";
import { addNewStoreData, fetchConfig, fetchStores, searchStores, updateStoreNew } from "../../api/storeLogisticManagerApi";
import ConfirmationDialog from "../../components/confirmationDialog/ConfirmationDialog";
import FilterSection from "../../CustomComponents/FilterSection";
import Footer from "../../CustomComponents/Footer";
import SearchBar from "../../CustomComponents/SearchBar";
import StoreTable from "../../CustomComponents/StoreTable";
import { StyledButton, StyledPaper } from "../../CustomComponents/StyledComponents";
import { RootState } from "../../store";
import { filterList, TabData } from "../../Types/store-types";
import { isStorePageAdmin } from "../../utils/roleUtils";
import AddStoreTabs from "./Components/AddStoreTabs";
import StoreDetailsTabs, { convertAllDateToString } from "./Components/StoreDetailsTabs";

type StoreEntry = Record<string, Record<string, unknown>>;

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});
const StoreManagerPage: FC<{ tablename: string }> = ({ tablename }) => {
  const dispatch = useDispatch();

  const { userRole } = useSelector((state: RootState) => state.auth);
  const { newStore } = useSelector((state: RootState) => state.addStore);
  const {
    stores,
    loading,
    page,
    totalPages,
    limit,
    open,
    formData,
    isAddMode,
    refresh,
    searchText,
    config,
    selectedMarkets,
    selectedGeographies,
    selectedRegions,
    selectedDistricts,
    selectedStoreNames,
    selectedStoreConcepts,
    selectedIsClosed,
    filters,
    showFilters,
    confirmDialogOpen,
    confirmDialogLoading,
    confirmDialogError,
    confirmDialogSuccess,
    // tabData,
    sortBy,
    sortOrder,
  } = useSelector((state: RootState) => state.storeManager);

  const [saveConfirmOpen, setSaveConfirmOpen] = useState(false);
  const [changedTables, setChangedTables] = useState<Record<string, Record<string, unknown>>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const [addStoreEmptyTableNames, setAddStoreEmptyTableNames] = useState<string[]>([]);

  const fetchStoresData = useCallback(
    async (page: number, limit: number, filters?: filterList, sortBy: string = "SQL ID", sortOrder: "asc" | "desc" = "asc") => {
      dispatch(setLoading(true));
      try {
        const data = await fetchStores(page, limit, filters, sortBy, sortOrder);
        dispatch(setStores(data.result));
        dispatch(setTotalPages(data.totalPages));
      } catch (error) {
        console.error("Error fetching stores:", error);
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const uploadNewStore = async () => {
    console.log("trigger new store api");
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      const tempStore: StoreEntry = JSON.parse(JSON.stringify(newStore));
      // delete empty tables
      for (const tableName of addStoreEmptyTableNames) {
        delete tempStore[tableName];
      }
      const tableKeys = Object.keys(tempStore);
      for (const tableKey of tableKeys) {
        const tabData = convertAllDateToString(tempStore[tableKey] as TabData, "YYYY-MM-DD");
        tempStore[tableKey] = tabData;
      }
      await addNewStoreData(tempStore);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "Store added successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
      setSaveConfirmOpen(false);
      handleCloseDialog();
      dispatch(setNewStore(initialAddStoreData));
    } catch (error) {
      console.error(" Error uploading new store", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to add store",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while uploading new store",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  useEffect(() => {
    if (filters) {
      fetchStoresData(page, 200, filters, sortBy || undefined, sortOrder || undefined).then(() => {
        dispatch(setSearchText(""));
      });
    } else {
      fetchStoresData(page, limit, undefined, sortBy || undefined, sortOrder || undefined).then(() => {
        dispatch(setSearchText(""));
      });
    }
    fetchConfigData();
  }, [page, limit, refresh, filters, sortBy, sortOrder, dispatch, fetchStoresData]);

  const fetchConfigData = async () => {
    try {
      const config = await fetchConfig();
      dispatch(setConfig(config));
    } catch (error) {
      console.error("Error fetching config:", error);
    }
  };

  const handleSearch = async () => {
    if (searchText.trim() === "") {
      alert("Search text is required.");
      return;
    }

    dispatch(setLoading(true));
    try {
      const results = await searchStores(searchText);
      dispatch(setStores(results));
      dispatch(setTotalPages(1));
    } catch (error) {
      console.error("Error searching stores:", error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    dispatch(setLimit(value === 100 ? 1000 : value));
    dispatch(setPage(1));
  };

  const handlePageChange = (_event: ChangeEvent<unknown>, value: number) => {
    dispatch(setPage(value));
  };

  // const handleClickOpen = (store: Store) => {
  //   dispatch(setIsAddMode(false));
  //   dispatch(setFormData(store));
  //   dispatch(resetTabData());
  //   dispatch(setOpen(true));
  // };

  // const handleAddNewStore = () => {
  //   dispatch(setIsAddMode(true));
  //   dispatch(setOpen(true));
  // };

  const handleCancelConfirm = () => {
    dispatch(setConfirmDialogOpen(false));
    dispatch(setConfirmDialogError(null));
    dispatch(setConfirmDialogSuccess(false));
  };

  const handleApplyFilter = () => {
    const newFilters = {
      geographyName: selectedGeographies,
      Region: selectedRegions,
      District: selectedDistricts,
      marketName: selectedMarkets,
      storeConceptName: selectedStoreConcepts,
      storeName: selectedStoreNames,
      IsClosed: selectedIsClosed,
    };

    Object.keys(newFilters).forEach(key => {
      if (newFilters[key as keyof typeof newFilters].length === 0) {
        delete newFilters[key as keyof typeof newFilters];
      }
    });
    dispatch(setFilters(newFilters));
    dispatch(setPage(1));
    dispatch(setShowFilters(false));
  };

  const handleResetFilter = () => {
    dispatch(setSelectedGeographies([]));
    dispatch(setSelectedRegions([]));
    dispatch(setSelectedDistricts([]));
    dispatch(setSelectedMarkets([]));
    dispatch(setSelectedStoreConcepts([]));
    dispatch(setSelectedStoreNames([]));
    dispatch(setSelectedIsClosed(""));
    dispatch(setSearchText(""));
    fetchStoresData(1, limit);
  };

  const handleToggleFilters = () => {
    dispatch(setShowFilters(!showFilters));
  };

  const handleClearSearch = () => {
    dispatch(setSearchText(""));
    fetchStoresData(1, limit);
  };

  const handleCloseDialog = () => {
    dispatch(setOpen(false));
    dispatch(setIsAddMode(false));
    dispatch(resetTabData());
  };

  const handleUpdateStoreSaveChanges = (editedTabData: Record<string, TabData>) => {
    const changedValues = Object.entries(editedTabData).reduce<Record<string, Record<string, unknown>>>((acc, [tableName, data]) => {
      const typedData = data as Record<string, unknown>;
      const changedFields = Object.entries(typedData).filter(([key, value]) => value !== formData[key as keyof typeof formData]);
      if (changedFields.length > 0) {
        acc[tableName] = Object.fromEntries(changedFields);
      }
      return acc;
    }, {} as Record<string, Record<string, unknown>>);

    setChangedTables(changedValues);
    setSaveConfirmOpen(true);
  };

  const handleNewStoreSaveChanges = () => {
    const errorText = validateNewStoreInput();
    if (errorText) {
      window.alert(errorText);
      return;
    }
    setSaveConfirmOpen(true);
  };

  // Validate New Store User Input.
  //
  const validateNewStoreInput = () => {
    const tableNames = Object.keys(newStore);
    const emptyTableNames: string[] = [];
    for (const tableKey of tableNames) {
      const columnKeys = Object.keys(newStore[tableKey]);
      const tableDict = newStore[tableKey];
      let emptyFieldCount = 0;
      for (const column of columnKeys) {
        if (column === "closedDate") continue;
        if (tableDict[column] === null || tableDict[column] === "") {
          console.log(`Table: ${tableKey} Empty field: ${column}`);
          emptyFieldCount += 1;
        }
      }
      // 1 is deducted for one optional column in "str_store" table
      const columnCount = tableKey === "str_store" ? columnKeys.length - 1 : columnKeys.length;
      if (columnCount === emptyFieldCount) emptyTableNames.push(tableKey);
      else {
        if (emptyFieldCount > 0) {
          return `Please fill up all the fields in ${formatTableName(tableKey)} tab`;
        }
      }
    }
    if (tableNames.length === emptyTableNames.length) return "Please fill up at least one table";
    else {
      // store the empty table names
      setAddStoreEmptyTableNames(emptyTableNames);
    }
    return null;
  };

  const handleConfirmSave = async () => {
    isAddMode ? await uploadNewStore() : await updateExistingStore();
  };
  const updateExistingStore = async () => {
    const objToSave = {
      data: changedTables,
      storeId: formData["SQL ID"],
    };
    console.log("objToSave", objToSave);
    setIsSaving(true);
    dispatch(setFullScreenLoader(true));
    try {
      await updateStoreNew(objToSave);
      console.log("Changes saved successfully");
      setSaveConfirmOpen(false);
      handleCloseDialog();
      setChangedTables({});

      await fetchStoresData(page, limit, filters ?? undefined);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "Store updated successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error saving changes:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to update store",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while saving changes",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  // const handleRemoveTable = (tableName: string) => {
  //   dispatch(removeTabData(tableName));
  //   setChangedTables((prevTables) => {
  //     const newTables = { ...prevTables };
  //     delete newTables[tableName];
  //     return newTables;
  //   });
  // };

  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSortClose = () => {
    setAnchorEl(null);
  };

  const handleViewTableClick = () => {
    // navigate("/store-logistic-manager/member-table")
    window.open("/store-member-table", "_blank");
  };

  const handleSortOptionClick = (newSortBy: string, newSortOrder: "asc" | "desc") => {
    dispatch(setSortBy(newSortBy));
    dispatch(setSortOrder(newSortOrder));
    handleSortClose();
  };

  const openDropdown = Boolean(anchorEl);
  const id = openDropdown ? "sort-popover" : undefined;

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 5rem)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                {tablename} -
              </Typography>
              <SearchBar
                searchText={searchText}
                setSearchText={text => dispatch(setSearchText(text))}
                handleSearch={handleSearch}
                handleClearSearch={handleClearSearch}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                <StyledButton variant="outlined" color="primary" onClick={handleViewTableClick} startIcon={<BackupTableIcon />} aria-describedby={id}>
                  View Table
                </StyledButton>
                <StyledButton variant="outlined" color="primary" onClick={handleSortClick} startIcon={<SortIcon />} aria-describedby={id}>
                  Sort
                </StyledButton>
                <Popover
                  id={id}
                  open={openDropdown}
                  anchorEl={anchorEl}
                  onClose={handleSortClose}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  PaperProps={{
                    sx: {
                      borderRadius: "12px",
                      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
                      padding: "16px",
                    },
                  }}
                >
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Sort By
                  </Typography>
                  <Grid container spacing={2} sx={{ width: "300px" }}>
                    {[
                      { label: "Site Number", value: "SQL ID" },
                      { label: "Store Name", value: "Store Name" },
                      { label: "Store Code", value: "Store Code" },
                      { label: "State", value: "State" },
                      { label: "Region", value: "Region" },
                      { label: "District", value: "District" },
                      { label: "RM", value: "RM" },
                      { label: "DM", value: "DM" },
                    ].map(option => (
                      <Grid item xs={12} key={option.value}>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            borderRadius: "8px",
                            overflow: "hidden",
                          }}
                        >
                          <StyledSortButton
                            onClick={() => handleSortOptionClick(option.value, "asc")}
                            selected={sortBy === option.value && sortOrder === "asc"}
                          >
                            {option.label} ↑
                          </StyledSortButton>
                          <StyledSortButton
                            onClick={() => handleSortOptionClick(option.value, "desc")}
                            selected={sortBy === option.value && sortOrder === "desc"}
                          >
                            {option.label} ↓
                          </StyledSortButton>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Popover>
                <StyledButton variant="outlined" color="primary" onClick={handleToggleFilters} startIcon={<FilterListIcon />}>
                  {showFilters ? "Hide Filters" : "Show Filters"}
                </StyledButton>
                {/* {isStorePageAdmin(userRole) && (
                  <StyledButton variant="contained" color="primary" onClick={handleAddNewStore} startIcon={<AddIcon />}>
                    Add New Entry
                  </StyledButton>
                )} */}
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Box></Box>
            <Box
              sx={{
                p: 1.5,
                backgroundColor: alpha(theme.palette.info.light, 0.1),
                borderRadius: 1,
                border: "1px solid",
                borderColor: alpha(theme.palette.info.main, 0.2),
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
                mt: 1,
                width: "fit-content",
                flexWrap: "wrap",
              }}
            >
              <InfoOutlinedIcon color="info" fontSize="small" sx={{ mt: 0.3 }} />
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, color: "text.primary" }}>
                  To add any new employee or make changes to any table, visit the view table section.
                </Typography>
              </Box>
            </Box>
          </Box>

          <FilterSection
            showFilters={showFilters}
            config={config}
            selectedRegions={selectedRegions}
            selectedDistricts={selectedDistricts}
            selectedMarkets={selectedMarkets}
            selectedStoreConcepts={selectedStoreConcepts}
            selectedStoreNames={selectedStoreNames}
            selectedGeographies={selectedGeographies}
            selectedIsClosed={selectedIsClosed}
            setSelectedRegions={regions => dispatch(setSelectedRegions(regions))}
            setSelectedDistricts={districts => dispatch(setSelectedDistricts(districts))}
            setSelectedMarkets={markets => dispatch(setSelectedMarkets(markets))}
            setSelectedGeographies={geographies => dispatch(setSelectedGeographies(geographies))}
            setSelectedStoreNames={storeNames => dispatch(setSelectedStoreNames(storeNames))}
            setSelectedStoreConcepts={storeConcepts => dispatch(setSelectedStoreConcepts(storeConcepts))}
            setSelectedIsClosed={isClosed => dispatch(setSelectedIsClosed(isClosed))}
            handleResetFilter={handleResetFilter}
            handleApplyFilter={handleApplyFilter}
          />
        </StyledPaper>

        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <StoreTable
            key={new Date().getTime()}
            stores={stores}
            loading={loading}
            limit={limit}
            HeaderKeys={[
              "Actions",
              "SQL ID",
              "storeName",
              "Store Number",
              "State",
              "Store Code",
              "Legacy Store ID",
              "Comp",
              "CohortName",
              "CompCohort",
              "IsClosed",
              "pricingtierid",
              "pricingTierName",
              "Store",
              "Phone",
              "siteAddress1",
              "siteAddress2",
              "city",
              "stateAbbreviation",
              "zip",
              "storeConceptName",
              "Open Year",
              "openDate",
              "marketName",
              "geographyName",
              "Region",
              "regionName",
              "RM",
              "District",
              "districtName",
              "DM",
            ]}
            Header={[
              // "Actions",
              "Site Number",
              "Store Name",
              "Store Number",
              "State",
              "Store Code",
              "Legacy Store ID",
              "Comp",
              "Cohort Name",
              "Comp Cohort",
              "Is Closed",
              "pricingtierid",
              "pricingTierName",
              "Store",
              "Phone",
              "Site Address 1",
              "Site Address 2",
              "City",
              "State Abbreviation",
              "Zip",
              "Store Concept Name",
              "Open Year",
              "Open Date",
              "Market Name",
              "Geography Name",
              "Region",
              "RegionName",
              "RM",
              "District",
              "districtName",
              "DM",
            ]}
          />
        </Box>

        <Footer
          limit={limit}
          totalPages={totalPages}
          page={page}
          count={stores.length}
          handleLimitChange={handleLimitChange}
          handlePageChange={handlePageChange}
        />
      </Box>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="xl" fullWidth>
        <DialogTitle>{isAddMode ? "Add Store" : "Store Details"}</DialogTitle>
        <DialogContent dividers>
          {isAddMode ? (
            <>
              <AddStoreTabs />
              <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
                <Button disabled={isSaving} onClick={handleCloseDialog} sx={{ mr: 1 }}>
                  Cancel
                </Button>
                {isStorePageAdmin(userRole) && (
                  <Button disabled={isSaving} onClick={handleNewStoreSaveChanges} variant="contained" color="primary">
                    Submit New Store
                  </Button>
                )}
              </Box>
            </>
          ) : (
            <StoreDetailsTabs
              storeId={formData["SQL ID"]}
              isSaving={isSaving}
              closeBtnCallback={handleCloseDialog}
              saveBtnCallback={handleUpdateStoreSaveChanges}
            />
          )}
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={confirmDialogOpen}
        title={isAddMode ? "Confirm Add Store" : "Confirm Update Store"}
        content={`Are you sure you want to ${isAddMode ? "add" : "update"} this store?`}
        onConfirm={async () => {}}
        onCancel={handleCancelConfirm}
        isLoading={confirmDialogLoading}
        error={confirmDialogError}
        success={confirmDialogSuccess}
      />
      <Dialog open={saveConfirmOpen} onClose={() => setSaveConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{isAddMode ? "Add New Store" : "Confirm Changes"}</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            {isAddMode ? "You are about to add new store" : "You are about to make changes to the following tables"}
          </Typography>
          <List
            sx={{
              mt: 2,
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
              gap: 2,
            }}
          >
            {Object.entries(changedTables).map(([tableName]) => (
              <ListItem
                key={tableName}
                sx={{
                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    transform: "translateY(-2px)",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: "0.9rem",
                    letterSpacing: "0.5px",
                    color: "primary.light",
                  }}
                >
                  {formatTableName(tableName)}
                </Typography>
                {/* <IconButton
                  disabled={isSaving}
                  edge="end"
                  aria-label="delete"
                  onClick={() => handleRemoveTable(tableName)}
                  sx={{
                    color: "error.light",
                    opacity: 0.7,
                    transition: "all 0.2s ease",
                    "&:hover": {
                      opacity: 1,
                      transform: "scale(1.1)",
                    },
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton> */}
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );
};

const StyledSortButton = styled(Button)<{ selected: boolean }>(({ theme, selected }) => ({
  flex: 1,
  padding: "8px 16px",
  borderRadius: 0,
  backgroundColor: selected ? theme.palette.primary.main : "transparent",
  color: selected ? theme.palette.primary.contrastText : theme.palette.text.primary,
  "&:hover": {
    backgroundColor: selected ? theme.palette.primary.dark : theme.palette.action.hover,
  },
  transition: "all 0.2s ease-in-out",
}));

export default StoreManagerPage;

function formatTableName(tableName: string): string {
  return tableName
    .split("_")
    .map(word => {
      if (word.toLowerCase() === "str") {
        return "STR";
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
}
