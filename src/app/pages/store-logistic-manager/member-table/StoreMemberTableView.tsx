import React, { useState, useEffect, use<PERSON>allback, ChangeEvent } from "react";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Box, Autocomplete, Typography, Grid, CircularProgress, Backdrop, Button, Dialog, DialogActions, DialogContent, DialogTitle } from "@mui/material";
import { StyledTextField } from "../../../CustomComponents/StyledComponents";
import { StoreMemberTableName } from "../../../constants/constants";
import SearchBar from "../../../CustomComponents/SearchBar";
import { fetchStoreMemberTableData, insertTableData, IDENTITY_KEYS, NOT_NULL, PRIMARY_KEYS } from "../../../api/storeMemberTableAPI";
import { checkExistingValueInStore } from "../../../api/storeLogisticManagerApi";
import StoreMemberTable from "../../../CustomComponents/StoreMemberTable";
import { addSpaceToCamelCase } from "../../ai/components/utils";
import Footer from "../../../CustomComponents/Footer";
import { useDispatch, useSelector } from "react-redux";
import { setFullScreenLoader } from "../../../../__redux/generalSlice";
import { AddStoreFormObjectRule } from "../../../utils/formObjectRules";
import { AddStoreFormFieldType } from "../../../utils/formFieldTypes";
import AddIcon from "@mui/icons-material/Add";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { RootState } from "../../../store";
import { isStorePageAdmin } from "../../../utils/roleUtils";

import { alpha } from "@mui/material/styles";
import InsertFormField from "../../../CustomComponents/InsertFormField";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const StoreMemberTableView = () => {
  const dispatch = useDispatch();
  const { userRole } = useSelector((state: RootState) => state.auth);
  const isAdmin = isStorePageAdmin(userRole);

  const [selectedTableName, setSelectedTableName] = useState<string | null>(null);
  const [searchText, setSearchText] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [shouldCallAPI, setShouldCallAPI] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(15);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [tableData, setTableData] = useState<Record<string, string>[]>([]);
  const [columnNames, setColumnNames] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchColumns, setSearchColumns] = useState<string[]>([]);
  const [storedColumnNames, setStoredColumnNames] = useState<string[]>([]);

  // Add new states for insert dialog
  const [insertDialogOpen, setInsertDialogOpen] = useState<boolean>(false);
  const [newRecordData, setNewRecordData] = useState<Record<string, string>>({});
  const [isInserting, setIsInserting] = useState<boolean>(false);
  const [insertErrors, setInsertErrors] = useState<Record<string, string>>({});
  const [insertSuccess, setInsertSuccess] = useState<boolean>(false);

  // Add a cache for previously checked values
  const [checkedValues, setCheckedValues] = useState<Record<string, Record<string, boolean>>>({});

  // Add new state for history table check
  const isHistoryTable = selectedTableName === "Employee History";

  const fetchTableData = useCallback(
    async (page: number, limit: number, tableName: string, searchText?: string, searchColumns?: string[]) => {
      setLoading(true);
      setError(null);

      if (tableData.length === 0) {
        dispatch(setFullScreenLoader(true));
      }

      try {
        if (!tableName) {
          throw new Error("Table name is required");
        }

        const data = await fetchStoreMemberTableData(page, limit, tableName, searchText, searchColumns);

        if (!data || !Array.isArray(data.result)) {
          throw new Error("Invalid data received from server");
        }

        // remove createDate from the data
        const newData = data.result.map((item: Record<string, string>) => {
          delete item.createDate;
          return item;
        });

        setTableData(newData);
        setTotalPages(data.totalPages || 1);

        if (data.result.length > 0) {
          const columnKeys = Object.keys(data.result[0]);
          setColumnNames(columnKeys);
          setStoredColumnNames(columnKeys);
        } else {
          setColumnNames(storedColumnNames);
        }
      } catch (error) {
        console.error("Error fetching stores:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
        setTableData([]);
      } finally {
        setLoading(false);
        setShouldCallAPI(false);
        dispatch(setFullScreenLoader(false));
      }
    },
    [dispatch, tableData.length, storedColumnNames]
  );

  useEffect(() => {
    if (!shouldCallAPI) {
      return;
    }

    if (!selectedTableName) {
      return;
    }

    const tableName = StoreMemberTableName[selectedTableName];

    if (searchText !== "") {
      const columnsToSearch = searchColumns.length > 0 ? searchColumns : columnNames;
      fetchTableData(page, limit, tableName, searchText, columnsToSearch);
    } else {
      fetchTableData(page, limit, tableName);
    }
  }, [fetchTableData, shouldCallAPI, selectedTableName, searchText, searchColumns, page, limit, columnNames]);

  const handleSearch = async () => {
    if (!selectedTableName) {
      return;
    }

    setPage(1);
    setLoading(true);
    const columnsToSearch = searchColumns.length > 0 ? searchColumns : columnNames;
    await fetchTableData(1, limit, StoreMemberTableName[selectedTableName], searchText, columnsToSearch);
    setLoading(false);
  };

  const handleClearSearch = async () => {
    if (!selectedTableName) {
      return;
    }

    setSearchText("");
    setSearchColumns([]);
    setPage(1);
    setLoading(true);
    await fetchTableData(1, limit, StoreMemberTableName[selectedTableName]);
    setLoading(false);
  };

  const handleLimitChange = (_event: Event, newValue: number | number[]) => {
    const value = Array.isArray(newValue) ? newValue[0] : newValue;
    setLimit(value === 100 ? 1000 : value);
    setPage(1);
    setShouldCallAPI(true);
  };

  const handlePageChange = (_event: ChangeEvent<unknown>, value: number) => {
    setPage(value);
    setShouldCallAPI(true);
  };

  const resetPage = () => {
    setTotalPages(1);
    setPage(1);
    setLimit(15);
    setSearchText("");
    setSearchColumns([]);
    setTableData([]);
    setColumnNames([]);
    setStoredColumnNames([]);
    setError(null);
  };

  const handleTableNameChange = (_: unknown, newValue: string | null) => {
    setSelectedTableName(newValue);
    setShouldCallAPI(true);
    resetPage();
  };

  // Get available columns for search selector
  const getAvailableColumns = (): string[] => {
    return storedColumnNames.length > 0 ? storedColumnNames : columnNames;
  };

  const headers = tableData.length > 0 ? Object.keys(tableData[0]).map(key => addSpaceToCamelCase(key)) : [];
  const HeaderKeys = tableData.length > 0 ? Object.keys(tableData[0]) : storedColumnNames;

  const getFieldType = (key: string): string => {
    if (AddStoreFormObjectRule[key]) {
      return AddStoreFormObjectRule[key].fieldType;
    }
    // Default to TEXT if field type not found
    return AddStoreFormFieldType.TEXT;
  };

  // Set primary key column based on the selected table
  const primaryKeyColumn =
    selectedTableName && IDENTITY_KEYS[StoreMemberTableName[selectedTableName]] ? IDENTITY_KEYS[StoreMemberTableName[selectedTableName]][0] : "";

  const fieldTypes =
    tableData.length > 0
      ? Object.keys(tableData[0]).reduce((acc, key) => {
          const fieldType = getFieldType(key);
          acc[key] = fieldType;
          return acc;
        }, {} as Record<string, string>)
      : {};

  // Format field keys for display
  const formatFieldKey = (key: string): string => {
    return key
      .replace(/([A-Z])/g, " $1") // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
  };

  // Helper function to check if a field is an identity column
  const isIdentityField = (key: string): boolean => {
    if (!selectedTableName) return false;
    const tableName = StoreMemberTableName[selectedTableName];
    return IDENTITY_KEYS[tableName]?.includes(key) || false;
  };

  // Helper function to check if a field is required (not null)
  const isRequiredField = (key: string): boolean => {
    if (!selectedTableName) return false;
    const tableName = StoreMemberTableName[selectedTableName];
    return NOT_NULL[tableName]?.includes(key) || false;
  };

  // Helper function to check if a field is a primary key
  const isPrimaryKeyField = (key: string): boolean => {
    if (!selectedTableName) return false;
    const tableName = StoreMemberTableName[selectedTableName];
    return PRIMARY_KEYS[tableName]?.includes(key) || false;
  };

  // Function to handle data refresh after an update
  const handleDataUpdate = useCallback(() => {
    if (selectedTableName) {
      setShouldCallAPI(true);
    }
  }, [selectedTableName]);

  // Function to handle opening the insert dialog
  const handleOpenInsertDialog = () => {
    // Prevent opening if it's a history table
    if (isHistoryTable) return;

    // Initialize form with empty values based on table structure
    if (tableData.length > 0) {
      const templateRecord: Record<string, string> = {};

      // Extract keys from the first record
      for (const key of HeaderKeys) {
        // Skip auto-generated identity fields
        if (isIdentityField(key)) {
          continue;
        }

        // For date fields, set default values
        if (["validFrom", "validTo"].includes(key)) {
          if (key === "validFrom") {
            templateRecord[key] = new Date().toISOString().split("T")[0];
          } else if (key === "validTo") {
            // Set validTo to 10 years in the future by default
            const futureDate = new Date();
            futureDate.setFullYear(futureDate.getFullYear() + 10);
            templateRecord[key] = futureDate.toISOString().split("T")[0];
          }
        } else if (key === "isActive" || key === "isClosed" || key === "showReporting") {
          // Set boolean fields to default values
          templateRecord[key] = "true";
        } else {
          templateRecord[key] = "";
        }
      }

      setNewRecordData(templateRecord);
    }

    setInsertDialogOpen(true);
  };

  // Function to handle closing the insert dialog
  const handleCloseInsertDialog = () => {
    setInsertDialogOpen(false);
    setNewRecordData({});
    setInsertErrors({});
    setInsertSuccess(false);
  };

  // Function to handle changes in the insert form
  const handleInsertInputChange = (key: string, value: string) => {
    // Special handling for primary key fields that are being cleared
    const isPrimary = isPrimaryKeyField(key);
    const isBeingCleared = value === "" && newRecordData[key] !== "";

    // Clear any errors for this field
    if (insertErrors[key] || (isPrimary && isBeingCleared)) {
      setInsertErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });

      // If clearing a primary key, also clear from cache
      if (isPrimary && isBeingCleared && selectedTableName) {
        const tableName = StoreMemberTableName[selectedTableName];
        setCheckedValues(prev => {
          if (!prev[tableName]) return prev;

          const tableCache = { ...prev[tableName] };
          // Remove any previous checks for this key
          Object.keys(tableCache).forEach(cacheKey => {
            if (cacheKey.startsWith(`${key}_`)) {
              delete tableCache[cacheKey];
            }
          });

          return {
            ...prev,
            [tableName]: tableCache,
          };
        });
      }
    }

    setNewRecordData(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Function to handle checking if a primary key value already exists
  const handleExistingValueCheck = async (key: string, value: string) => {
    if (!selectedTableName) return;

    // Handle empty value by clearing any existing errors
    if (!value || value.trim() === "") {
      if (insertErrors[key]) {
        setInsertErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[key];
          return newErrors;
        });
      }

      // Also update cache to indicate this field doesn't have a value
      if (checkedValues[StoreMemberTableName[selectedTableName]]) {
        setCheckedValues(prev => {
          const tableName = StoreMemberTableName[selectedTableName];
          if (!prev[tableName]) return prev;

          const tableCache = { ...prev[tableName] };
          // Remove any previous checks for this key
          Object.keys(tableCache).forEach(cacheKey => {
            if (cacheKey.startsWith(`${key}_`)) {
              delete tableCache[cacheKey];
            }
          });

          return {
            ...prev,
            [tableName]: tableCache,
          };
        });
      }

      return false;
    }

    try {
      const tableName = StoreMemberTableName[selectedTableName];

      // Check if we already have this value in cache
      if (checkedValues[tableName] && checkedValues[tableName][`${key}_${value}`] !== undefined) {
        const exists = checkedValues[tableName][`${key}_${value}`];

        if (exists) {
          setInsertErrors(prev => ({
            ...prev,
            [key]: `This ${formatFieldKey(key)} already exists`,
          }));
        } else if (insertErrors[key]?.includes("already exists")) {
          // Clear the error if it was previously set
          setInsertErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[key];
            return newErrors;
          });
        }

        return exists;
      }

      // If not in cache, call API
      const response = await checkExistingValueInStore(tableName, key, value, "dbo");

      // Update cache
      setCheckedValues(prev => ({
        ...prev,
        [tableName]: {
          ...(prev[tableName] || {}),
          [`${key}_${value}`]: response.exists,
        },
      }));

      if (response.exists) {
        setInsertErrors(prev => ({
          ...prev,
          [key]: `This ${formatFieldKey(key)} already exists`,
        }));
      } else if (insertErrors[key]?.includes("already exists")) {
        // Clear the error if the value doesn't exist
        setInsertErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[key];
          return newErrors;
        });
      }

      return response.exists;
    } catch (error) {
      console.error("Error checking existing value:", error);
      return false;
    }
  };

  // Clear cache when table changes
  useEffect(() => {
    setCheckedValues({});
  }, [selectedTableName]);

  // Function to validate the new record before insertion
  const validateInsertForm = (): boolean => {
    if (!selectedTableName) return false;

    const errors: Record<string, string> = {};
    const tableName = StoreMemberTableName[selectedTableName];

    // Get the not-null fields for this table
    const requiredFields = NOT_NULL[tableName] || [];

    for (const field of requiredFields) {
      // Skip identity fields which are auto-generated
      if (isIdentityField(field)) continue;

      const value = newRecordData[field];
      if (value === undefined || value === null || value === "") {
        errors[field] = "This field is required";
      }
    }

    // Check if there are any existing primary key errors
    Object.keys(insertErrors).forEach(key => {
      if (insertErrors[key].includes("already exists")) {
        errors[key] = insertErrors[key];
      }
    });

    setInsertErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Function to handle the insert operation
  const handleInsertRecord = async () => {
    if (!selectedTableName) return;

    // Validate form
    if (!validateInsertForm()) {
      setInsertSuccess(false);
      return;
    }

    try {
      setIsInserting(true);
      const tableName = StoreMemberTableName[selectedTableName];

      // Filter out any identity fields from the data
      const dataToInsert: Record<string, unknown> = {};
      Object.entries(newRecordData).forEach(([key, value]) => {
        if (!isIdentityField(key) && value !== "") {
          dataToInsert[key] = value;
        }
      });

      await insertTableData(tableName, dataToInsert);

      setInsertSuccess(true);

      // Close dialog and refresh data after short delay
      setTimeout(() => {
        handleCloseInsertDialog();
        setShouldCallAPI(true);
      }, 1500);
    } catch (error) {
      console.error("Error inserting record:", error);
      setInsertSuccess(false);
      setError(error instanceof Error ? error.message : "Failed to insert record");
    } finally {
      setIsInserting(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      {/* Loading backdrop overlay to prevent user interaction */}
      <Backdrop
        sx={{
          color: "#fff",
          zIndex: theme.zIndex.drawer + 1,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backdropFilter: "blur(3px)",
        }}
        open={loading}
      >
        <CircularProgress color="inherit" size={70} thickness={5} />
        <Typography variant="h6" sx={{ mt: 2, color: "white", fontWeight: 500 }}>
          Loading data...
        </Typography>
      </Backdrop>

      <Box
        sx={{
          height: "calc(100vh - 8%)",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          opacity: loading ? 0.5 : 1,
          pointerEvents: loading ? "none" : "auto",
          transition: "opacity 0.3s ease",
        }}
      >
        {/* Header Section Start */}
        <div style={{ display: "flex", flexDirection: "column", gap: 2, marginLeft: 10, marginRight: 10, marginBottom: 10 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              gap: "10%",
              marginLeft: 10,
              marginRight: 10,
            }}
          >
            <Box>
              <Typography variant="h5" gutterBottom>
                Selected Store Table Name
              </Typography>
              <Autocomplete
                fullWidth
                sx={{ width: 220 }}
                options={Object.keys(StoreMemberTableName).sort((a, b) => (a < b ? -1 : 1))}
                value={selectedTableName}
                onChange={handleTableNameChange}
                disabled={loading}
                renderInput={params => <StyledTextField {...params} label="" variant="outlined" placeholder="Please Select Table Name" />}
              />
            </Box>

            <Box
              sx={{
                p: 1.5,
                backgroundColor: alpha(theme.palette.info.light, 0.1),
                borderRadius: 1,
                border: "1px solid",
                borderColor: alpha(theme.palette.info.main, 0.2),
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
              }}
            >
              <InfoOutlinedIcon color="info" fontSize="small" sx={{ mt: 0.3 }} />
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5, color: "text.primary" }}>
                  How to Update Store Managers:
                </Typography>
                <Typography variant="body1" sx={{ color: "text.secondary", fontSize: "0.8rem" }}>
                  1. Check if employee exists. Add new if not found.
                </Typography>
                <Typography variant="body1" sx={{ color: "text.secondary", fontSize: "0.8rem" }}>
                  2. Update employeeID in District and Region tables.
                </Typography>
                <Typography variant="body1" sx={{ color: "text.secondary", fontSize: "0.8rem", fontStyle: "italic", mt: 0.5 }}>
                  Note: Copy generated EmpID after adding employee. Use existing empID for existing employees.
                </Typography>
              </Box>
            </Box>
          </div>

          {/* Top Border */}
          <div className="bg-secondary" style={{ width: "100%", height: 3, marginTop: 10, marginBottom: 10 }}></div>

          {/* Header Section End */}

          {/* Search Bar and Add New Button */}
          {selectedTableName !== null && (
            <Grid container spacing={2} sx={{ px: 2 }}>
              <Grid
                item
                xs={12}
                md={8}
                sx={{
                  display: "flex",
                  justifyContent: "flex-start",
                  alignItems: "center",
                  gap: 2,
                }}
              >
                <Typography
                  variant="h4"
                  sx={{
                    flexShrink: 0,
                    whiteSpace: "nowrap",
                    fontWeight: 500,
                    letterSpacing: "0.05em",
                    color: "primary.main",
                  }}
                >
                  {selectedTableName} -
                </Typography>
                <SearchBar
                  searchText={searchText}
                  setSearchText={setSearchText}
                  handleSearch={handleSearch}
                  handleClearSearch={handleClearSearch}
                  disabled={loading}
                  showFieldSelector={true}
                  searchColumns={searchColumns}
                  availableColumns={getAvailableColumns()}
                  setSearchColumns={setSearchColumns}
                />
              </Grid>

              {/* Add New Record Button */}
              {isAdmin && selectedTableName !== null && !isHistoryTable && (
                <Grid item xs={12} md={4} sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleOpenInsertDialog}
                    disabled={loading || tableData.length === 0}
                    sx={{
                      borderRadius: 2,
                      fontWeight: 600,
                      boxShadow: 3,
                      px: 3,
                      py: 1,
                      marginRight: "10%",
                      "&:hover": {
                        boxShadow: 5,
                      },
                    }}
                  >
                    Add New Record
                  </Button>
                </Grid>
              )}

              {/* Display warning message for history tables */}
              {isHistoryTable && (
                <Grid item xs={12} md={4} sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <Box
                    sx={{
                      p: 1.5,
                      backgroundColor: alpha(theme.palette.warning.light, 0.2),
                      borderRadius: 2,
                      border: "1px solid",
                      borderColor: alpha(theme.palette.warning.main, 0.3),
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      marginRight: "10%",
                    }}
                  >
                    <InfoOutlinedIcon color="warning" fontSize="small" />
                    <Typography variant="body2" sx={{ fontWeight: 500, color: "text.secondary" }}>
                      Employee History tables is read-only
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          )}
        </div>

        {/* Error message display */}
        {error && (
          <Box sx={{ p: 2, bgcolor: "error.light", color: "error.contrastText", mx: 2, borderRadius: 1 }}>
            <Typography variant="body1">Error: {error}</Typography>
          </Box>
        )}

        {/* Table content or empty state */}
        {selectedTableName !== null && tableData.length > 0 && (
          <>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
              <StoreMemberTable
                tableData={tableData}
                loading={loading}
                limit={limit}
                Header={headers}
                HeaderKeys={HeaderKeys}
                fieldTypes={fieldTypes}
                tableName={StoreMemberTableName[selectedTableName]}
                primaryKeyColumn={primaryKeyColumn}
                onDataUpdate={handleDataUpdate}
                readOnly={isHistoryTable}
              />
            </Box>
            <Footer
              limit={limit}
              totalPages={totalPages}
              page={page}
              count={tableData.length}
              handleLimitChange={handleLimitChange}
              handlePageChange={handlePageChange}
              disabled={loading}
            />
          </>
        )}
        {selectedTableName === null && (
          <Box display="flex" flexDirection="column" alignItems="center" py={4}>
            <Typography variant="h6" color="textSecondary">
              Please select a table name from the dropdown list
            </Typography>
          </Box>
        )}
        {selectedTableName !== null && tableData.length === 0 && !loading && !error && (
          <Box display="flex" flexDirection="column" alignItems="center" py={4}>
            <Typography variant="h6" color="textSecondary">
              No data found for the selected table
            </Typography>
          </Box>
        )}
      </Box>

      {/* Insert Dialog */}
      <Dialog
        open={insertDialogOpen}
        onClose={isInserting ? undefined : handleCloseInsertDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            overflow: "hidden",
          },
        }}
      >
        <DialogTitle
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            p: 3,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            bgcolor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
          }}
        >
          <Box>
            <Typography variant="h6" component="span">
              Add New {selectedTableName ? selectedTableName.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) : "Record"}
            </Typography>
            {selectedTableName && (
              <Typography variant="caption" color="inherit" display="block" sx={{ opacity: 0.8 }}>
                Table: {StoreMemberTableName[selectedTableName]}
              </Typography>
            )}
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {isInserting && (
            <Box
              sx={{
                position: "absolute",
                zIndex: 9999,
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: alpha(theme.palette.background.paper, 0.5),
                left: 0,
                top: 0,
              }}
            >
              <Box sx={{ p: 3, display: "flex", alignItems: "center", flexDirection: "column", gap: 2 }}>
                {insertSuccess ? (
                  <>
                    <Typography variant="h6" color="success.main">
                      Record added successfully!
                    </Typography>
                    <CircularProgress color="success" />
                  </>
                ) : (
                  <>
                    <CircularProgress size={40} />
                    <Typography variant="h6">Adding new record...</Typography>
                  </>
                )}
              </Box>
            </Box>
          )}

          <Grid container spacing={2} sx={{ mt: 1 }}>
            {Object.keys(newRecordData).map(key => {
              if (key === "createDate") {
                return null;
              }

              // Skip identity fields in the form
              if (isIdentityField(key)) return null;

              const fieldType = getFieldType(key);
              const isRequired = isRequiredField(key);
              const isPrimary = isPrimaryKeyField(key);

              return (
                <Grid item xs={12} sm={6} key={key}>
                  <InsertFormField
                    fieldKey={key}
                    fieldType={fieldType}
                    value={newRecordData[key] || ""}
                    onChange={value => handleInsertInputChange(key, value)}
                    isRequired={isRequired}
                    error={insertErrors[key] || ""}
                    formatFieldKey={formatFieldKey}
                    disabled={isInserting}
                    isPrimaryKey={isPrimary}
                    tableName={selectedTableName ? StoreMemberTableName[selectedTableName] : ""}
                    onExistingValueCheck={handleExistingValueCheck}
                  />
                </Grid>
              );
            })}
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            p: 3,
            borderTop: 1,
            borderColor: "divider",
            backgroundColor: alpha(theme.palette.background.default, 0.5),
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Box>
            <Typography variant="caption" color="error">
              * Required fields
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button onClick={handleCloseInsertDialog} variant="outlined" disabled={isInserting} sx={{ borderRadius: 2 }}>
              Cancel
            </Button>
            <Button
              onClick={handleInsertRecord}
              variant="contained"
              color="primary"
              disabled={isInserting}
              sx={{
                borderRadius: 2,
                minWidth: 100,
                fontWeight: 600,
              }}
            >
              {isInserting ? "Adding..." : "Add Record"}
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );
};

export default StoreMemberTableView;
