export enum AddStoreFormFieldType {
  TEXT,
  YES_NO,
  NUMERIC,
  DATE,
  EMAIL,
  DROP_DOWN,
}

export interface FormObjectValidation {
  helperText: string;
  maxInputLimit: number;
  isPrimaryKey: boolean;
  fieldType: AddStoreFormFieldType;
  isDisabled?: boolean;
  isRequired?: boolean;
  options?: { value: string | number; label: string }[];
  staticPrefix?: string;
}

export const AddStoreFormObjectRule: Record<string, FormObjectValidation> = {
  // TABLE:: str_store
  storeId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  companyId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  siteId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  posId: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  loyaltyId: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  storeConceptId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  dataProviderId: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  storeNumber: { helperText: "", maxInputLimit: 20, isPrimaryKey: true, fieldType: AddStoreFormFieldType.TEXT },
  storeName: { helperText: "", maxInputLimit: 100, isPrimaryKey: true, fieldType: AddStoreFormFieldType.TEXT },
  openDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  closedDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  showReporting: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  compareDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  isClosed: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  validFrom: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  validTo: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  marketId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  legacyStoreCode: { helperText: "", maxInputLimit: 2, isPrimaryKey: true, fieldType: AddStoreFormFieldType.TEXT },
  legacyStoreId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  storePhone: { helperText: "", maxInputLimit: 10, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  geographyId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  isComp: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  upcomingCompDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  districtId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  cohortId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },

  // TABLE:: str_site
  // "siteId" already exist under str_store section
  siteName: { helperText: "", maxInputLimit: 20, isPrimaryKey: true, fieldType: AddStoreFormFieldType.TEXT },
  siteAddress1: { helperText: "", maxInputLimit: 100, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  siteAddress2: { helperText: "", maxInputLimit: 100, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  city: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  state: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  stateAbbreviation: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  zip: { helperText: "", maxInputLimit: 6, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  squareFootage: { helperText: "", maxInputLimit: 5, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  latitude: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  longitude: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  projectedOpenDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  // "geographyPoint" filed is hidden on the UI
  // "validFrom" & "validTo" already exist under str_store section

  //  TABLE:: str_storeConcept
  // "storeConceptId" "validFrom" & "validTo" already exist under str_store section
  storeConceptName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },

  // TABLE:: str_market
  // "marketId" "validFrom" & "validTo" already exist under str_store section
  marketName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },

  // TABLE:: str_district
  districtid: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  districtName: { helperText: "", maxInputLimit: 20, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  // "validFrom" & "validTo" already exist under str_store section

  // TABLE:: str_regionDistrict
  regionDistrictId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  regionId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  // "validFrom" & "validTo" already exist under str_store section

  // TABLE:: str_region
  // "validFrom" & "validTo" already exist under str_store section
  regionid: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  regionName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },

  // TABLE:: str_geography
  // "geographyId" "validFrom" & "validTo" already exist under str_store section
  geographyName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },

  // TABLE:: emp_employee
  employeeId: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  employeeNumber: { helperText: "", maxInputLimit: 4, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
  firstName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  lastName: { helperText: "", maxInputLimit: 20, isPrimaryKey: false, fieldType: AddStoreFormFieldType.TEXT },
  email: { helperText: "", maxInputLimit: 100, isPrimaryKey: false, fieldType: AddStoreFormFieldType.EMAIL },
  departmentId: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  jobId: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  hireDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  termDate: { helperText: "", maxInputLimit: 8, isPrimaryKey: false, fieldType: AddStoreFormFieldType.DATE },
  isActive: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.YES_NO },
  managerEmployeeNumber: { helperText: "", maxInputLimit: 4, isPrimaryKey: false, fieldType: AddStoreFormFieldType.NUMERIC },
  phone: { helperText: "", maxInputLimit: 10, isPrimaryKey: true, fieldType: AddStoreFormFieldType.NUMERIC },
};
