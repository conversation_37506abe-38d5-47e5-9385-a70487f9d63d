import {
  Box,
  Button,
  CircularProgress,
  Fade,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Step,
  StepButton,
  Stepper,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import SaveIcon from "@mui/icons-material/Save";
import InfoIcon from "@mui/icons-material/Info";
import { setNewStore } from "../../../../__redux/addStoreSlice";
import { addSpaceToCamelCase } from "../../ai/components/utils";
import { AddStoreFormFieldType, FormObjectValidation, AddStoreFormObjectRule } from "./AddStoreHelper";
import { checkExistingValueInStore, StoreExistingValueResponse } from "../../../api/storeLogisticManagerApi";
import { StoreUIFieldName } from "./StoreDetailsTabs";
import debounce from "lodash/debounce";

type StoreEntry = Record<string, Record<string, any>>;
type FieldRule = { rule: Record<string, FormObjectValidation> };

export const AllowedTables = [
  "str_store",
  "str_site",
  "str_storeConcept",
  "str_district",
  "str_market",
  "str_regionDistrict",
  "str_region",
  "emp_employee",
  "str_geography",
];

//   interface AddStoreTabsProps {
//     storeId: number;
//   }

interface TabData {
  [key: string]:
    | string
    | number
    | boolean
    | null
    | Date
    | {
        latitude: number;
        longitude: number;
      };
}

export const formatTableName = (tableName: string): string => {
  return tableName
    .split("_")
    .map((word) => {
      if (word.toLowerCase() === "str") {
        return "STR";
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

const PRIMARY_KEYS = {
  str_geography: ["geographyId"],
  str_store: [
    "storeId",
    "siteId",
    "storeNumber",
    "storeName",
    "districtId",
    "storeConceptId",
    "marketId",
    "geographyId",
    "legacyStoreCode",
    "legacyStoreId",
  ],
  emp_employee: ["employeeId", "employeeNumber"],
  str_district: ["districtid"],
  str_market: ["marketId"],
  str_region: ["regionid"],
  str_regionDistrict: ["regionDistrictId", "regionId", "districtId"],
  str_storeConcept: ["storeConceptId"],
  str_site: ["siteId", "siteName"],
};

const AddStoreTabs: React.FC = () => {
  const dispatch = useDispatch();

  const { newStore } = useSelector((state: RootState) => state.addStore);
  const theme = useTheme();
  const [editedData, setEditedData] = useState<TabData>({});
  const [fieldRules, setFieldRules] = useState<FieldRule>({ rule: JSON.parse(JSON.stringify(AddStoreFormObjectRule)) });
  const [isLoading, setIsLoading] = useState(false);
  const [showSaveMessage, setShowSaveMessage] = useState(false);
  const [currentTab, setCurrentTab] = useState<number>(0);
  const [isDebouncing, setDebouncing] = useState<boolean>(false);

  useEffect(() => {
    setFieldRules({ rule: JSON.parse(JSON.stringify(AddStoreFormObjectRule)) });
    setEditedData(newStore[AllowedTables[currentTab]] || {});
  }, [currentTab]);

  const callExistingValueCheckAPI = async (key: string, value: any, tableName: string) => {
    if (value === null || isDebouncing) return;
    try {
      setDebouncing(true);
      const val = String(value);
      const response = await checkExistingValueInStore(tableName, key, val, "dbo");
      return response;
    } catch (error) {
      console.error("Error in calling existing value check api", error);
    } finally {
      setDebouncing(false);
    }
  };

  const handleNext = () => {
    if (currentTab < AllowedTables.length - 1) {
      setEditedData({});
      setCurrentTab(currentTab + 1);
    }
  };

  const handleBack = () => {
    if (currentTab > 0) {
      setEditedData({});
      setCurrentTab(currentTab - 1);
    }
  };

  const handleStepClick = (index: number) => {
    setEditedData({});
    setCurrentTab(index);
  };

  const handleSave = () => {
    console.log("editedData", editedData);
    const currentColumnNames = Object.keys(editedData);
    for (const key of currentColumnNames) {
      if (fieldRules.rule[key].helperText !== "") {
        window.alert("Please check all the fields, there are some wrong inputs");
        return;
      }
    }
    const tableDict: StoreEntry = { ...newStore };
    tableDict[AllowedTables[currentTab]] = editedData;
    dispatch(setNewStore(tableDict));
    setShowSaveMessage(true);
    setTimeout(() => setShowSaveMessage(false), 3000);
  };

  const handleInputChange = (key: string, value: string | number | boolean | Date | null) => {
    if (validateInputMaxLimit(key, value)) {
      // setEditedData((prev) => ({ ...prev, [key]: value }));
      value instanceof Date
        ? setEditedData((prev) => ({ ...prev, [key]: value.toISOString() }))
        : setEditedData((prev) => ({ ...prev, [key]: value }));

      // Check existing value on the server only for primary key
      handleDebounceValueChecking(key, value);
    }
  };

  const validateInputMaxLimit = (key: string, value: string | number | boolean | Date | null): boolean => {
    const rule = { ...fieldRules.rule[key] };

    switch (rule.fieldType) {
      case AddStoreFormFieldType.NUMERIC: {
        const isValid = Number(value).toString().length <= rule.maxInputLimit;
        return isValid;
      }
      case AddStoreFormFieldType.TEXT: {
        const isValid = String(value).length <= rule.maxInputLimit;
        return isValid;
      }
      default:
        break;
    }
    return true;
  };

  const validateExistingValue = async (key: string, value: string | number | boolean | Date | null) => {
    const rule = { ...fieldRules.rule[key] };
    if (!rule.isPrimaryKey) return;
    const currentTableName = AllowedTables[currentTab];
    const result = await callExistingValueCheckAPI(key, value, currentTableName);
    if (result) {
      const tempRules = { ...fieldRules };
      rule.helperText = result.exists ? "Value already exits" : "";
      tempRules.rule[key] = rule;
      setFieldRules(tempRules);
    } else if (value === null) {
      const tempRules = { ...fieldRules };
      rule.helperText = "";
      tempRules.rule[key] = rule;
      setFieldRules(tempRules);
    }
  };

  const handleDebounceValueChecking = useCallback(
    debounce(async (key: string, value: string | number | boolean | Date | null) => {
      await validateExistingValue(key, value);
    }, 400),
    []
  );

  const renderFormInputField = (key: string, value: any) => {
    const rule = fieldRules.rule[key];
    // Common props for all inputs
    const commonProps = {
      label: addSpaceToCamelCase(key),
      error: rule.helperText !== "",
      helperText: rule.helperText,
      fullWidth: true,
    };

    // Create UI
    switch (rule.fieldType) {
      case AddStoreFormFieldType.YES_NO:
        return (
          <FormControl fullWidth>
            <InputLabel id={`${key}-label`}>{addSpaceToCamelCase(key)}</InputLabel>
            <Select
              labelId={`${key}-label`}
              id={key}
              value={value === null ? null : value}
              onChange={(e) => handleInputChange(key, e.target.value ? e.target.value === "true" : null)}
              label={addSpaceToCamelCase(key)}
            >
              <MenuItem value="true">True</MenuItem>
              <MenuItem value="false">False</MenuItem>
            </Select>
          </FormControl>
        );

      case AddStoreFormFieldType.DATE:
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
              // disabled={PRIMARY_KEYS["str_store" as keyof typeof PRIMARY_KEYS]?.includes(key)}
              value={value ? new Date(value) : null}
              onChange={(v) => {
                handleInputChange(key, v);
              }}
              renderInput={(params) => (
                <TextField
                  {...commonProps}
                  {...params}
                  fullWidth
                  inputProps={{
                    ...params.inputProps,
                    readOnly: true,
                  }}
                />
              )}
            />
          </LocalizationProvider>
        );

      case AddStoreFormFieldType.NUMERIC:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
            type="number"
            sx={{
              "& .MuiInputLabel-root.Mui-error": {
                color: "#ff8804",
              },
              "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
                border: "3px solid #ff8804",
              },
              "& .MuiFormHelperText-root.Mui-error": {
                color: "#ff8804",
              },
            }}
          />
        );

      case AddStoreFormFieldType.TEXT:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, e.target.value)}
            sx={{
              "& .MuiInputLabel-root.Mui-error": {
                color: "#ff8804",
              },
              "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
                border: "3px solid #ff8804",
              },
              "& .MuiFormHelperText-root.Mui-error": {
                color: "#ff8804",
              },
            }}
          />
        );

      case AddStoreFormFieldType.EMAIL:
        return (
          <TextField
            {...commonProps}
            value={value || undefined}
            onChange={(e) => handleInputChange(key, e.target.value)}
            type="email"
            sx={{
              "& .MuiInputLabel-root.Mui-error": {
                color: "#ff8804",
              },
              "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
                border: "3px solid #ff8804",
              },
              "& .MuiFormHelperText-root.Mui-error": {
                color: "#ff8804",
              },
            }}
          />
        );
    }
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <CircularProgress color="primary" />
        </Box>
      );
    }

    if (!editedData || Object.keys(editedData).length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <Typography variant="body1">No data available</Typography>
        </Box>
      );
    }

    return (
      <Fade in={true} timeout={500}>
        <Box>
          <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
            <InfoIcon sx={{ mr: 1, color: "info.main" }} />
            <Typography variant="body2" color="info.main">
              Make changes and click Save to update the data, if you make a change and come back to a tab from another tab, the changes will be lost.
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {Object.entries(editedData).map(([key, value]) => {
              if (key !== "geographyPoint") {
                return (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "space-between",
                        transition: "all 0.3s",
                        borderRadius: 2,
                        border: "1px solid",
                        borderColor: "divider",
                        "&:hover": {
                          boxShadow: theme.shadows[4],
                          borderColor: "primary.main",
                        },
                      }}
                    >
                      {/* {renderInputField(key, value)} */}
                      {renderFormInputField(key, value)}
                    </Paper>
                  </Grid>
                );
              }
              return null;
            })}
          </Grid>
        </Box>
      </Fade>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        // bgcolor: "background.paper",
        borderRadius: 2,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        height: "100%",
        // boxShadow: theme.shadows[4],
      }}
    >
      <Box sx={{ height: "100%" }}>
        <Stepper
          activeStep={currentTab}
          alternativeLabel
          nonLinear
          sx={{
            p: 2,
            "& .MuiStepLabel-root .Mui-completed": {
              color: "success.main",
            },
            "& .MuiStepLabel-label": {
              mt: 1,
              fontSize: "0.75rem",
            },
          }}
        >
          {AllowedTables.map((table, index) => (
            <Step key={table}>
              <StepButton onClick={() => handleStepClick(index)}>{formatTableName(table)}</StepButton>
            </Step>
          ))}
        </Stepper>
      </Box>
      <Box sx={{ p: 3, flexGrow: 1, overflowY: "auto", maxHeight: "50vh" }}>{renderTabContent()}</Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
        }}
      >
        <Button onClick={handleBack} disabled={currentTab === 0} variant="outlined" sx={{ borderRadius: 20 }}>
          Back
        </Button>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {showSaveMessage && (
            <Typography variant="body2" color="success.main" sx={{ mr: 2 }}>
              Changes saved successfully!
            </Typography>
          )}
          <Button onClick={handleSave} color="success" variant="contained" startIcon={<SaveIcon />} sx={{ borderRadius: 20, mr: 2 }}>
            Save
          </Button>
          <Button onClick={handleNext} disabled={currentTab === AllowedTables.length - 1} variant="contained" sx={{ borderRadius: 20 }}>
            {currentTab === AllowedTables.length - 1 ? "Finish" : "Next"}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default AddStoreTabs;
