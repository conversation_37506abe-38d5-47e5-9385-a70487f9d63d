import {
  Box,
  Button,
  CircularProgress,
  Fade,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Step,
  StepButton,
  Stepper,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentTab, setTabData } from "../../../../__redux/storeManagerSlice";
import { fetchTableData } from "../../../api/storeLogisticManagerApi";
import { RootState } from "../../../store";
import SaveIcon from "@mui/icons-material/Save";
import InfoIcon from "@mui/icons-material/Info";
import { ROLES } from "../../../constants/constants";
import { formatDateToString, addSpaceToCamelCase } from "../../ai/components/utils";
import _ from "lodash";
import { isStorePageAdmin, isStorePageReadOnly } from "../../../utils/roleUtils";

const allowedTables = [
  "str_store",
  "str_site",
  "str_storeConcept",
  "str_district",
  "str_market",
  "str_regionDistrict",
  "str_region",
  "district_employee",
  "region_employee",
  "str_geography",
];

const allowedTablesUIName: Record<string, string> = {
  str_store: "Store",
  str_site: "Site",
  str_storeConcept: "Store Concept",
  str_district: "District",
  str_market: "Market",
  str_regionDistrict: "Region District",
  str_region: "Region",
  district_employee: "District Manager",
  region_employee: "Region Manger",
  str_geography: "Geography",
};

export const StoreUIFieldName: Record<string, string> = {
  validTo: "Valid Until",
};

export const convertAllDateToString = (data: TabData, dateFormat: string, timezone?: string): TabData => {
  const tempData = { ...data };
  const keys = Object.keys(tempData);
  for (const key of keys) {
    if (["openDate", "closedDate", "compareDate", "upcomingCompDate", "createDate", "validFrom", "validTo", "hireDate", "termDate"].includes(key))
      if (tempData[key]) {
        tempData[key] = timezone ? formatDateToString(tempData[key], dateFormat, timezone) : formatDateToString(tempData[key], dateFormat);
      }
  }
  return tempData;
};

interface StoreDetailsTabsProps {
  storeId: number;
  isSaving: boolean;
  closeBtnCallback: () => void;
  saveBtnCallback: (editedTabData: Record<string, TabData>) => void;
}

interface TabData {
  [key: string]:
    | string
    | number
    | boolean
    | null
    | Date
    | {
        latitude: number;
        longitude: number;
      };
}

const formatTableName = (tableName: string): string => {
  return tableName
    .split("_")
    .map((word) => {
      if (word.toLowerCase() === "str") {
        return "STR";
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

const PRIMARY_KEYS = {
  str_geography: ["geographyId"],
  str_store: ["storeId", "siteId", "storeNumber", "districtid", "storeConceptId", "marketId", "geographyId", "legacyStoreCode", "legacyStoreId"],
  emp_employee: ["employeeId", "employeeNumber"],
  str_district: ["districtid"],
  str_market: ["marketId"],
  str_region: ["regionid"],
  str_regionDistrict: ["regionDistrictId", "regionId", "districtId"],
  str_storeConcept: ["storeConceptId"],
  str_site: ["siteId", "siteName"],
};

const StoreDetailsTabs: React.FC<StoreDetailsTabsProps> = ({ storeId, isSaving, closeBtnCallback, saveBtnCallback }) => {
  const dispatch = useDispatch();
  const { tabData, currentTab } = useSelector((state: RootState) => state.storeManager);
  const { userRole } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  const [editedData, setEditedData] = useState<TabData>({});
  const [duplicateEditedData, setDuplicateEditedData] = useState<TabData>({});
  const [dataSource, setDataSource] = useState<TabData>({});

  const [isLoading, setIsLoading] = useState(false);
  const [showSaveMessage, setShowSaveMessage] = useState(false);

  // console.log("tabData", tabData);
  // console.log("editedData", editedData);

  const fetchData = async (tableName: string) => {
    setIsLoading(true);
    try {
      const data = await fetchTableData(tableName, storeId);

      if (Array.isArray(data) && data.length > 0) {
        if (data[0].geographyPoint) {
          const { geographyPoint, ...dataWithoutGeographyPoint } = data[0];

          // Convert date to GMT
          const tempData = convertAllDateToString(dataWithoutGeographyPoint, "MM/DD/YYYY", "GMT");

          setDuplicateEditedData(tempData);
          // setEditedData(tempData);
          setDataSource(tempData);
        } else {
          // Convert date to GMT
          const tempData = convertAllDateToString(data[0], "MM/DD/YYYY", "GMT");
          setDuplicateEditedData(tempData);
          // setEditedData(tempData);
          setDataSource(tempData);
        }
      } else {
        setEditedData({});
      }
    } catch (err) {
      console.error("Error fetching table data:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    if (currentTab < allowedTables.length - 1) {
      // dispatch(setTabData({ tableName: allowedTables[currentTab], data: editedData }));
      dispatch(setCurrentTab(currentTab + 1));
    }
  };

  const handleBack = () => {
    if (currentTab > 0) {
      // dispatch(setTabData({ tableName: allowedTables[currentTab], data: editedData }));
      dispatch(setCurrentTab(currentTab - 1));
    }
  };

  const handleInputChange = (key: string, value: string | number | boolean | Date | null | { latitude: number; longitude: number } | null) => {
    if (value instanceof Date) {
      setDataSource((prev) => ({ ...prev, [key]: value.toISOString() }));
      checkForUpdatedData(key, value.toISOString());
    } else {
      setDataSource((prev) => ({ ...prev, [key]: value }));
      checkForUpdatedData(key, value);
    }
  };

  const checkForUpdatedData = (key: string, value: string | number | boolean | Date | null | { latitude: number; longitude: number }) => {
    const dict: TabData = { ...duplicateEditedData };
    if (dict[key] !== value) {
      setEditedData((prev) => ({ ...prev, [key]: value }));
    } else if (key in editedData) {
      // delete the existing key
      const temp = { ...editedData };
      delete temp[key];
      setEditedData(temp);
    }
  };

  const handleStepClick = (index: number) => {
    // dispatch(setTabData({ tableName: allowedTables[currentTab], data: editedData }));
    dispatch(setCurrentTab(index));
  };

  const handleSave = () => {
    console.log("editedData", editedData);
    const tempData = convertAllDateToString(editedData, "YYYY-MM-DD");
    const dict: Record<string, TabData> = {};
    dict[allowedTables[currentTab]] = tempData;
    saveBtnCallback(dict);
    // dispatch(setTabData({ tableName: allowedTables[currentTab], data: tempData }));

    // setShowSaveMessage(true);
    // setTimeout(() => setShowSaveMessage(false), 3000);
  };

  useEffect(() => {
    fetchData(allowedTables[currentTab]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTab, storeId]);

  useEffect(() => {
    setEditedData({});
  }, [currentTab]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderInput = (key: string, value: any) => {
    const currentTable = allowedTables[currentTab];
    // const isPrimaryKey = PRIMARY_KEYS[currentTable as keyof typeof PRIMARY_KEYS]?.includes(key) || key.toLowerCase().includes("id");
    const isPrimaryKey = PRIMARY_KEYS[currentTable as keyof typeof PRIMARY_KEYS]?.includes(key);

    // Common props for all inputs
    const commonProps = {
      label: key,
      value: value || "",
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleInputChange(key, e.target.value),
      fullWidth: true,
      disabled: isPrimaryKey,
    };

    // Handle specific fields for each table
    switch (currentTable) {
      case "str_store":
        if (["showReporting", "isClosed", "isComp"].includes(key)) {
          return (
            <FormControl fullWidth>
              <InputLabel id={`${key}-label`}>{addSpaceToCamelCase(key)}</InputLabel>
              <Select
                labelId={`${key}-label`}
                id={key}
                value={value ? "true" : "false"}
                onChange={(e) => handleInputChange(key, e.target.value === "true")}
                label={key}
                readOnly={isStorePageReadOnly(userRole)}
              >
                <MenuItem value="true">True</MenuItem>
                <MenuItem value="false">False</MenuItem>
              </Select>
            </FormControl>
          );
        } else if (["openDate", "closedDate", "compareDate", "upcomingCompDate", "createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_store" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        } else if (
          [
            "storeId",
            "companyId",
            "siteId",
            "posId",
            "loyaltyId",
            "storeConceptId",
            "dataProviderId",
            "marketId",
            "legacyStoreId",
            "geographyId",
            "districtId",
            "cohortId",
          ].includes(key)
        ) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        }
        break;

      case "str_site":
        if (["projectedOpenDate", "createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_site" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        } else if (["siteId", "squareFootage"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["latitude", "longitude"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseFloat(e.target.value) || null)}
              fullWidth
              type="number"
              // inputProps={{ step: "any" }}
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (key === "geographyPoint") {
          return null;
          // <Box>
          //   <Typography variant="subtitle1">Geography Point</Typography>
          //   <TextField
          //     value={formatValue(value)}
          //     onChange={(e) => handleInputChange(key, e.target.value)}
          //     fullWidth
          //     disabled
          //     variant="outlined"
          //     size="small"
          //     sx={{ mt: 1 }}
          //   />
          // </Box>
        }
        break;

      case "str_storeConcept":
        if (["storeConceptId"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_storeConcept" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
              />
            </LocalizationProvider>
          );
        }
        break;

      case "str_district":
      case "str_region":
        if (["employeeId", "districtid", "regionid"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_district" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        }
        break;

      case "str_market":
      case "str_geography":
        if (["marketId", "geographyId"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_market" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        }
        break;

      case "str_regionDistrict":
        if (["regionDistrictId", "regionId", "districtId"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["str_regionDistrict" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        }
        break;

      case "district_employee":
      case "region_employee":
        if (["employeeId", "companyId", "employeeNumber", "departmentId", "jobId", "managerEmployeeNumber"].includes(key)) {
          return (
            <TextField
              {...commonProps}
              disabled={PRIMARY_KEYS["emp_employee" as keyof typeof PRIMARY_KEYS]?.includes(key)}
              label={addSpaceToCamelCase(key)}
              value={value || ""}
              onChange={(e) => handleInputChange(key, parseInt(e.target.value) || null)}
              fullWidth
              type="number"
              InputProps={{
                readOnly: isStorePageReadOnly(userRole),
              }}
            />
          );
        } else if (["hireDate", "termDate", "createDate", "validFrom", "validTo"].includes(key)) {
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label={StoreUIFieldName[key] == undefined ? addSpaceToCamelCase(key) : StoreUIFieldName[key]}
                disabled={PRIMARY_KEYS["emp_employee" as keyof typeof PRIMARY_KEYS]?.includes(key) || isStorePageReadOnly(userRole)}
                value={value ? new Date(value) : null}
                onChange={(newValue: Date | null) => {
                  handleInputChange(key, newValue);
                }}
                // renderInput={(params) => (
                //   <TextField
                //     {...params}
                //     fullWidth
                //     InputProps={{
                //       ...params.InputProps,
                //       readOnly: isStorePageReadOnly(userRole),
                //     }}
                //   />
                // )}
                renderInput={(params) => (
                  <TextField
                    {...commonProps}
                    {...params}
                    fullWidth
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        } else if (["isActive"].includes(key)) {
          return (
            <FormControl fullWidth>
              <InputLabel id={`${key}-label`}>{key}</InputLabel>
              <Select
                labelId={`${key}-label`}
                id={key}
                value={value ? "true" : "false"}
                disabled={PRIMARY_KEYS["emp_employee" as keyof typeof PRIMARY_KEYS]?.includes(key)}
                onChange={(e) => handleInputChange(key, e.target.value === "true")}
                label={addSpaceToCamelCase(key)}
                readOnly={isStorePageReadOnly(userRole)}
              >
                <MenuItem value="true">True</MenuItem>
                <MenuItem value="false">False</MenuItem>
              </Select>
            </FormControl>
          );
        }
        break;
    }

    return (
      <TextField
        {...commonProps}
        label={addSpaceToCamelCase(key)}
        value={value || ""}
        onChange={(e) => handleInputChange(key, e.target.value)}
        fullWidth
        InputProps={{ readOnly: isStorePageReadOnly(userRole) }}
      />
    );
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <CircularProgress color="primary" />
        </Box>
      );
    }

    if (!dataSource || Object.keys(dataSource).length === 0) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="200px">
          <Typography variant="body1">No data available</Typography>
        </Box>
      );
    }

    return (
      <Fade in={true} timeout={500}>
        <Box>
          <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
            <InfoIcon sx={{ mr: 1, color: "info.main" }} />
            <Typography variant="body2" color="info.main">
              Make changes and click Save to update the data, if you make a change and come back to a tab from another tab, the changes will be lost.
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {Object.entries(dataSource).map(([key, value]) => {
              if (key !== "geographyPoint") {
                return (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "space-between",
                        transition: "all 0.3s",
                        borderRadius: 2,
                        border: "1px solid",
                        borderColor: "divider",
                        "&:hover": {
                          boxShadow: theme.shadows[4],
                          borderColor: "primary.main",
                        },
                      }}
                    >
                      {renderInput(key, value)}
                    </Paper>
                  </Grid>
                );
              }
              return null;
            })}
          </Grid>
        </Box>
      </Fade>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        // bgcolor: "background.paper",
        borderRadius: 2,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        height: "100%",
        // boxShadow: theme.shadows[4],
      }}
    >
      <Box sx={{ height: "100%" }}>
        <Stepper
          activeStep={currentTab}
          alternativeLabel
          nonLinear
          sx={{
            p: 2,
            "& .MuiStepLabel-root .Mui-completed": {
              color: "success.main",
            },
            "& .MuiStepLabel-label": {
              mt: 1,
              fontSize: "0.75rem",
            },
          }}
        >
          {allowedTables.map((table, index) => (
            <Step key={table}>
              <StepButton onClick={() => handleStepClick(index)}>{allowedTablesUIName[table]}</StepButton>
            </Step>
          ))}
        </Stepper>
      </Box>
      <Box sx={{ p: 3, flexGrow: 1, overflowY: "auto", maxHeight: "50vh" }}>{renderTabContent()}</Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
        }}
      >
        <Button onClick={handleBack} disabled={currentTab === 0 || isLoading} variant="outlined" sx={{ borderRadius: 20 }}>
          Back
        </Button>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button onClick={handleNext} disabled={currentTab === allowedTables.length - 1 || isLoading} variant="contained" sx={{ borderRadius: 20 }}>
            {currentTab === allowedTables.length - 1 ? "Finish" : "Next"}
          </Button>
        </Box>
      </Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
        <Button disabled={isSaving || isLoading} onClick={closeBtnCallback} sx={{ mr: 1 }}>
          Cancel
        </Button>
        {isStorePageAdmin(userRole) && (
          <Button
            disabled={isSaving || isLoading || Object.keys(editedData).length === 0}
            onClick={handleSave}
            color="success"
            variant="contained"
            startIcon={<SaveIcon />}
            sx={{ borderRadius: 20, mr: 2 }}
          >
            Save
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default StoreDetailsTabs;
