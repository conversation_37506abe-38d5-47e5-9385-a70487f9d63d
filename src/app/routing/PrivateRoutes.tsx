import { FC, lazy, Suspense } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import TopBarProgress from "react-topbar-progress-indicator";
import { getCSSVariableValue } from "../../_metronic/assets/ts/_utils";
import { WithChildren } from "../../_metronic/helpers";
import { MasterLayout } from "../../_metronic/layout/MasterLayout";
import BuilderPageWrapper from "../pages/layout-builder/BuilderPageWrapper";
import { MenuTestPage } from "../pages/MenuTestPage";
import { AuditLogWrapper } from "../pages/audit-logs/AuditLogWrapper";
import RoleProtectedRoute from "./RoleProtectedRoute";
import { ROLES } from "../constants/constants";
import { useSelector } from "react-redux";
import { RootState } from "../store";
import PipelinDashPage from "../pages/pipelineDash/PipelinDashPage";
// import { PipelineDashboardWrapper } from "../pages/pipelineDash/PipelineDash";
// import { DashboardWrapper } from "../pages/dashboard/DashboardWrapper";
// import { StoreManagerWrapper } from "../pages/store-logistic-manager/StoreManagerWrapper";
// import { FiscalCalendarWrapper } from "../pages/fiscal-calendar/FiscalCalendarWrapper";
// import StoreMemberTableWrapper from "../pages/store-logistic-manager/member-table/StoreMemberTableWrapper";
// import AvailableDatePageWrapper from "../pages/fiscal-calendar/components/view-date/AvailableDatePageWrapper";
// import { ReportWrapper } from "../pages/report/ReportWrapper";
// import { PromotionWrapper } from "../pages/promotion/PromotionWrapper";
// import UserManualWrapper from "../pages/user-manual/UserManualWrapper";
// import ValidationDataWrapper from "../pages/validation-table/ValidationDataWrapper";
// import RevelDiscountWrapper from "../pages/RevelDiscount/RevelDiscountWrapper";

const PrivateRoutes = () => {
  const ProfilePage = lazy(() => import("../modules/profile/ProfilePage"));
  const WizardsPage = lazy(() => import("../modules/wizards/WizardsPage"));
  const AccountPage = lazy(() => import("../modules/accounts/AccountPage"));
  const WidgetsPage = lazy(() => import("../modules/widgets/WidgetsPage"));
  const ChatPage = lazy(() => import("../modules/apps/chat/ChatPage"));
  const UsersPage = lazy(() => import("../modules/apps/user-management/UsersPage"));

  const DashboardWrapper = lazy(() => import("../pages/dashboard/DashboardWrapper").then(module => ({ default: module.DashboardWrapper })));
  const StoreManagerWrapper = lazy(() =>
    import("../pages/store-logistic-manager/StoreManagerWrapper").then(module => ({ default: module.StoreManagerWrapper }))
  );
  const FiscalCalendarWrapper = lazy(() =>
    import("../pages/fiscal-calendar/FiscalCalendarWrapper").then(module => ({ default: module.FiscalCalendarWrapper }))
  );
  const ReportWrapper = lazy(() => import("../pages/report/ReportWrapper").then(module => ({ default: module.ReportWrapper })));
  const PromotionWrapper = lazy(() => import("../pages/promotion/PromotionWrapper").then(module => ({ default: module.PromotionWrapper })));
  const UserManualWrapper = lazy(() => import("../pages/user-manual/UserManualWrapper"));
  const StoreMemberTableWrapper = lazy(() => import("../pages/store-logistic-manager/member-table/StoreMemberTableWrapper"));
  const AvailableDatePageWrapper = lazy(() => import("../pages/fiscal-calendar/components/view-date/AvailableDatePageWrapper"));
  const ValidationDataWrapper = lazy(() => import("../pages/validation-table/ValidationDataWrapper"));
  const RevelDiscountWrapper = lazy(() => import("../pages/RevelDiscount/RevelDiscountWrapper"));
  const RevelDiscountTableViewWrapper = lazy(() => import("../pages/RevelDiscount/table-view/RevelDiscountTableViewWrapper"));

  const auth = useSelector((state: RootState) => state.auth);

  const checkIfStorePageAllowed = (allowedRole: string[] | null) => {
    if (allowedRole === null) {
      return false;
    }

    if (
      allowedRole.includes(ROLES.STORE_LOGISTIC_ADMIN) ||
      allowedRole.includes(ROLES.STORE_LOGISTIC_READER) ||
      allowedRole.includes(ROLES.ADMIN) ||
      allowedRole.includes(ROLES.DEVELOPER) ||
      allowedRole.includes(ROLES.READER)
    ) {
      return true;
    }
    return false;
  };

  const checkIfDiscountPageAllowed = (allowedRole: string[] | null) => {
    if (allowedRole === null) {
      return false;
    }

    if (
      allowedRole.includes(ROLES.DISCOUNT_PAGE_ADMIN) ||
      allowedRole.includes(ROLES.DISCOUNT_PAGE_READER) ||
      allowedRole.includes(ROLES.ADMIN) ||
      allowedRole.includes(ROLES.DEVELOPER) ||
      allowedRole.includes(ROLES.READER)
    ) {
      return true;
    }
    return false;
  };

  return (
    <>
      <Routes>
        <Route element={<MasterLayout />}>
          {/* Redirect to Dashboard after success login/registartion */}
          {checkIfStorePageAllowed(auth?.userRole) ? (
            <Route path="auth/*" element={<Navigate to="/store-logistic-manager" />} />
          ) : checkIfDiscountPageAllowed(auth?.userRole) ? (
            <Route path="auth/*" element={<Navigate to="/revel-discount" />} />
          ) : (
            <Route path="auth/*" element={<Navigate to="/unauthorized" />} />
          )}

          {/* Store Logistic User Routes */}
          <Route
            element={
              <RoleProtectedRoute allowedRoles={[ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER, ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]} />
            }
          >
            <Route
              path="store-logistic-manager"
              element={
                <SuspensedView>
                  <StoreManagerWrapper tablename="Store Logistic Manager" />
                </SuspensedView>
              }
            />
            <Route
              path="store-member-table"
              element={
                <SuspensedView>
                  <StoreMemberTableWrapper />
                </SuspensedView>
              }
            />
          </Route>

          {/* Discount Page User Routes */}
          <Route
            element={<RoleProtectedRoute allowedRoles={[ROLES.DISCOUNT_PAGE_ADMIN, ROLES.DISCOUNT_PAGE_READER, ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]} />}
          >
            <Route
              path="revel-discount"
              element={
                <SuspensedView>
                  <RevelDiscountWrapper />
                </SuspensedView>
              }
            />
            <Route
              path="revel-discount-table"
              element={
                <SuspensedView>
                  <RevelDiscountTableViewWrapper />
                </SuspensedView>
              }
            />
          </Route>

          {/* All Users Routes */}
          <Route element={<RoleProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]} />}>
            <Route
              path="pipeline-dash"
              element={
                <SuspensedView>
                  <PipelinDashPage />
                </SuspensedView>
              }
            />

            <Route
              path="dashboard"
              element={
                <SuspensedView>
                  <DashboardWrapper />
                </SuspensedView>
              }
            />
            <Route
              path="fiscal-calendar"
              element={
                <SuspensedView>
                  <FiscalCalendarWrapper tablename="Fiscal Calendar" />
                </SuspensedView>
              }
            />
            <Route
              path="report"
              element={
                <SuspensedView>
                  <ReportWrapper />
                </SuspensedView>
              }
            />
            <Route
              path="promotion"
              element={
                <SuspensedView>
                  <PromotionWrapper tablename="Promotion" />
                </SuspensedView>
              }
            />
            <Route
              path="audit-logs"
              element={
                <SuspensedView>
                  <AuditLogWrapper tablename="AuditLogs" />
                </SuspensedView>
              }
            />
            <Route
              path="user-manual"
              element={
                <SuspensedView>
                  <UserManualWrapper />
                </SuspensedView>
              }
            />

            <Route
              path="available-date"
              element={
                <SuspensedView>
                  <AvailableDatePageWrapper />
                </SuspensedView>
              }
            />
            <Route
              path="data-validation"
              element={
                <SuspensedView>
                  <ValidationDataWrapper />
                </SuspensedView>
              }
            />
          </Route>

          {/* Pages */}
          {/* <Route path="dashboard" element={<DashboardWrapper />} /> */}

          {/* <Route path="ai" element={<AiWrapper />} /> */}

          {/* Admin Routes */}
          {/* <Route element={<RoleProtectedRoute requiredRole={Role.Admin} />}>
            <Route path="overview" element={<OverviewPageWrapper />} />
          </Route> */}
          {/* <Route element={<RoleProtectedRoute requiredRole={Role.Admin} />}>
            <Route path="maintenance" element={<MaintenancePageWrapper />} />
          </Route> */}

          <Route path="builder" element={<BuilderPageWrapper />} />
          <Route path="menu-test" element={<MenuTestPage />} />
          {/* Lazy Modules */}
          <Route
            path="crafted/pages/profile/*"
            element={
              <SuspensedView>
                <ProfilePage />
              </SuspensedView>
            }
          />
          <Route
            path="crafted/pages/wizards/*"
            element={
              <SuspensedView>
                <WizardsPage />
              </SuspensedView>
            }
          />
          <Route
            path="crafted/widgets/*"
            element={
              <SuspensedView>
                <WidgetsPage />
              </SuspensedView>
            }
          />
          <Route
            path="crafted/account/*"
            element={
              <SuspensedView>
                <AccountPage />
              </SuspensedView>
            }
          />
          <Route
            path="apps/chat/*"
            element={
              <SuspensedView>
                <ChatPage />
              </SuspensedView>
            }
          />
          <Route
            path="apps/user-management/*"
            element={
              <SuspensedView>
                <UsersPage />
              </SuspensedView>
            }
          />
          {/* Page Not Found */}
          <Route path="*" element={<Navigate to="/error/404" />} />
          {/* Unauthorized Page */}
          <Route path="unauthorized" element={<Navigate to="/error/401" />} />
        </Route>
      </Routes>
    </>
  );
};

const SuspensedView: FC<WithChildren> = ({ children }) => {
  const baseColor = getCSSVariableValue("--bs-primary");
  TopBarProgress.config({
    barColors: {
      "0": baseColor,
    },
    barThickness: 1,
    shadowBlur: 5,
  });
  return <Suspense fallback={<TopBarProgress />}>{children}</Suspense>;
};

export { PrivateRoutes };
