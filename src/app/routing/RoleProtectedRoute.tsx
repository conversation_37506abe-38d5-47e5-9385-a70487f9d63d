import { useSelector } from "react-redux";
import { Navigate, Outlet } from "react-router-dom";
import { RootState } from "../store"; // Adjust the import path as necessary

interface RoleProtectedRouteProps {
  allowedRoles: string[];
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({ allowedRoles }) => {
  const { userRole } = useSelector((state: RootState) => state.auth);

  if (!userRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  if (allowedRoles.some((role) => userRole.includes(role))) {
    return <Outlet />;
  }

  return <Navigate to="/unauthorized" replace />;
};

export default RoleProtectedRoute;
