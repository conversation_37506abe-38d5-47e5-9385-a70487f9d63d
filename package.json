{"name": "demo1", "private": true, "version": "8.2.4", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@ant-design/charts": "^2.2.7", "@ant-design/icons": "^6.0.0", "@azure/msal-browser": "^3.13.0", "@azure/msal-react": "^2.0.15", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/roboto": "^5.0.13", "@formatjs/intl-pluralrules": "5.2.4", "@formatjs/intl-relativetimeformat": "11.2.4", "@fortawesome/fontawesome-free": "6.1.1", "@mui/icons-material": "^5.15.16", "@mui/joy": "^5.0.0-beta.36", "@mui/material": "^5.15.16", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/styles": "^6.1.1", "@mui/x-date-pickers": "^5.0.20", "@popperjs/core": "^2.11.6", "@reduxjs/toolkit": "^2.2.3", "@types/date-fns": "^2.6.0", "@types/react-syntax-highlighter": "^15.5.13", "animate.css": "4.1.1", "antd": "^5.24.8", "apexcharts": "3.35.0", "axios": "^1.6.5", "bootstrap": "5.3.3", "bootstrap-icons": "^1.5.0", "buffer": "^6.0.3", "chart.js": "4.3.0", "clsx": "2.1.0", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "formik": "2.2.9", "leaflet": "^1.9.4", "line-awesome": "1.3.0", "lodash": "^4.17.21", "lucide-react": "^0.445.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "nouislider": "15.5.1", "prism-react-renderer": "2.0.5", "prism-themes": "1.9.0", "prismjs": "1.28.0", "qs": "6.10.3", "react": "^18.3.1", "react-apexcharts": "1.4.0", "react-bootstrap": "2.5.0-beta.1", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "5.1.0", "react-dom": "^18.3.1", "react-flatpickr": "^3.10.13", "react-inlinesvg": "4.1.0", "react-intl": "^6.4.4", "react-leaflet": "^4.2.1", "react-query": "3.38.0", "react-redux": "^9.1.2", "react-router-dom": "6.3.0", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.6.1", "react-table": "^7.7.0", "react-topbar-progress-indicator": "4.1.1", "react-window": "^1.8.10", "recharts": "^2.12.6", "redux-persist": "^6.0.0", "socicon": "3.0.5", "styled-components": "^6.1.9", "use-debounce": "^10.0.0", "yup": "^1.0.0"}, "devDependencies": {"@types/bootstrap": "5.1.10", "@types/chart.js": "2.9.37", "@types/jest": "29.5.2", "@types/leaflet": "^1.9.12", "@types/lodash": "^4.17.7", "@types/node": "20.3.1", "@types/prismjs": "1.26.0", "@types/qs": "6.9.7", "@types/react": "18.2.64", "@types/react-copy-to-clipboard": "5.0.2", "@types/react-dom": "^18.2.7", "@types/react-redux": "^7.1.33", "@types/react-table": "^7.7.9", "@types/react-virtualized": "^9.21.30", "@types/react-window": "^1.8.8", "@types/sass-loader": "8.0.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "css-loader": "6.7.1", "del": "7.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "mini-css-extract-plugin": "2.6.1", "remove-files-webpack-plugin": "^1.5.0", "rtlcss-webpack-plugin": "4.0.7", "sass": "1.55.0", "sass-loader": "13.2.0", "typescript": "^5.3.3", "vite": "^4.4.5", "vite-plugin-compression": "^0.5.1", "webpack": "5.74.0", "webpack-cli": "5.1.4", "webpack-rtl-plugin": "^2.0.0"}}